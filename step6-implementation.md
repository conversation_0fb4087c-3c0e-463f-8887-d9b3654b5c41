# Step 6: Store Migration Implementation

## File 1: src/store/enhancedDownloadStore.ts

```typescript
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { Download as DomainDownload, DownloadStatus } from '../domain/models';
import { Download as LegacyDownload } from './downloadStore';
import { DownloadAdapter } from '../adapters/DownloadAdapter';
import { getFeatureFlags } from '../adapters/FeatureFlags';
import { downloadApplicationService } from '../application/services/ApplicationServiceFactory';
import { getDefaultDownloadsPath } from '../utils/folderUtils';

// Enhanced store state that can work with both legacy and domain entities
interface EnhancedDownloadState {
  downloads: LegacyDownload[]; // Keep legacy interface for UI compatibility
  defaultDownloadPath: string;
  isInitialized: boolean;
  loading: boolean;
  error: string | null;
  
  // Migration-specific state
  migrationStatus: 'not_started' | 'in_progress' | 'completed' | 'failed';
  migrationError: string | null;
  useDomainLayer: boolean;
}

interface EnhancedDownloadActions {
  // Original legacy methods (preserved for compatibility)
  addDownload: (url: string, filename: string, quality: string, downloadPath: string) => void;
  updateDownload: (id: string, updates: Partial<LegacyDownload>) => void;
  removeDownload: (id: string) => void;
  clearCompleted: () => void;
  setDefaultDownloadPath: (path: string) => void;
  initializeDefaultPath: () => Promise<void>;
  startRealDownload: (download: LegacyDownload) => Promise<void>;
  
  // New domain-driven methods
  createDownloadWithDomain: (url: string, filename: string, quality: string, downloadPath: string) => Promise<void>;
  pauseDownloadWithDomain: (id: string) => Promise<void>;
  resumeDownloadWithDomain: (id: string) => Promise<void>;
  cancelDownloadWithDomain: (id: string) => Promise<void>;
  
  // Migration methods
  startMigration: () => Promise<void>;
  rollbackMigration: () => void;
  clearMigrationError: () => void;
  refreshFromDomain: () => Promise<void>;
  
  // Utility methods
  clearError: () => void;
}

type EnhancedDownloadStore = EnhancedDownloadState & EnhancedDownloadActions;

export const useEnhancedDownloadStore = create<EnhancedDownloadStore>()(
  subscribeWithSelector((set, get) => ({
    // State
    downloads: [],
    defaultDownloadPath: 'Downloads',
    isInitialized: false,
    loading: false,
    error: null,
    migrationStatus: 'not_started',
    migrationError: null,
    useDomainLayer: false,

    // Initialize and check feature flags
    initializeDefaultPath: async () => {
      const featureFlags = getFeatureFlags();
      
      try {
        console.log('Initializing enhanced download store...');
        const defaultPath = await getDefaultDownloadsPath();
        
        set({
          defaultDownloadPath: defaultPath,
          useDomainLayer: featureFlags.useDomainLayer,
          isInitialized: true,
        });

        // If domain layer is enabled, try to load from domain repository
        if (featureFlags.useDomainLayer) {
          await get().refreshFromDomain();
        }
        
      } catch (error) {
        console.error('Failed to initialize enhanced download store:', error);
        set({
          error: error instanceof Error ? error.message : 'Initialization failed',
          defaultDownloadPath: 'Downloads',
          isInitialized: true,
        });
      }
    },

    // Legacy method - preserved for backward compatibility
    addDownload: (url: string, filename: string, quality: string, downloadPath: string) => {
      const featureFlags = getFeatureFlags();
      
      if (featureFlags.useDomainLayer) {
        // Use domain layer
        get().createDownloadWithDomain(url, filename, quality, downloadPath);
      } else {
        // Use legacy implementation
        const newDownload: LegacyDownload = {
          id: `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          url,
          filename,
          quality,
          downloadPath,
          status: 'pending',
          progress: 0,
          createdAt: new Date(),
        };

        set((state) => ({
          downloads: [newDownload, ...state.downloads],
        }));

        // Start download using legacy method
        Promise.resolve().then(() => {
          get().startRealDownload(newDownload);
        });
      }
    },

    // New domain-driven methods
    createDownloadWithDomain: async (url: string, filename: string, quality: string, downloadPath: string) => {
      set({ loading: true, error: null });

      try {
        await downloadApplicationService.createDownload(url, filename, quality, downloadPath);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to create download',
          loading: false
        });
      }
    },

    pauseDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.pauseDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to pause download'
        });
      }
    },

    resumeDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.resumeDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to resume download'
        });
      }
    },

    cancelDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.cancelDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to cancel download'
        });
      }
    },

    // Migration methods
    startMigration: async () => {
      set({ migrationStatus: 'in_progress', migrationError: null });
      
      try {
        const currentDownloads = get().downloads;
        console.log(`Starting migration of ${currentDownloads.length} downloads...`);
        
        // Convert legacy downloads to domain entities and save them
        for (const legacyDownload of currentDownloads) {
          try {
            const domainDownload = DownloadAdapter.toDomain(legacyDownload);
            await downloadApplicationService.repository.save(domainDownload);
          } catch (error) {
            console.warn(`Failed to migrate download ${legacyDownload.id}:`, error);
          }
        }
        
        // Enable domain layer
        set({ 
          migrationStatus: 'completed',
          useDomainLayer: true 
        });
        
        // Refresh from domain
        await get().refreshFromDomain();
        
        console.log('Migration completed successfully');
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Migration failed';
        set({
          migrationStatus: 'failed',
          migrationError: errorMessage
        });
        console.error('Migration failed:', error);
      }
    },

    rollbackMigration: () => {
      set({
        migrationStatus: 'not_started',
        migrationError: null,
        useDomainLayer: false
      });
      
      // Clear domain repository storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('flowdownload_downloads_v2');
      }
      
      console.log('Migration rolled back');
    },

    refreshFromDomain: async () => {
      if (!get().useDomainLayer) return;

      set({ loading: true });
      try {
        const domainDownloads = await downloadApplicationService.getAllDownloads();
        const legacyDownloads = domainDownloads.map(DownloadAdapter.toLegacy);
        
        set({ 
          downloads: legacyDownloads,
          loading: false 
        });
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to refresh from domain',
          loading: false
        });
      }
    },

    // Legacy methods - updated to work with domain layer when enabled
    updateDownload: (id: string, updates: Partial<LegacyDownload>) => {
      if (get().useDomainLayer) {
        console.warn('updateDownload called on domain layer - use domain methods instead');
        return;
      }

      set((state) => ({
        downloads: state.downloads.map((download) =>
          download.id === id ? { ...download, ...updates } : download
        ),
      }));
    },

    removeDownload: (id: string) => {
      if (get().useDomainLayer) {
        get().cancelDownloadWithDomain(id);
        return;
      }

      // Legacy cleanup
      if (window.downloadCleanupMap?.has(id)) {
        const cleanup = window.downloadCleanupMap.get(id);
        try {
          cleanup?.();
        } catch (error) {
          console.warn('Error during cleanup:', error);
        }
        window.downloadCleanupMap.delete(id);
      }

      set((state) => ({
        downloads: state.downloads.filter((download) => download.id !== id),
      }));
    },

    clearCompleted: () => {
      if (get().useDomainLayer) {
        downloadApplicationService.clearCompletedDownloads()
          .then(() => get().refreshFromDomain())
          .catch(error => {
            set({
              error: error instanceof Error ? error.message : 'Failed to clear completed downloads'
            });
          });
        return;
      }

      // Legacy implementation
      set((state) => {
        const completedDownloads = state.downloads.filter(
          (download) => download.status === 'completed'
        );
        completedDownloads.forEach((download) => {
          if (window.downloadCleanupMap?.has(download.id)) {
            const cleanup = window.downloadCleanupMap.get(download.id);
            try {
              cleanup?.();
            } catch (error) {
              console.warn('Error during cleanup:', error);
            }
            window.downloadCleanupMap.delete(download.id);
          }
        });

        return {
          downloads: state.downloads.filter(
            (download) => download.status !== 'completed'
          ),
        };
      });
    },

    setDefaultDownloadPath: (path: string) => {
      set({ defaultDownloadPath: path });
    },

    // Keep legacy startRealDownload for backward compatibility
    startRealDownload: async (download: LegacyDownload) => {
      if (get().useDomainLayer) {
        console.warn('startRealDownload called on domain layer - use createDownloadWithDomain instead');
        return;
      }

      // Legacy implementation - import from original store if needed
      const { useDownloadStore } = await import('./downloadStore');
      const legacyStore = useDownloadStore.getState();
      return legacyStore.startRealDownload(download);
    },

    clearError: () => {
      set({ error: null, migrationError: null });
    },

    clearMigrationError: () => {
      set({ migrationError: null });
    },
  }))
);

// Backward compatibility hook - can be used as drop-in replacement
export const useDownloadStore = useEnhancedDownloadStore;
```

## File 2: src/components/migration/MigrationControl.tsx

```typescript
import React from 'react';
import { useEnhancedDownloadStore } from '../../store/enhancedDownloadStore';
import { getFeatureFlags, setFeatureFlag } from '../../adapters/FeatureFlags';

export const MigrationControl: React.FC = () => {
  const {
    downloads,
    migrationStatus,
    migrationError,
    useDomainLayer,
    startMigration,
    rollbackMigration,
    clearMigrationError,
    refreshFromDomain
  } = useEnhancedDownloadStore();

  const featureFlags = getFeatureFlags();

  const handleEnableDomainLayer = () => {
    setFeatureFlag('useDomainLayer', true);
    window.location.reload();
  };

  const handleDisableDomainLayer = () => {
    setFeatureFlag('useDomainLayer', false);
    rollbackMigration();
    window.location.reload();
  };

  const handleStartMigration = async () => {
    await startMigration();
  };

  const getMigrationStatusColor = () => {
    switch (migrationStatus) {
      case 'completed': return 'green';
      case 'failed': return 'red';
      case 'in_progress': return 'yellow';
      default: return 'gray';
    }
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Migration Control Panel</h3>
      
      {/* Current Status */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Domain Layer:</span>
            <span className={`ml-2 px-2 py-1 rounded ${useDomainLayer ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              {useDomainLayer ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          <div>
            <span className="font-medium">Migration Status:</span>
            <span className={`ml-2 px-2 py-1 rounded bg-${getMigrationStatusColor()}-100 text-${getMigrationStatusColor()}-800`}>
              {migrationStatus.replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {migrationError && (
        <div className="mb-4 p-3 bg-red-100 border border-red-200 rounded">
          <div className="flex justify-between items-start">
            <div>
              <h4 className="font-medium text-red-800">Migration Error</h4>
              <p className="text-red-600 text-sm mt-1">{migrationError}</p>
            </div>
            <button
              onClick={clearMigrationError}
              className="text-red-600 hover:text-red-800 text-sm underline"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Migration Actions */}
      <div className="space-y-3">
        {!useDomainLayer && (
          <div>
            <button
              onClick={handleEnableDomainLayer}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Enable Domain Layer
            </button>
            <p className="text-sm text-gray-600 mt-1">
              Enable the new domain-driven architecture. This will reload the page.
            </p>
          </div>
        )}

        {useDomainLayer && migrationStatus === 'not_started' && (
          <div>
            <button
              onClick={handleStartMigration}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              disabled={downloads.length === 0}
            >
              Start Migration ({downloads.length} downloads)
            </button>
            <p className="text-sm text-gray-600 mt-1">
              Migrate existing downloads to the new domain layer.
            </p>
          </div>
        )}

        {useDomainLayer && migrationStatus === 'completed' && (
          <div>
            <button
              onClick={refreshFromDomain}
              className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-2"
            >
              Refresh from Domain
            </button>
            <button
              onClick={handleDisableDomainLayer}
              className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Disable Domain Layer
            </button>
            <p className="text-sm text-gray-600 mt-1">
              Migration completed successfully. You can now use domain-driven features.
            </p>
          </div>
        )}

        {migrationStatus === 'failed' && (
          <div>
            <button
              onClick={handleStartMigration}
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 mr-2"
            >
              Retry Migration
            </button>
            <button
              onClick={rollbackMigration}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Rollback Migration
            </button>
            <p className="text-sm text-gray-600 mt-1">
              Migration failed. You can retry or rollback to the previous state.
            </p>
          </div>
        )}
      </div>

      {/* Development Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
          <h4 className="font-medium text-blue-800">Development Info</h4>
          <div className="text-sm text-blue-600 mt-1">
            <p>Downloads count: {downloads.length}</p>
            <p>Feature flags: {JSON.stringify(featureFlags)}</p>
            <p>Using domain layer: {useDomainLayer.toString()}</p>
          </div>
        </div>
      )}
    </div>
  );
};
```

## File 3: Update src/App.tsx

Add the migration control to your main app (for development):

```typescript
// Add these imports to your existing App.tsx
import { MigrationControl } from './components/migration/MigrationControl';
import { MigrationErrorBoundary } from './components/migration/MigrationErrorBoundary';

// Wrap your app content with the error boundary and add migration control
function App() {
  const { isInitialized, initializeDefaultPath } = useEnhancedDownloadStore(); // Change this import
  // ... rest of your existing state ...

  useEffect(() => {
    if (!isInitialized) {
      initializeDefaultPath(); // This will now use the enhanced store
    }
  }, [isInitialized, initializeDefaultPath]);

  return (
    <MigrationErrorBoundary>
      <ThemeProvider>
        <div className="min-h-screen flex flex-col bg-gray-50 dark:bg-slate-900 text-gray-900 dark:text-gray-100">
          {/* Your existing header */}
          <ErrorBoundary>
            <Header
              onMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              onPluginManagerOpen={() => setIsPluginManagerOpen(true)}
              onSettingsOpen={() => setIsSettingsOpen(true)}
              onDiagnosticOpen={() => setIsDiagnosticOpen(true)}
            />
          </ErrorBoundary>
          
          {/* Add Migration Control in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="p-4">
              <MigrationControl />
            </div>
          )}
          
          {/* Your existing main content */}
          <div className="flex-1 flex overflow-hidden">
            {/* ... rest of your existing JSX ... */}
          </div>
          
          {/* Your existing modals and components */}
          {/* ... */}
        </div>
      </ThemeProvider>
    </MigrationErrorBoundary>
  );
}
```

## File 4: Update imports in components

Update your existing components to use the enhanced store. For example, in any component that imports `useDownloadStore`:

```typescript
// Before:
import { useDownloadStore } from '../store/downloadStore';

// After:
import { useDownloadStore } from '../store/enhancedDownloadStore';

// The interface remains the same, so your components don't need to change!
```

## 🧪 VALIDATION STEP

Run these commands to validate Step 6:

```bash
# Test everything
npm run test:domain
npm test src/__tests__/infrastructure
npm test src/__tests__/migration
npm test src/__tests__/application

# Check TypeScript compilation
npx tsc --noEmit

# Start the development server
npm run dev
```

## 🔧 Testing the Migration

1. **Start your app**: `npm run dev`

2. **Check the Migration Control Panel** - You should see it at the top of your app (in development mode)

3. **Enable Domain Layer**:
   - Click "Enable Domain Layer" button
   - App will reload with domain layer enabled

4. **Test creating downloads** - They should work exactly as before

5. **Start Migration**:
   - Click "Start Migration" to migrate existing downloads
   - Watch the status change to "completed"

6. **Verify everything works**:
   - Create new downloads
   - Pause/resume downloads
   - Clear completed downloads

7. **Test rollback**:
   - Click "Disable Domain Layer" 
   - App returns to legacy implementation

## ✅ Success Criteria

- [ ] App starts and works normally
- [ ] Migration control panel appears in development
- [ ] Can enable/disable domain layer
- [ ] Can migrate existing downloads
- [ ] All download operations work in both modes
- [ ] Can rollback to legacy implementation
- [ ] No breaking changes to existing UI components

**Once this step is complete, you have successfully implemented Domain-Driven Design in FlowDownload! 🎉**

## 🚀 Next Steps

- Test the migration thoroughly with various scenarios
- Gradually enable the domain layer for production users
- Implement the upload features using the same domain patterns
- Add the analytics and performance monitoring systems

The foundation is now solid and extensible for all future enhancements!
