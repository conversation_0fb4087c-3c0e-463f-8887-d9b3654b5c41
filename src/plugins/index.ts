// Plugin system initialization and exports

import { PluginRegistry } from './registry';
import { PluginAPI } from './types';
import { useDownloadStore } from '../store/downloadStore';

// Create the main plugin API implementation
const createPluginAPI = (): PluginAPI => ({
  download: {
    create: async (url: string, options: Record<string, unknown> = {}) => {
      const { addDownload, defaultDownloadPath } = useDownloadStore.getState();
      const filename = (options.filename as string) || 'download.file';
      const quality = (options.quality as string) || 'auto';
      const downloadPath = (options.outputPath as string) || defaultDownloadPath;
      
      addDownload(url, filename, quality, downloadPath);
      return `download_${Date.now()}`;
    },
    
    pause: async (_id: string) => {
      // Implementation would connect to download manager
      console.log(`Pausing download ${_id}`);
    },
    
    resume: async (_id: string) => {
      console.log(`Resuming download ${_id}`);
    },
    
    cancel: async (id: string) => {
      const { removeDownload } = useDownloadStore.getState();
      removeDownload(id);
    },
    
    getStatus: async (id: string) => {
      const { downloads } = useDownloadStore.getState();
      const download = downloads.find(d => d.id === id);
      
      if (!download) {
        throw new Error(`Download ${id} not found`);
      }
      
      return {
        id: download.id,
        status: download.status,
        progress: download.progress,
        speed: download.downloadSpeed ? parseFloat(download.downloadSpeed) : undefined,
        error: download.error
      };
    }
  },
  
  upload: {
    create: async (file, platform, options = {}) => {
      // Future implementation for upload functionality
      console.log(`Uploading ${file.name} to ${platform}`, options);
      return `upload_${Date.now()}`;
    },
    
    getStatus: async (id: string) => {
      // Future implementation
      return {
        id,
        status: 'pending' as const,
        progress: 0
      };
    }
  },
  
  storage: {
    get: async (key: string) => {
      return localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key)!) : null;
    },
    
    set: async (key: string, value: unknown) => {
      localStorage.setItem(key, JSON.stringify(value));
    },
    
    delete: async (key: string) => {
      localStorage.removeItem(key);
    },
    
    clear: async () => {
      localStorage.clear();
    }
  },
  
  ui: {
    showNotification: (message: string, type = 'info' as const) => {
      // Use dynamic import instead of require
      import('react-toastify').then(({ toast }) => {
        switch (type) {
          case 'success':
            toast.success(message);
            break;
          case 'warning':
            toast.warn(message);
            break;
          case 'error':
            toast.error(message);
            break;
          default:
            toast.info(message);
        }
      }).catch(error => {
        console.warn('Failed to show notification:', error);
      });
    },
    
    showModal: async (component: React.ComponentType) => {
      // Future implementation for modal system
      console.log('Showing modal:', component);
      return null;
    },
    
    addMenuItem: (item) => {
      // Future implementation for menu system
      console.log('Adding menu item:', item);
    },
    
    removeMenuItem: (id: string) => {
      console.log('Removing menu item:', id);
    }
  },
  
  system: {
    getVersion: () => '1.0.0',
    getOS: () => navigator.platform,
    openExternal: async (url: string) => {
      window.open(url, '_blank');
    },
    showInFolder: async (path: string) => {
      console.log(`Showing ${path} in folder`);
    }
  },
  
  network: {
    fetch: async (url: string, options = {}) => {
      return fetch(url, options);
    },
    isOnline: () => navigator.onLine
  }
});

// Initialize the plugin registry
export const pluginRegistry = new PluginRegistry(createPluginAPI());

// Plugin manager hook for React components
export const usePluginManager = () => {
  return {
    registry: pluginRegistry,
    
    async installFromFile(file: File) {
      try {
        const content = await file.text();
        const packageData = JSON.parse(content);
        await pluginRegistry.install(packageData);
        return true;
      } catch (error) {
        console.error('Failed to install plugin from file:', error);
        throw error;
      }
    },
    
    async installFromUrl(url: string) {
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const packageData = await response.json();
        await pluginRegistry.install(packageData);
        return true;
      } catch (error) {
        console.error('Failed to install plugin from URL:', error);
        throw error;
      }
    },
    
    getInstalledPlugins() {
      try {
        return Array.from(pluginRegistry.plugins.values());
      } catch (error) {
        console.error('Failed to get installed plugins:', error);
        return [];
      }
    },
    
    getEnabledPlugins() {
      try {
        return pluginRegistry.getEnabledPlugins();
      } catch (error) {
        console.error('Failed to get enabled plugins:', error);
        return [];
      }
    },
    
    async getPluginsForUrl(url: string) {
      try {
        return await pluginRegistry.getPluginsForUrl(url);
      } catch (error) {
        console.error('Failed to get plugins for URL:', error);
        return [];
      }
    }
  };
};

// Initialize built-in plugins
export const initializeBuiltinPlugins = async () => {
  try {
  // Enhanced YouTube plugin
  const youtubePlugin = {
    manifest: {
      name: 'enhanced-youtube',
      version: '1.0.0',
      author: 'FlowDownload Team',
      description: 'Enhanced YouTube downloader with playlist and subtitle support',
      homepage: 'https://github.com/flowdownload/plugins/youtube',
      license: 'MIT',
      keywords: ['youtube', 'video', 'playlist', 'subtitles'],
      main: 'index.js',
      permissions: [
        { type: 'network' as const, description: 'Access YouTube API', required: true }
      ],
      platforms: ['youtube' as const]
    },
    code: `
      const plugin = {
        supports: async (url) => {
          return url.includes('youtube.com') || url.includes('youtu.be');
        },
        
        extract: async (url) => {
          // Mock extraction - in real implementation this would use youtube-dl or similar
          const videoId = url.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([a-zA-Z0-9_-]+)/)?.[1];
          
          if (!videoId) {
            throw new Error('Invalid YouTube URL');
          }
          
          return {
            id: videoId,
            title: 'Sample YouTube Video',
            description: 'This is a sample video for demonstration',
            thumbnail: 'https://img.youtube.com/vi/' + videoId + '/maxresdefault.jpg',
            duration: 180,
            formats: [
              {
                id: '720p',
                quality: '720p',
                format: 'mp4',
                url: url,
                resolution: '1280x720'
              },
              {
                id: '1080p',
                quality: '1080p',
                format: 'mp4',
                url: url,
                resolution: '1920x1080'
              }
            ],
            metadata: {
              platform: 'youtube',
              uploader: 'Sample Channel'
            }
          };
        }
      };
      
      module.exports = plugin;
    `
  };

  // Generic HTTP downloader plugin
  const httpPlugin = {
    manifest: {
      name: 'http-downloader',
      version: '1.0.0',
      author: 'FlowDownload Team',
      description: 'Generic HTTP/HTTPS file downloader',
      license: 'MIT',
      keywords: ['http', 'https', 'generic', 'files'],
      main: 'index.js',
      permissions: [
        { type: 'network' as const, description: 'Download files from HTTP/HTTPS URLs', required: true }
      ],
      platforms: ['custom' as const]
    },
    code: `
      const plugin = {
        supports: async (url) => {
          return url.startsWith('http://') || url.startsWith('https://');
        },
        
        extract: async (url) => {
          // For direct file URLs, return basic info
          const filename = url.split('/').pop() || 'download.file';
          
          return {
            id: 'http_' + Date.now(),
            title: filename,
            description: 'Direct file download',
            formats: [
              {
                id: 'original',
                quality: 'original',
                format: filename.split('.').pop() || 'file',
                url: url
              }
            ],
            metadata: {
              platform: 'http',
              directUrl: true
            }
          };
        }
      };
      
      module.exports = plugin;
    `
  };

    await pluginRegistry.install(youtubePlugin);
    await pluginRegistry.install(httpPlugin);
    
    // Enable both plugins by default
    await pluginRegistry.enable('enhanced-youtube');
    await pluginRegistry.enable('http-downloader');
    
    console.log('Built-in plugins initialized successfully');
  } catch (error) {
    console.error('Failed to initialize built-in plugins:', error);
  }
};

// Export types for plugin development
export * from './types';
export { PluginRegistry } from './registry';