// Plugin Error class
export class PluginError extends Error {
  public code: string;
  public plugin: string;
  public details?: Record<string, unknown>;

  constructor(message: string, context: { code: string; plugin: string; details?: Record<string, unknown> }) {
    super(message);
    this.name = 'PluginError';
    this.code = context.code;
    this.plugin = context.plugin;
    this.details = context.details;
  }
}