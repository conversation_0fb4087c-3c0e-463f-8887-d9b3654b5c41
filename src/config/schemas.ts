// Comprehensive configuration schemas for FlowDownload
// This file defines all configurable aspects of the application

export interface QualityOption {
  id: string;
  label: string;
  value: string;
  icon: string;
  description: string;
  estimatedSize: string;
  color: string;
  resolution?: string;
  bitrate?: string;
  priority: number;
  enabled: boolean;
}

export interface FormatOption {
  id: string;
  label: string;
  value: string;
  description: string;
  mimeType: string;
  fileExtension: string;
  category: 'video' | 'audio' | 'image' | 'document' | 'archive';
  enabled: boolean;
  supportedQualities?: string[];
}

export interface ColorTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  qualityColors: Record<string, {
    light: {
      background: string;
      text: string;
    };
    dark: {
      background: string;
      text: string;
    };
  }>;
}

export interface DownloadEngineConfig {
  type: 'ytdlp' | 'native' | 'aria2' | 'custom';
  executable: {
    bundledPath: string;
    systemPaths: {
      unix: string[];
      windows: string[];
    };
    customPath?: string;
  };
  arguments: string[];
  environment: Record<string, string>;
  capabilities: string[];
}

export interface ExternalTool {
  id: string;
  name: string;
  description: string;
  engine: DownloadEngineConfig;
  supportedDomains: string[];
  supportedFormats: string[];
  priority: number;
  enabled: boolean;
}

export interface UILabels {
  // Main UI
  appTitle: string;
  addNewDownload: string;
  sourceUrl: string;
  sourceUrlPlaceholder: string;
  qualitySelection: string;
  formatSelection: string;
  customFilename: string;
  customFilenamePlaceholder: string;
  downloadLocation: string;
  startDownload: string;
  processing: string;
  advanced: string;
  browse: string;
  
  // Status messages
  downloadComplete: string;
  downloadFailed: string;
  downloadStarted: string;
  downloadPaused: string;
  downloadResumed: string;
  downloadCancelled: string;
  
  // Errors
  invalidUrl: string;
  networkError: string;
  permissionDenied: string;
  diskFull: string;
  unsupportedFormat: string;
  
  // Time formats
  secondsFormat: string;
  minutesFormat: string;
  hoursFormat: string;
}

export interface MediaEditorConfig {
  enabled: boolean;
  features: {
    crop: boolean;
    resize: boolean;
    rotate: boolean;
    filters: boolean;
    text: boolean;
    draw: boolean;
    stickers: boolean;
    frames: boolean;
  };
  exportFormats: FormatOption[];
  maxFileSize: number;
  watermark?: {
    enabled: boolean;
    text: string;
    position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    opacity: number;
  };
}

export interface TemplateConfig {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail?: string;
  settings: {
    quality: string;
    format: string;
    outputPattern: string;
    postProcessing?: {
      resize?: { width: number; height: number };
      compress?: { quality: number };
      watermark?: boolean;
    };
  };
}

export interface DynamicConfig {
  // Quality and format options
  qualityOptions: QualityOption[];
  formatOptions: FormatOption[];
  
  // UI Configuration
  ui: {
    labels: UILabels;
    theme: ColorTheme;
    animations: boolean;
    compactMode: boolean;
    showAdvancedOptions: boolean;
    defaultView: 'grid' | 'list' | 'compact';
  };
  
  // Download engines and tools
  downloadEngines: ExternalTool[];
  
  // Features
  features: {
    mediaEditor: MediaEditorConfig;
    templates: {
      enabled: boolean;
      builtIn: TemplateConfig[];
      allowCustom: boolean;
    };
    collaboration: {
      enabled: boolean;
      requireAuth: boolean;
      maxSharedItems: number;
    };
    cloudSync: {
      enabled: boolean;
      providers: string[];
      autoSync: boolean;
      syncInterval: number;
    };
  };
  
  // Performance
  performance: {
    maxConcurrentDownloads: number;
    chunkSize: number;
    retryAttempts: number;
    retryDelay: number;
    progressUpdateInterval: number;
    cacheSize: number;
    preloadCount: number;
  };
  
  // Network
  network: {
    timeout: number;
    userAgent: string;
    proxy?: {
      enabled: boolean;
      host: string;
      port: number;
      auth?: {
        username: string;
        password: string;
      };
    };
    bandwidthLimit: number; // 0 = unlimited
    ipv6: boolean;
  };
  
  // Storage
  storage: {
    defaultDownloadPath: string;
    organizationRules: {
      enabled: boolean;
      byType: boolean;
      byDate: boolean;
      bySite: boolean;
      customRules: Array<{
        pattern: string;
        destination: string;
      }>;
    };
    autoCleanup: {
      enabled: boolean;
      daysToKeep: number;
      maxStorageGb: number;
    };
  };
  
  // Security
  security: {
    allowedDomains: string[];
    blockedDomains: string[];
    maxFileSize: number;
    scanDownloads: boolean;
    requireHttps: boolean;
    sanitizeFilenames: boolean;
    validateUrls: boolean;
  };
  
  // Analytics
  analytics: {
    enabled: boolean;
    trackDownloads: boolean;
    trackErrors: boolean;
    anonymizeData: boolean;
    retentionDays: number;
  };
  
  // Developer
  developer: {
    debugMode: boolean;
    verboseLogging: boolean;
    showDevTools: boolean;
    experimentalFeatures: string[];
  };
}

// Default configuration values
export const DEFAULT_DYNAMIC_CONFIG: DynamicConfig = {
  qualityOptions: [
    {
      id: 'best',
      label: 'Best Quality Available',
      value: 'best',
      icon: '🔴',
      description: 'Automatically selects highest quality',
      estimatedSize: 'Variable',
      color: 'red',
      priority: 1,
      enabled: true
    },
    {
      id: '4k',
      label: '4K (2160p)',
      value: '2160p',
      icon: '4K',
      description: 'Ultra HD resolution',
      estimatedSize: '~3.5 GB/hour',
      color: 'purple',
      resolution: '3840x2160',
      bitrate: '45 Mbps',
      priority: 2,
      enabled: true
    },
    {
      id: '1440p',
      label: '1440p',
      value: '1440p',
      icon: 'QHD',
      description: 'Quad HD resolution',
      estimatedSize: '~1.8 GB/hour',
      color: 'indigo',
      resolution: '2560x1440',
      bitrate: '16 Mbps',
      priority: 3,
      enabled: true
    },
    {
      id: '1080p',
      label: '1080p',
      value: '1080p',
      icon: 'HD',
      description: 'Full HD resolution',
      estimatedSize: '~800 MB/hour',
      color: 'blue',
      resolution: '1920x1080',
      bitrate: '8 Mbps',
      priority: 4,
      enabled: true
    },
    {
      id: '720p',
      label: '720p',
      value: '720p',
      icon: 'HD',
      description: 'HD resolution',
      estimatedSize: '~350 MB/hour',
      color: 'teal',
      resolution: '1280x720',
      bitrate: '5 Mbps',
      priority: 5,
      enabled: true
    },
    {
      id: '480p',
      label: '480p',
      value: '480p',
      icon: 'SD',
      description: 'Standard definition',
      estimatedSize: '~150 MB/hour',
      color: 'green',
      resolution: '854x480',
      bitrate: '2.5 Mbps',
      priority: 6,
      enabled: true
    },
    {
      id: '360p',
      label: '360p',
      value: '360p',
      icon: 'SD',
      description: 'Low quality, small file size',
      estimatedSize: '~75 MB/hour',
      color: 'yellow',
      resolution: '640x360',
      bitrate: '1 Mbps',
      priority: 7,
      enabled: true
    },
    {
      id: 'audio-high',
      label: 'Audio Only (High)',
      value: 'audio-high',
      icon: '🎵',
      description: 'High quality audio (320kbps)',
      estimatedSize: '~150 MB/hour',
      color: 'pink',
      bitrate: '320 kbps',
      priority: 10,
      enabled: true
    },
    {
      id: 'audio-medium',
      label: 'Audio Only (Medium)',
      value: 'audio-medium',
      icon: '🎵',
      description: 'Good quality audio (128kbps)',
      estimatedSize: '~60 MB/hour',
      color: 'pink',
      bitrate: '128 kbps',
      priority: 11,
      enabled: true
    },
    {
      id: 'audio-low',
      label: 'Audio Only (Low)',
      value: 'audio-low',
      icon: '🎵',
      description: 'Compressed audio (64kbps)',
      estimatedSize: '~30 MB/hour',
      color: 'pink',
      bitrate: '64 kbps',
      priority: 12,
      enabled: true
    }
  ],
  
  formatOptions: [
    {
      id: 'mp4',
      label: 'MP4',
      value: 'mp4',
      description: 'Best compatibility across devices',
      mimeType: 'video/mp4',
      fileExtension: '.mp4',
      category: 'video',
      enabled: true,
      supportedQualities: ['all']
    },
    {
      id: 'mkv',
      label: 'MKV',
      value: 'mkv',
      description: 'Best for high quality and subtitles',
      mimeType: 'video/x-matroska',
      fileExtension: '.mkv',
      category: 'video',
      enabled: true,
      supportedQualities: ['all']
    },
    {
      id: 'webm',
      label: 'WebM',
      value: 'webm',
      description: 'Optimized for web viewing',
      mimeType: 'video/webm',
      fileExtension: '.webm',
      category: 'video',
      enabled: true,
      supportedQualities: ['all']
    },
    {
      id: 'mp3',
      label: 'MP3',
      value: 'mp3',
      description: 'Universal audio format',
      mimeType: 'audio/mpeg',
      fileExtension: '.mp3',
      category: 'audio',
      enabled: true,
      supportedQualities: ['audio-high', 'audio-medium', 'audio-low']
    },
    {
      id: 'm4a',
      label: 'M4A',
      value: 'm4a',
      description: 'Apple audio format, great quality',
      mimeType: 'audio/mp4',
      fileExtension: '.m4a',
      category: 'audio',
      enabled: true,
      supportedQualities: ['audio-high', 'audio-medium', 'audio-low']
    },
    {
      id: 'wav',
      label: 'WAV',
      value: 'wav',
      description: 'Uncompressed audio, best quality',
      mimeType: 'audio/wav',
      fileExtension: '.wav',
      category: 'audio',
      enabled: true,
      supportedQualities: ['audio-high']
    },
    {
      id: 'flac',
      label: 'FLAC',
      value: 'flac',
      description: 'Lossless compression, perfect quality',
      mimeType: 'audio/flac',
      fileExtension: '.flac',
      category: 'audio',
      enabled: true,
      supportedQualities: ['audio-high']
    },
    {
      id: 'mov',
      label: 'MOV',
      value: 'mov',
      description: 'Apple video format, ProRes support',
      mimeType: 'video/quicktime',
      fileExtension: '.mov',
      category: 'video',
      enabled: true,
      supportedQualities: ['all']
    },
    {
      id: 'avi',
      label: 'AVI',
      value: 'avi',
      description: 'Legacy format, wide compatibility',
      mimeType: 'video/x-msvideo',
      fileExtension: '.avi',
      category: 'video',
      enabled: true,
      supportedQualities: ['all']
    }
  ],
  
  ui: {
    labels: {
      appTitle: 'FlowDownload Desktop Pro',
      addNewDownload: 'Add New Download',
      sourceUrl: 'Source URL',
      sourceUrlPlaceholder: 'https://www.youtube.com/watch?v=example or any media URL...',
      qualitySelection: 'Quality Selection',
      formatSelection: 'Format Selection',
      customFilename: 'Custom Filename (Optional)',
      customFilenamePlaceholder: 'Enter custom filename...',
      downloadLocation: 'Download Location',
      startDownload: 'Start Download',
      processing: 'Processing...',
      advanced: 'Advanced',
      browse: 'Browse',
      downloadComplete: 'Download completed successfully!',
      downloadFailed: 'Download failed',
      downloadStarted: 'Download started',
      downloadPaused: 'Download paused',
      downloadResumed: 'Download resumed',
      downloadCancelled: 'Download cancelled',
      invalidUrl: 'Invalid URL format. Please enter a valid URL.',
      networkError: 'Network connection failed. Please check your internet connection.',
      permissionDenied: 'Permission denied. Please check folder permissions.',
      diskFull: 'Insufficient disk space. Please free up space and try again.',
      unsupportedFormat: 'Unsupported file format.',
      secondsFormat: '{0}s',
      minutesFormat: '{0}m {1}s',
      hoursFormat: '{0}h {1}m'
    },
    theme: {
      id: 'default',
      name: 'Default Theme',
      colors: {
        primary: '#3b82f6',
        secondary: '#8b5cf6',
        accent: '#10b981',
        background: '#ffffff',
        foreground: '#000000',
        muted: '#6b7280',
        error: '#ef4444',
        warning: '#f59e0b',
        success: '#10b981',
        info: '#3b82f6'
      },
      qualityColors: {
        red: {
          light: { background: '#fee2e2', text: '#dc2626' },
          dark: { background: '#7f1d1d', text: '#f87171' }
        },
        purple: {
          light: { background: '#f3e8ff', text: '#7c3aed' },
          dark: { background: '#581c87', text: '#c084fc' }
        },
        indigo: {
          light: { background: '#e0e7ff', text: '#4f46e5' },
          dark: { background: '#312e81', text: '#818cf8' }
        },
        blue: {
          light: { background: '#dbeafe', text: '#2563eb' },
          dark: { background: '#1e3a8a', text: '#60a5fa' }
        },
        teal: {
          light: { background: '#ccfbf1', text: '#0d9488' },
          dark: { background: '#134e4a', text: '#5eead4' }
        },
        green: {
          light: { background: '#d1fae5', text: '#059669' },
          dark: { background: '#064e3b', text: '#6ee7b7' }
        },
        pink: {
          light: { background: '#fce7f3', text: '#db2777' },
          dark: { background: '#831843', text: '#f9a8d4' }
        },
        yellow: {
          light: { background: '#fef3c7', text: '#d97706' },
          dark: { background: '#78350f', text: '#fbbf24' }
        }
      }
    },
    animations: true,
    compactMode: false,
    showAdvancedOptions: false,
    defaultView: 'list'
  },
  
  downloadEngines: [
    {
      id: 'ytdlp',
      name: 'yt-dlp',
      description: 'YouTube and social media downloader',
      engine: {
        type: 'ytdlp',
        executable: {
          bundledPath: 'bin/yt-dlp',
          systemPaths: {
            unix: [
              '/usr/local/bin/yt-dlp',
              '/opt/homebrew/bin/yt-dlp',
              '/usr/bin/yt-dlp',
              '/opt/local/bin/yt-dlp'
            ],
            windows: [
              'C:\\Program Files\\yt-dlp\\yt-dlp.exe',
              'C:\\yt-dlp\\yt-dlp.exe',
              'C:\\Tools\\yt-dlp\\yt-dlp.exe'
            ]
          }
        },
        arguments: ['--verbose', '--newline', '--no-progress', '-f'],
        environment: {},
        capabilities: ['video', 'audio', 'playlist', 'subtitles']
      },
      supportedDomains: [
        'youtube.com', 'youtu.be', 'twitter.com', 'x.com',
        'facebook.com', 'instagram.com', 'vimeo.com', 'twitch.tv'
      ],
      supportedFormats: ['mp4', 'mkv', 'webm', 'mp3', 'aac'],
      priority: 1,
      enabled: true
    }
  ],
  
  features: {
    mediaEditor: {
      enabled: false,
      features: {
        crop: true,
        resize: true,
        rotate: true,
        filters: true,
        text: true,
        draw: false,
        stickers: false,
        frames: false
      },
      exportFormats: [],
      maxFileSize: 500 * 1024 * 1024, // 500MB
      watermark: {
        enabled: false,
        text: '',
        position: 'bottom-right',
        opacity: 0.7
      }
    },
    templates: {
      enabled: false,
      builtIn: [],
      allowCustom: true
    },
    collaboration: {
      enabled: false,
      requireAuth: true,
      maxSharedItems: 100
    },
    cloudSync: {
      enabled: false,
      providers: [],
      autoSync: false,
      syncInterval: 300 // 5 minutes
    }
  },
  
  performance: {
    maxConcurrentDownloads: 3,
    chunkSize: 1024 * 1024, // 1MB
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    progressUpdateInterval: 500, // 500ms
    cacheSize: 100 * 1024 * 1024, // 100MB
    preloadCount: 5
  },
  
  network: {
    timeout: 30000, // 30 seconds
    userAgent: 'FlowDownload/1.0 (Desktop)',
    bandwidthLimit: 0, // unlimited
    ipv6: true
  },
  
  storage: {
    defaultDownloadPath: '',
    organizationRules: {
      enabled: false,
      byType: false,
      byDate: false,
      bySite: false,
      customRules: []
    },
    autoCleanup: {
      enabled: false,
      daysToKeep: 30,
      maxStorageGb: 100
    }
  },
  
  security: {
    allowedDomains: [],
    blockedDomains: [],
    maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB
    scanDownloads: false,
    requireHttps: false,
    sanitizeFilenames: true,
    validateUrls: true
  },
  
  analytics: {
    enabled: false,
    trackDownloads: true,
    trackErrors: true,
    anonymizeData: true,
    retentionDays: 30
  },
  
  developer: {
    debugMode: false,
    verboseLogging: false,
    showDevTools: false,
    experimentalFeatures: []
  }
};

// Configuration schema for validation
export const CONFIG_SCHEMA = {
  type: 'object',
  properties: {
    qualityOptions: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'label', 'value', 'enabled'],
        properties: {
          id: { type: 'string' },
          label: { type: 'string' },
          value: { type: 'string' },
          icon: { type: 'string' },
          description: { type: 'string' },
          estimatedSize: { type: 'string' },
          color: { type: 'string' },
          resolution: { type: 'string' },
          bitrate: { type: 'string' },
          priority: { type: 'number' },
          enabled: { type: 'boolean' }
        }
      }
    },
    formatOptions: {
      type: 'array',
      items: {
        type: 'object',
        required: ['id', 'label', 'value', 'category', 'enabled'],
        properties: {
          id: { type: 'string' },
          label: { type: 'string' },
          value: { type: 'string' },
          description: { type: 'string' },
          mimeType: { type: 'string' },
          fileExtension: { type: 'string' },
          category: { 
            type: 'string',
            enum: ['video', 'audio', 'image', 'document', 'archive']
          },
          enabled: { type: 'boolean' },
          supportedQualities: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      }
    }
  }
};