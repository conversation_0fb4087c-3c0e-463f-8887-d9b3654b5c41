export interface AIProvider {
  id: string;
  name: string;
  description: string;
  tier: 'free' | 'basic' | 'premium';
  features: string[];
  limitations?: string[];
  pricing?: string;
  requiresApiKey: boolean;
  apiKeyField?: string;
}

export interface AIFeature {
  id: string;
  name: string;
  description: string;
  providers: {
    free?: string;
    basic?: string;
    premium?: string;
  };
}

export const AI_PROVIDERS: Record<string, AIProvider> = {
  // Free Local Processing
  local: {
    id: 'local',
    name: 'Local Processing',
    description: 'Free processing using FFmpeg and basic algorithms on your device',
    tier: 'free',
    features: [
      'Basic scene detection',
      'Audio level analysis',
      'Video metadata extraction',
      'Simple cut detection',
      'Format conversion'
    ],
    limitations: [
      'No transcription',
      'Basic quality only',
      'Limited accuracy'
    ],
    requiresApiKey: false
  },

  // Free/Cheap API Providers
  groq: {
    id: 'groq',
    name: 'Groq Cloud',
    description: 'Fast inference with generous free tier',
    tier: 'basic',
    features: [
      'LLM-based analysis',
      'Content summarization',
      'Topic extraction',
      'Sentiment analysis'
    ],
    pricing: 'Free: 30 req/min | Paid: $0.10/million tokens',
    requiresApiKey: true,
    apiKeyField: 'groqApiKey'
  },

  deepseek: {
    id: 'deepseek',
    name: 'DeepSeek',
    description: 'Cost-effective Chinese AI with strong performance',
    tier: 'basic',
    features: [
      'DeepSeek-V2 analysis',
      'Multilingual support',
      'Code understanding',
      'Long context (32K)'
    ],
    pricing: '$0.14/million input tokens | $0.28/million output tokens',
    requiresApiKey: true,
    apiKeyField: 'deepseekApiKey'
  },

  qwen: {
    id: 'qwen',
    name: 'Qwen (Tongyi)',
    description: 'Alibaba\'s powerful multilingual models',
    tier: 'basic',
    features: [
      'Qwen-Max analysis',
      'Vision understanding',
      'Multilingual excellence',
      'Audio processing'
    ],
    pricing: 'Free tier: 1M tokens/month | Paid: $0.20/million tokens',
    requiresApiKey: true,
    apiKeyField: 'qwenApiKey'
  },

  huggingface: {
    id: 'huggingface',
    name: 'Hugging Face',
    description: 'Open-source models with free inference API',
    tier: 'basic',
    features: [
      'Whisper transcription',
      'CLIP image analysis',
      'Sentiment analysis',
      'Object detection'
    ],
    pricing: 'Free tier available | Pro: $9/month',
    requiresApiKey: true,
    apiKeyField: 'huggingfaceApiKey'
  },

  // Premium Providers
  openai: {
    id: 'openai',
    name: 'OpenAI',
    description: 'State-of-the-art AI models',
    tier: 'premium',
    features: [
      'GPT-4 analysis',
      'Whisper transcription',
      'DALL-E thumbnails',
      'Advanced insights'
    ],
    pricing: 'Whisper: $0.006/min | GPT-4: $0.03/1K tokens',
    requiresApiKey: true,
    apiKeyField: 'openaiApiKey'
  },

  anthropic: {
    id: 'anthropic',
    name: 'Anthropic Claude',
    description: 'Advanced reasoning and analysis',
    tier: 'premium',
    features: [
      'Claude 3 analysis',
      'Long-form content understanding',
      'Advanced content strategy',
      'Detailed insights'
    ],
    pricing: 'Claude 3: $0.015/1K tokens',
    requiresApiKey: true,
    apiKeyField: 'anthropicApiKey'
  },

  google: {
    id: 'google',
    name: 'Google Cloud AI',
    description: 'Google\'s AI suite for media processing',
    tier: 'premium',
    features: [
      'Video Intelligence API',
      'Speech-to-Text',
      'Natural Language API',
      'AutoML Vision'
    ],
    pricing: 'Video: $0.10/min | Speech: $0.016/min',
    requiresApiKey: true,
    apiKeyField: 'googleCloudApiKey'
  }
};

export const AI_FEATURES: Record<string, AIFeature> = {
  transcription: {
    id: 'transcription',
    name: 'Transcription',
    description: 'Convert speech to text',
    providers: {
      free: 'none',
      basic: 'qwen', // Qwen has good audio capabilities
      premium: 'openai'
    }
  },
  sceneDetection: {
    id: 'sceneDetection',
    name: 'Scene Detection',
    description: 'Detect scene changes and key moments',
    providers: {
      free: 'local',
      basic: 'local',
      premium: 'google'
    }
  },
  contentAnalysis: {
    id: 'contentAnalysis',
    name: 'Content Analysis',
    description: 'Analyze content quality and insights',
    providers: {
      free: 'local',
      basic: 'deepseek', // DeepSeek for cost-effective analysis
      premium: 'anthropic'
    }
  },
  viralityPrediction: {
    id: 'viralityPrediction',
    name: 'Virality Prediction',
    description: 'Predict content performance',
    providers: {
      free: 'none',
      basic: 'groq',
      premium: 'openai'
    }
  },
  thumbnailGeneration: {
    id: 'thumbnailGeneration',
    name: 'Thumbnail Generation',
    description: 'Generate eye-catching thumbnails',
    providers: {
      free: 'local',
      basic: 'huggingface',
      premium: 'openai'
    }
  },
  multilingualSupport: {
    id: 'multilingualSupport',
    name: 'Multilingual Support',
    description: 'Process content in multiple languages',
    providers: {
      free: 'none',
      basic: 'qwen', // Qwen excels at multilingual
      premium: 'deepseek'
    }
  }
};

export interface AISettings {
  tier: 'free' | 'basic' | 'premium';
  providers: {
    [feature: string]: string;
  };
  apiKeys: {
    [provider: string]: string;
  };
  preferences: {
    autoAnalyze: boolean;
    saveTranscripts: boolean;
    generateSuggestions: boolean;
    privacyMode: boolean; // Use local processing only
  };
}

// Load initial settings from environment variables
const getInitialApiKeys = () => {
  const keys: Record<string, string> = {};
  
  // Check for API keys in environment variables
  if (import.meta.env.VITE_GROQ_API_KEY) {
    keys.groqApiKey = import.meta.env.VITE_GROQ_API_KEY;
  }
  if (import.meta.env.VITE_DEEPSEEK_API_KEY) {
    keys.deepseekApiKey = import.meta.env.VITE_DEEPSEEK_API_KEY;
  }
  if (import.meta.env.VITE_QWEN_API_KEY) {
    keys.qwenApiKey = import.meta.env.VITE_QWEN_API_KEY;
  }
  if (import.meta.env.VITE_HUGGINGFACE_API_KEY) {
    keys.huggingfaceApiKey = import.meta.env.VITE_HUGGINGFACE_API_KEY;
  }
  if (import.meta.env.VITE_OPENAI_API_KEY) {
    keys.openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY;
  }
  if (import.meta.env.VITE_ANTHROPIC_API_KEY) {
    keys.anthropicApiKey = import.meta.env.VITE_ANTHROPIC_API_KEY;
  }
  if (import.meta.env.VITE_GOOGLE_CLOUD_API_KEY) {
    keys.googleCloudApiKey = import.meta.env.VITE_GOOGLE_CLOUD_API_KEY;
  }
  
  return keys;
};

const defaultTier = (import.meta.env.VITE_DEFAULT_AI_TIER as 'free' | 'basic' | 'premium') || 'free';
const privacyMode = import.meta.env.VITE_AI_PRIVACY_MODE === 'true';

export const DEFAULT_AI_SETTINGS: AISettings = {
  tier: defaultTier,
  providers: {
    transcription: defaultTier === 'free' ? 'none' : (defaultTier === 'basic' ? 'qwen' : 'openai'),
    sceneDetection: 'local',
    contentAnalysis: defaultTier === 'free' ? 'local' : (defaultTier === 'basic' ? 'deepseek' : 'anthropic'),
    viralityPrediction: defaultTier === 'free' ? 'none' : (defaultTier === 'basic' ? 'groq' : 'openai'),
    thumbnailGeneration: defaultTier === 'free' ? 'local' : (defaultTier === 'basic' ? 'huggingface' : 'openai'),
    multilingualSupport: defaultTier === 'free' ? 'none' : (defaultTier === 'basic' ? 'qwen' : 'deepseek')
  },
  apiKeys: getInitialApiKeys(),
  preferences: {
    autoAnalyze: false,
    saveTranscripts: true,
    generateSuggestions: true,
    privacyMode: privacyMode
  }
};