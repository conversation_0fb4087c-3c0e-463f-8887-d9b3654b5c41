// Enhanced configuration manager with hot-reload and dynamic updates
import { DynamicConfig, DEFAULT_DYNAMIC_CONFIG } from './schemas';
import { configManager } from './configManager';
import { toast } from 'react-toastify';

type ConfigChangeListener = (config: DynamicConfig) => void;
type ConfigValidator = (config: Partial<DynamicConfig>) => { valid: boolean; errors?: string[] };

export class DynamicConfigManager {
  private config: DynamicConfig;
  private listeners: Set<ConfigChangeListener> = new Set();
  private validators: Set<ConfigValidator> = new Set();
  private configPath: string = '';
  private isInitialized: boolean = false;
  private watchInterval?: NodeJS.Timeout;

  constructor() {
    this.config = { ...DEFAULT_DYNAMIC_CONFIG };
  }

  /**
   * Initialize the dynamic configuration manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load static config first
      await configManager.initialize();
      
      // Load dynamic configuration
      await this.loadDynamicConfig();
      
      // Set up hot-reload in development
      if (import.meta.env?.DEV) {
        this.enableHotReload();
      }
      
      this.isInitialized = true;
      console.log('Dynamic configuration manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize dynamic configuration manager:', error);
      this.config = { ...DEFAULT_DYNAMIC_CONFIG };
      this.isInitialized = true;
    }
  }

  /**
   * Get the current dynamic configuration
   */
  getConfig(): DynamicConfig {
    return { ...this.config };
  }

  /**
   * Get a specific configuration value with type safety
   */
  get<K extends keyof DynamicConfig>(key: K): DynamicConfig[K] {
    return this.config[key];
  }

  /**
   * Update dynamic configuration with validation
   */
  async updateConfig(updates: Partial<DynamicConfig>, notify: boolean = true): Promise<void> {
    // Validate updates
    const validationErrors = this.validateConfig(updates);
    if (validationErrors.length > 0) {
      throw new Error(`Configuration validation failed: ${validationErrors.join(', ')}`);
    }

    // Apply updates
    this.config = this.mergeConfig(this.config, updates);
    
    // Save configuration
    await this.saveDynamicConfig();
    
    // Notify listeners
    this.notifyListeners();
    
    // Show user notification
    if (notify && typeof window !== 'undefined') {
      toast.success('Configuration updated successfully');
    }
  }

  /**
   * Reset dynamic configuration to defaults
   */
  async resetToDefaults(sections?: (keyof DynamicConfig)[]): Promise<void> {
    if (sections && sections.length > 0) {
      // Reset specific sections
      const updates: Partial<DynamicConfig> = {};
      for (const section of sections) {
        (updates as Record<string, unknown>)[section] = DEFAULT_DYNAMIC_CONFIG[section];
      }
      await this.updateConfig(updates);
    } else {
      // Reset everything
      this.config = { ...DEFAULT_DYNAMIC_CONFIG };
      await this.saveDynamicConfig();
      this.notifyListeners();
    }
  }

  /**
   * Add a configuration change listener
   */
  addListener(listener: ConfigChangeListener): () => void {
    this.listeners.add(listener);
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Add a configuration validator
   */
  addValidator(validator: ConfigValidator): () => void {
    this.validators.add(validator);
    // Return unregister function
    return () => {
      this.validators.delete(validator);
    };
  }

  /**
   * Enable or disable a quality option
   */
  async toggleQualityOption(qualityId: string, enabled: boolean): Promise<void> {
    const qualityOptions = [...this.config.qualityOptions];
    const index = qualityOptions.findIndex(q => q.id === qualityId);
    
    if (index !== -1) {
      qualityOptions[index] = { ...qualityOptions[index], enabled };
      await this.updateConfig({ qualityOptions });
    }
  }

  /**
   * Enable or disable a format option
   */
  async toggleFormatOption(formatId: string, enabled: boolean): Promise<void> {
    const formatOptions = [...this.config.formatOptions];
    const index = formatOptions.findIndex(f => f.id === formatId);
    
    if (index !== -1) {
      formatOptions[index] = { ...formatOptions[index], enabled };
      await this.updateConfig({ formatOptions });
    }
  }

  /**
   * Update UI labels for internationalization
   */
  async updateUILabels(labels: Partial<DynamicConfig['ui']['labels']>): Promise<void> {
    const ui = { ...this.config.ui };
    ui.labels = { ...ui.labels, ...labels };
    await this.updateConfig({ ui });
  }

  /**
   * Export configuration to JSON
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from JSON
   */
  async importConfig(jsonString: string): Promise<void> {
    try {
      const imported = JSON.parse(jsonString) as Partial<DynamicConfig>;
      await this.updateConfig(imported);
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error}`);
    }
  }

  /**
   * Get configuration for specific feature
   */
  getFeatureConfig<K extends keyof DynamicConfig['features']>(
    feature: K
  ): DynamicConfig['features'][K] {
    return this.config.features[feature];
  }

  /**
   * Check if a feature is enabled
   */
  isFeatureEnabled(feature: keyof DynamicConfig['features']): boolean {
    const featureConfig = this.config.features[feature];
    return featureConfig && 'enabled' in featureConfig ? featureConfig.enabled : false;
  }

  /**
   * Get active quality options (enabled only)
   */
  getActiveQualityOptions() {
    return this.config.qualityOptions
      .filter(q => q.enabled)
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * Get active format options (enabled only)
   */
  getActiveFormatOptions() {
    return this.config.formatOptions.filter(f => f.enabled);
  }

  /**
   * Get format options for a specific quality
   */
  getFormatsForQuality(qualityId: string) {
    const quality = this.config.qualityOptions.find(q => q.id === qualityId);
    if (!quality) return this.getActiveFormatOptions();

    return this.config.formatOptions.filter(f => {
      if (!f.enabled) return false;
      if (!f.supportedQualities) return true;
      return f.supportedQualities.includes('all') || 
             f.supportedQualities.includes(qualityId);
    });
  }

  /**
   * Load dynamic configuration from storage
   */
  private async loadDynamicConfig(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        // Tauri environment
        try {
          const { readTextFile, exists } = await import('@tauri-apps/plugin-fs');
          const { join, appConfigDir } = await import('@tauri-apps/api/path');
          
          const configDir = await appConfigDir();
          this.configPath = await join(configDir, 'dynamic-config.json');
          
          if (await exists(this.configPath)) {
            const configText = await readTextFile(this.configPath);
            const loadedConfig = JSON.parse(configText) as Partial<DynamicConfig>;
            
            // Validate loaded config
            const errors = this.validateConfig(loadedConfig);
            if (errors.length === 0) {
              this.config = this.mergeConfig(DEFAULT_DYNAMIC_CONFIG, loadedConfig);
              console.log('Dynamic configuration loaded from:', this.configPath);
            } else {
              console.warn('Invalid dynamic configuration, using defaults:', errors);
            }
          }
        } catch (error) {
          console.warn('Failed to load from Tauri, falling back to localStorage:', error);
          this.loadFromLocalStorage();
        }
      } else {
        // Web environment
        this.loadFromLocalStorage();
      }
    } catch (error) {
      console.warn('Failed to load dynamic configuration:', error);
    }
  }

  /**
   * Load configuration from localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const saved = localStorage.getItem('flowdownload-dynamic-config');
      if (saved) {
        const loadedConfig = JSON.parse(saved) as Partial<DynamicConfig>;
        const errors = this.validateConfig(loadedConfig);
        if (errors.length === 0) {
          this.config = this.mergeConfig(DEFAULT_DYNAMIC_CONFIG, loadedConfig);
          console.log('Dynamic configuration loaded from localStorage');
        }
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
    }
  }

  /**
   * Save dynamic configuration
   */
  private async saveDynamicConfig(): Promise<void> {
    try {
      const configJson = JSON.stringify(this.config, null, 2);
      
      if (typeof window !== 'undefined' && window.__TAURI__) {
        // Tauri environment
        try {
          const { writeTextFile, create } = await import('@tauri-apps/plugin-fs');
          const { dirname } = await import('@tauri-apps/api/path');
          
          if (this.configPath) {
            const configDir = await dirname(this.configPath);
            try {
              await create(configDir);
            } catch {
              // Directory might already exist
            }
            
            await writeTextFile(this.configPath, configJson);
            console.log('Dynamic configuration saved to:', this.configPath);
          }
        } catch (error) {
          console.warn('Failed to save to Tauri, falling back to localStorage:', error);
          this.saveToLocalStorage(configJson);
        }
      } else {
        // Web environment
        this.saveToLocalStorage(configJson);
      }
    } catch (error) {
      console.error('Failed to save dynamic configuration:', error);
    }
  }

  /**
   * Save configuration to localStorage
   */
  private saveToLocalStorage(configJson: string): void {
    try {
      localStorage.setItem('flowdownload-dynamic-config', configJson);
      console.log('Dynamic configuration saved to localStorage');
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  /**
   * Enable hot-reload for configuration changes
   */
  private enableHotReload(): void {
    // Check for changes every 2 seconds in development
    this.watchInterval = setInterval(async () => {
      try {
        if (!this.configPath) return;

        // Check file modification time
        if (typeof window !== 'undefined' && window.__TAURI__) {
          
          // TODO: Fix hot-reload with proper Tauri file stats API
          // For now, just reload config periodically in development
          await this.loadDynamicConfig();
          this.notifyListeners();
        }
      } catch (error) {
        // Ignore errors in hot-reload
      }
    }, 2000);
  }

  /**
   * Disable hot-reload
   */
  disableHotReload(): void {
    if (this.watchInterval) {
      clearInterval(this.watchInterval);
      this.watchInterval = undefined;
    }
  }

  /**
   * Validate configuration
   */
  private validateConfig(config: Partial<DynamicConfig>): string[] {
    const errors: string[] = [];

    // Run custom validators
    for (const validator of this.validators) {
      const result = validator(config);
      if (!result.valid && result.errors) {
        errors.push(...result.errors);
      }
    }

    // Basic validation
    if (config.qualityOptions) {
      for (const quality of config.qualityOptions) {
        if (!quality.id || !quality.label || !quality.value) {
          errors.push(`Invalid quality option: ${JSON.stringify(quality)}`);
        }
      }
    }

    if (config.formatOptions) {
      for (const format of config.formatOptions) {
        if (!format.id || !format.label || !format.value) {
          errors.push(`Invalid format option: ${JSON.stringify(format)}`);
        }
      }
    }

    return errors;
  }

  /**
   * Deep merge two objects
   */
  private deepMerge(base: Record<string, unknown>, updates: Record<string, unknown>): Record<string, unknown> {
    const result = { ...base };

    for (const key in updates) {
      const updateValue = updates[key];
      if (updateValue !== undefined) {
        if (
          typeof updateValue === 'object' && 
          updateValue !== null && 
          !Array.isArray(updateValue) &&
          !(updateValue instanceof Date)
        ) {
          // Recursively merge objects
          const currentValue = result[key];
          if (typeof currentValue === 'object' && currentValue !== null) {
            result[key] = this.deepMerge(
              currentValue as Record<string, unknown>,
              updateValue as Record<string, unknown>
            );
          } else {
            result[key] = updateValue;
          }
        } else {
          // Direct assignment for primitives and arrays
          result[key] = updateValue;
        }
      }
    }

    return result;
  }

  /**
   * Deep merge configuration objects
   */
  private mergeConfig(base: DynamicConfig, updates: Partial<DynamicConfig>): DynamicConfig {
    return this.deepMerge(
      base as unknown as Record<string, unknown>, 
      updates as unknown as Record<string, unknown>
    ) as unknown as DynamicConfig;
  }

  /**
   * Notify all listeners of configuration changes
   */
  private notifyListeners(): void {
    const config = this.getConfig();
    for (const listener of this.listeners) {
      try {
        listener(config);
      } catch (error) {
        console.error('Error in configuration change listener:', error);
      }
    }
  }
}

// Global dynamic configuration manager instance
export const dynamicConfigManager = new DynamicConfigManager();

// Helper functions for easy access
export const getDynamicConfig = () => dynamicConfigManager.getConfig();
export const updateDynamicConfig = (updates: Partial<DynamicConfig>) => 
  dynamicConfigManager.updateConfig(updates);
export const subscribeToConfigChanges = (listener: ConfigChangeListener) => 
  dynamicConfigManager.addListener(listener);

// Initialize on module load
if (typeof window !== 'undefined') {
  dynamicConfigManager.initialize().catch(console.error);
}