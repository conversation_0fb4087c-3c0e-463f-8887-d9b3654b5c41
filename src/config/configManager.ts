// Configuration Manager
// Handles loading, saving, and merging of application configuration

import { AppConfig, DEFAULT_CONFIG } from './constants';

export class ConfigManager {
  private config: AppConfig;
  private configPath: string = '';
  private isInitialized: boolean = false;

  constructor() {
    this.config = { ...DEFAULT_CONFIG };
  }

  /**
   * Initialize the configuration manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Set dynamic paths based on environment
      await this.setDynamicPaths();
      
      // Load configuration from file if it exists
      await this.loadConfig();
      
      this.isInitialized = true;
      console.log('Configuration manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize configuration manager:', error);
      // Use default config if initialization fails
      this.config = { ...DEFAULT_CONFIG };
      this.isInitialized = true;
    }
  }

  /**
   * Get the current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Get a specific configuration value
   */
  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key];
  }

  /**
   * Update configuration
   */
  async updateConfig(updates: Partial<AppConfig>): Promise<void> {
    this.config = this.mergeConfig(this.config, updates);
    await this.saveConfig();
  }

  /**
   * Reset configuration to defaults
   */
  async resetConfig(): Promise<void> {
    this.config = { ...DEFAULT_CONFIG };
    await this.setDynamicPaths();
    await this.saveConfig();
  }

  /**
   * Load configuration from file
   */
  private async loadConfig(): Promise<void> {
    try {
      // Check if we're in Tauri environment with proper validation
      if (typeof window !== 'undefined' && window.__TAURI__) {
        try {
          const { readTextFile, exists } = await import('@tauri-apps/plugin-fs');
          const { join, appConfigDir } = await import('@tauri-apps/api/path');
          
          // Validate that functions exist
          if (!readTextFile || !exists || !join || !appConfigDir) {
            throw new Error('Required Tauri functions are not available');
          }
          
          const configDir = await appConfigDir();
          this.configPath = await join(configDir, 'config.json');
          
          if (await exists(this.configPath)) {
            const configText = await readTextFile(this.configPath);
            const loadedConfig = JSON.parse(configText) as Partial<AppConfig>;
            this.config = this.mergeConfig(DEFAULT_CONFIG, loadedConfig);
            console.log('Configuration loaded from:', this.configPath);
          } else {
            console.log('No existing configuration found, using defaults');
          }
        } catch (tauriError) {
          console.warn('Failed to load configuration from Tauri, falling back to localStorage:', tauriError);
          this.loadFromLocalStorage();
        }
      } else {
        // Web environment - use localStorage
        this.loadFromLocalStorage();
      }
    } catch (error) {
      console.warn('Failed to load configuration, using defaults:', error);
      this.config = { ...DEFAULT_CONFIG };
    }
  }
  
  /**
   * Load configuration from localStorage
   */
  private loadFromLocalStorage(): void {
    try {
      const savedConfig = localStorage.getItem('flowdownload-config');
      if (savedConfig) {
        const loadedConfig = JSON.parse(savedConfig) as Partial<AppConfig>;
        this.config = this.mergeConfig(DEFAULT_CONFIG, loadedConfig);
        console.log('Configuration loaded from localStorage');
      }
    } catch (error) {
      console.warn('Failed to load from localStorage:', error);
    }
  }

  /**
   * Save configuration to file
   */
  private async saveConfig(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        try {
          const { writeTextFile, create } = await import('@tauri-apps/plugin-fs');
          const { dirname } = await import('@tauri-apps/api/path');
          
          // Validate that functions exist
          if (!writeTextFile || !create || !dirname) {
            throw new Error('Required Tauri functions are not available');
          }
          
          if (this.configPath) {
            // Ensure config directory exists
            const configDir = await dirname(this.configPath);
            try {
              await create(configDir);
            } catch {
              // Directory might already exist, ignore error
            }
            
            // Save configuration
            await writeTextFile(this.configPath, JSON.stringify(this.config, null, 2));
            console.log('Configuration saved to:', this.configPath);
          }
        } catch (tauriError) {
          console.warn('Failed to save configuration to Tauri, falling back to localStorage:', tauriError);
          this.saveToLocalStorage();
        }
      } else {
        // Web environment - use localStorage
        this.saveToLocalStorage();
      }
    } catch (error) {
      console.error('Failed to save configuration:', error);
    }
  }
  
  /**
   * Save configuration to localStorage
   */
  private saveToLocalStorage(): void {
    try {
      localStorage.setItem('flowdownload-config', JSON.stringify(this.config));
      console.log('Configuration saved to localStorage');
    } catch (error) {
      console.warn('Failed to save to localStorage:', error);
    }
  }

  /**
   * Set dynamic paths based on the current environment
   */
  private async setDynamicPaths(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        try {
          const { 
            downloadDir, 
            appConfigDir, 
            appLogDir, 
            tempDir,
            appDataDir 
          } = await import('@tauri-apps/api/path');
          const { join } = await import('@tauri-apps/api/path');
          
          // Validate that functions exist
          if (!downloadDir || !appConfigDir || !appLogDir || !tempDir || !appDataDir || !join) {
            throw new Error('Required Tauri path functions are not available');
          }

          // Set platform-specific paths
          this.config.paths.defaultDownloadDir = await downloadDir() || '';
          this.config.paths.configDir = await appConfigDir() || '';
          this.config.paths.logDir = await appLogDir() || '';
          this.config.paths.tempDir = await tempDir() || '';
          this.config.paths.pluginDir = await join(await appDataDir() || '', 'plugins');
        } catch (tauriError) {
          console.warn('Failed to set dynamic paths via Tauri, using fallbacks:', tauriError);
          this.setFallbackPaths();
        }
      } else {
        this.setFallbackPaths();
      }
    } catch (error) {
      console.warn('Failed to set dynamic paths:', error);
      this.setFallbackPaths();
    }
  }
  
  /**
   * Set fallback paths for web environment
   */
  private setFallbackPaths(): void {
    this.config.paths.defaultDownloadDir = 'Downloads';
    this.config.paths.configDir = '';
    this.config.paths.logDir = '';
    this.config.paths.tempDir = '';
    this.config.paths.pluginDir = '';
  }

  /**
   * Deep merge two configuration objects
   */
  private mergeConfig(base: AppConfig, updates: Partial<AppConfig>): AppConfig {
    const result = { ...base };

    for (const key in updates) {
      const updateValue = updates[key as keyof AppConfig];
      if (updateValue !== undefined) {
        if (typeof updateValue === 'object' && updateValue !== null && !Array.isArray(updateValue)) {
          // Type-safe merge for nested objects
          const currentValue = (result as Record<string, unknown>)[key];
          if (typeof currentValue === 'object' && currentValue !== null) {
            (result as Record<string, unknown>)[key] = {
              ...(currentValue as Record<string, unknown>),
              ...(updateValue as Record<string, unknown>)
            };
          } else {
            (result as Record<string, unknown>)[key] = updateValue;
          }
        } else {
          (result as Record<string, unknown>)[key] = updateValue;
        }
      }
    }

    return result;
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): { isDev: boolean; isProduction: boolean; isTauri: boolean } {
    return {
      isDev: import.meta.env?.DEV ?? false,
      isProduction: import.meta.env?.PROD ?? true,
      isTauri: typeof window !== 'undefined' && !!window.__TAURI__,
    };
  }
}

// Global configuration manager instance
export const configManager = new ConfigManager();

// Helper functions for easy access
export const getConfig = () => configManager.getConfig();
export const updateConfig = (updates: Partial<AppConfig>) => configManager.updateConfig(updates);
export const resetConfig = () => configManager.resetConfig();

// Initialize configuration on module load
if (typeof window !== 'undefined') {
  configManager.initialize().catch(console.error);
}
