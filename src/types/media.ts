export interface MediaInfo {
  file_path: string;
  file_name: string;
  file_size: number;
  duration?: number;
  width?: number;
  height?: number;
  codec?: string;
  bitrate?: number;
  fps?: number;
  media_type: 'video' | 'image' | 'audio' | 'unknown';
  thumbnail_path?: string;
}

export interface AudioInfo {
  sample_rate?: number;
  channels?: number;
  audio_codec?: string;
}

export interface MediaLibraryItem extends MediaInfo {
  id: string;
  created_at: Date;
  tags?: string[];
  favorite?: boolean;
}