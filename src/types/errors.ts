/**
 * Comprehensive error type system for FlowDownload
 * Provides structured error handling with recovery strategies
 */

export enum ErrorCode {
  // Network errors
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  NETWORK_UNAVAILABLE = 'NETWORK_UNAVAILABLE',
  INVALID_URL = 'INVALID_URL',
  DOWNLOAD_FAILED = 'DOWNLOAD_FAILED',
  
  // File system errors
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  DISK_FULL = 'DISK_FULL',
  INVALID_PATH = 'INVALID_PATH',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  
  // Plugin errors
  PLUGIN_LOAD_FAILED = 'PLUGIN_LOAD_FAILED',
  PLUGIN_EXECUTION_FAILED = 'PLUGIN_EXECUTION_FAILED',
  PLUGIN_NOT_FOUND = 'PLUGIN_NOT_FOUND',
  
  // Configuration errors
  CONFIG_INVALID = 'CONFIG_INVALID',
  CONFIG_LOAD_FAILED = 'CONFIG_LOAD_FAILED',
  
  // Application errors
  INITIALIZATION_FAILED = 'INITIALIZATION_FAILED',
  UNEXPECTED_ERROR = 'UNEXPECTED_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp: Date;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, unknown>;
}

export interface RecoveryAction {
  label: string;
  action: () => void | Promise<void>;
  primary?: boolean;
}

export interface ErrorMetadata {
  code: ErrorCode;
  severity: ErrorSeverity;
  retryable: boolean;
  userMessage: string;
  technicalMessage: string;
  recoveryActions?: RecoveryAction[];
  helpUrl?: string;
}

/**
 * Base application error class with enhanced context and recovery options
 */
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly retryable: boolean;
  public readonly userMessage: string;
  public readonly technicalMessage: string;
  public readonly context: ErrorContext;
  public readonly recoveryActions: RecoveryAction[];
  public readonly helpUrl?: string;
  public readonly originalError?: Error;

  constructor(
    metadata: ErrorMetadata,
    context: ErrorContext,
    originalError?: Error
  ) {
    super(metadata.technicalMessage);
    
    this.name = 'AppError';
    this.code = metadata.code;
    this.severity = metadata.severity;
    this.retryable = metadata.retryable;
    this.userMessage = metadata.userMessage;
    this.technicalMessage = metadata.technicalMessage;
    this.context = context;
    this.recoveryActions = metadata.recoveryActions || [];
    this.helpUrl = metadata.helpUrl;
    this.originalError = originalError;
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  /**
   * Convert error to JSON for logging/reporting
   */
  toJSON() {
    return {
      name: this.name,
      code: this.code,
      severity: this.severity,
      retryable: this.retryable,
      userMessage: this.userMessage,
      technicalMessage: this.technicalMessage,
      context: this.context,
      stack: this.stack,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack
      } : undefined
    };
  }
}

/**
 * Network-specific error class
 */
export class NetworkError extends AppError {
  constructor(
    code: ErrorCode,
    context: ErrorContext,
    originalError?: Error
  ) {
    const metadata = getErrorMetadata(code);
    super(metadata, context, originalError);
    this.name = 'NetworkError';
  }
}

/**
 * File system-specific error class
 */
export class FileSystemError extends AppError {
  constructor(
    code: ErrorCode,
    context: ErrorContext,
    originalError?: Error
  ) {
    const metadata = getErrorMetadata(code);
    super(metadata, context, originalError);
    this.name = 'FileSystemError';
  }
}

/**
 * Enhanced plugin error class
 */
export class PluginError extends AppError {
  public readonly pluginName: string;
  public readonly pluginVersion?: string;

  constructor(
    code: ErrorCode,
    pluginName: string,
    context: ErrorContext,
    pluginVersion?: string,
    originalError?: Error
  ) {
    const metadata = getErrorMetadata(code);
    super(metadata, context, originalError);
    this.name = 'PluginError';
    this.pluginName = pluginName;
    this.pluginVersion = pluginVersion;
  }
}

/**
 * Get predefined error metadata for error codes
 */
export function getErrorMetadata(code: ErrorCode): ErrorMetadata {
  const errorMap: Record<ErrorCode, ErrorMetadata> = {
    [ErrorCode.NETWORK_TIMEOUT]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: true,
      userMessage: 'The download timed out. Please check your internet connection and try again.',
      technicalMessage: 'Network request timed out',
      helpUrl: 'https://docs.flowdownload.com/troubleshooting/network'
    },
    [ErrorCode.NETWORK_UNAVAILABLE]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: true,
      userMessage: 'No internet connection available. Please check your network settings.',
      technicalMessage: 'Network is unavailable',
      helpUrl: 'https://docs.flowdownload.com/troubleshooting/network'
    },
    [ErrorCode.INVALID_URL]: {
      code,
      severity: ErrorSeverity.LOW,
      retryable: false,
      userMessage: 'The URL you entered is not valid. Please check and try again.',
      technicalMessage: 'Invalid URL format provided'
    },
    [ErrorCode.DOWNLOAD_FAILED]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: true,
      userMessage: 'The download failed. This might be due to server issues or network problems.',
      technicalMessage: 'Download operation failed'
    },
    [ErrorCode.PERMISSION_DENIED]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: false,
      userMessage: 'Permission denied. Please check folder permissions or choose a different location.',
      technicalMessage: 'Insufficient permissions for file operation',
      helpUrl: 'https://docs.flowdownload.com/troubleshooting/permissions'
    },
    [ErrorCode.DISK_FULL]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: false,
      userMessage: 'Not enough disk space. Please free up space or choose a different location.',
      technicalMessage: 'Insufficient disk space for download'
    },
    [ErrorCode.INVALID_PATH]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: false,
      userMessage: 'The download path is invalid. Please choose a different location.',
      technicalMessage: 'Invalid file system path'
    },
    [ErrorCode.FILE_NOT_FOUND]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: false,
      userMessage: 'The requested file was not found.',
      technicalMessage: 'File not found at specified path'
    },
    [ErrorCode.PLUGIN_LOAD_FAILED]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: true,
      userMessage: 'A plugin failed to load. Some features may not be available.',
      technicalMessage: 'Plugin initialization failed',
      helpUrl: 'https://docs.flowdownload.com/plugins/troubleshooting'
    },
    [ErrorCode.PLUGIN_EXECUTION_FAILED]: {
      code,
      severity: ErrorSeverity.MEDIUM,
      retryable: true,
      userMessage: 'A plugin encountered an error. Please try again or disable the plugin.',
      technicalMessage: 'Plugin execution failed'
    },
    [ErrorCode.PLUGIN_NOT_FOUND]: {
      code,
      severity: ErrorSeverity.LOW,
      retryable: false,
      userMessage: 'The requested plugin was not found.',
      technicalMessage: 'Plugin not found in registry'
    },
    [ErrorCode.CONFIG_INVALID]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: false,
      userMessage: 'Configuration is invalid. Please check your settings.',
      technicalMessage: 'Configuration validation failed'
    },
    [ErrorCode.CONFIG_LOAD_FAILED]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: true,
      userMessage: 'Failed to load configuration. Using default settings.',
      technicalMessage: 'Configuration file could not be loaded'
    },
    [ErrorCode.INITIALIZATION_FAILED]: {
      code,
      severity: ErrorSeverity.CRITICAL,
      retryable: true,
      userMessage: 'Application failed to initialize properly. Please restart the app.',
      technicalMessage: 'Application initialization failed'
    },
    [ErrorCode.UNEXPECTED_ERROR]: {
      code,
      severity: ErrorSeverity.HIGH,
      retryable: true,
      userMessage: 'An unexpected error occurred. Please try again.',
      technicalMessage: 'Unexpected error encountered'
    },
    [ErrorCode.VALIDATION_ERROR]: {
      code,
      severity: ErrorSeverity.LOW,
      retryable: false,
      userMessage: 'The information you entered is not valid. Please check and try again.',
      technicalMessage: 'Input validation failed'
    }
  };

  return errorMap[code] || errorMap[ErrorCode.UNEXPECTED_ERROR];
}

/**
 * Create an AppError from a generic Error
 */
export function createAppError(
  error: Error,
  context: ErrorContext,
  code: ErrorCode = ErrorCode.UNEXPECTED_ERROR
): AppError {
  const metadata = getErrorMetadata(code);
  return new AppError(metadata, context, error);
}

/**
 * Type guard to check if an error is an AppError
 */
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}