import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import App from './App';
import './index.css';
import './styles/theme.css';
import { initSentry } from './services/sentry';
import SentryErrorBoundary from './components/SentryErrorBoundary';

// Initialize Sentry for error tracking
initSentry();

// Debug Tauri environment at startup
console.log('🚀 Main.tsx startup - Tauri check:', {
  hasTauri: !!(window as any).__TAURI__,
  hasInternals: !!(window as any).__TAURI_INTERNALS__,
  timestamp: new Date().toISOString(),
  readyState: document.readyState,
  url: window.location.href
});

// Listen for when <PERSON><PERSON> might become available
window.addEventListener('DOMContentLoaded', () => {
  console.log('📄 DOMContentLoaded - Tauri check:', {
    hasTauri: !!(window as any).__TAURI__,
    hasInternals: !!(window as any).__TAURI_INTERNALS__
  });
});

// Also check on load
window.addEventListener('load', () => {
  console.log('✅ Window loaded - Tauri check:', {
    hasTauri: !!(window as any).__TAURI__,
    hasInternals: !!(window as any).__TAURI_INTERNALS__,
    allWindowKeys: Object.keys(window).filter(k => k.includes('TAURI'))
  });
});

// Wait for Tauri to be ready if it's not immediately available
const waitForTauri = (callback: () => void, maxAttempts = 50) => {
  let attempts = 0;
  const checkInterval = setInterval(() => {
    attempts++;
    console.log(`🔄 Checking for Tauri API (attempt ${attempts}/${maxAttempts})...`);
    
    if ((window as any).__TAURI__) {
      console.log('✅ Tauri API is now available!');
      clearInterval(checkInterval);
      callback();
    } else if (attempts >= maxAttempts) {
      console.error('❌ Tauri API failed to load after maximum attempts');
      clearInterval(checkInterval);
    }
  }, 100); // Check every 100ms
};

// Start checking for Tauri
waitForTauri(() => {
  console.log('🎉 Tauri is ready, app can now use native features');
});

// Prepare for dark mode by adding class to document early
const getInitialTheme = (): 'light' | 'dark' => {
  const savedTheme = localStorage.getItem('theme');
  
  if (savedTheme === 'light' || savedTheme === 'dark') {
    return savedTheme as 'light' | 'dark';
  }
  
  // Check user system preference
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
};

// Safely add theme class to document
if (document.documentElement) {
  document.documentElement.classList.add(getInitialTheme());
}

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Root element not found. Make sure there is an element with id="root" in your HTML.');
}

ReactDOM.createRoot(rootElement).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
); 