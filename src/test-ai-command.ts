// Test AI commands directly
import { invoke } from '@tauri-apps/api/core';

export async function testAICommands() {
  console.log('🧪 Testing AI Commands...');
  
  // Test file path - update this to a real file
  const testFile = '/Users/<USER>/Downloads/sample-video.mp4';
  
  try {
    // Test 1: Check if command exists
    console.log('\n1️⃣ Testing get_media_metadata command...');
    const metadata = await invoke('get_media_metadata', { file_path: testFile });
    console.log('✅ Metadata result:', metadata);
  } catch (error) {
    console.error('❌ get_media_metadata failed:', error);
  }
  
  try {
    // Test 2: Local analysis
    console.log('\n2️⃣ Testing analyze_content_local command...');
    const analysis = await invoke('analyze_content_local', { file_path: testFile });
    console.log('✅ Local analysis result:', analysis);
  } catch (error) {
    console.error('❌ analyze_content_local failed:', error);
  }
  
  try {
    // Test 3: Scene detection
    console.log('\n3️⃣ Testing detect_scenes_local command...');
    const scenes = await invoke('detect_scenes_local', { file_path: testFile });
    console.log('✅ Scene detection result:', scenes);
  } catch (error) {
    console.error('❌ detect_scenes_local failed:', error);
  }
  
  // Test with the old analyze_content command
  try {
    console.log('\n4️⃣ Testing old analyze_content command...');
    const oldAnalysis = await invoke('analyze_content', { 
      filePath: testFile,
      options: {
        enableTranscription: false,
        enableSceneDetection: true
      }
    });
    console.log('✅ Old analysis result:', oldAnalysis);
  } catch (error) {
    console.error('❌ Old analyze_content failed:', error);
  }
}

// Export for use in console
(window as any).testAICommands = testAICommands;