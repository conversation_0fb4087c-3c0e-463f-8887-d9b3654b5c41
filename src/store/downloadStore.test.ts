import { describe, it, expect, beforeEach, vi } from 'vitest';
import { act, renderHook } from '@testing-library/react';
import { useDownloadStore } from '../store/downloadStore';
import { mockTauriCommand } from '../test/utils';

// Mock the folderUtils module
vi.mock('../utils/folderUtils', () => ({
  getDefaultDownloadsPath: vi.fn().mockResolvedValue('/Users/<USER>/Downloads'),
  ensureDirectoryExists: vi.fn().mockResolvedValue(true),
  testDirectoryAccess: vi.fn().mockResolvedValue(true),
  isInTauri: vi.fn().mockReturnValue(true),
}));

// Mock the config manager
vi.mock('../config/configManager', () => ({
  getConfig: vi.fn().mockReturnValue({
    security: {
      allowedDomains: ['youtube.com', 'vimeo.com', 'instagram.com'],
      validateUrls: true,
      sanitizeFilenames: true,
    },
    downloads: {
      maxConcurrent: 3,
      retryAttempts: 3,
    },
  }),
}));

describe('Download Store', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useDownloadStore.setState({
      downloads: [],
      defaultDownloadPath: '',
      isInitialized: false,
    });
  });

  describe('Store Initialization', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      expect(result.current.downloads).toEqual([]);
      expect(result.current.defaultDownloadPath).toBe('');
      expect(result.current.isInitialized).toBe(false);
    });

    it('should initialize default download path', async () => {
      mockTauriCommand('get_downloads_folder', '/Users/<USER>/Downloads');
      
      const { result } = renderHook(() => useDownloadStore());
      
      await act(async () => {
        await result.current.initializeDefaultPath();
      });

      expect(result.current.defaultDownloadPath).toBe('/Users/<USER>/Downloads');
      expect(result.current.isInitialized).toBe(true);
    });
  });

  describe('Download Management', () => {
    it('should add a new download', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      act(() => {
        result.current.addDownload(
          'https://youtube.com/watch?v=test',
          'test-video.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
      });

      expect(result.current.downloads).toHaveLength(1);
      expect(result.current.downloads[0]).toMatchObject({
        url: 'https://youtube.com/watch?v=test',
        filename: 'test-video.mp4',
        quality: '1080p',
        downloadPath: '/Users/<USER>/Downloads',
        status: 'pending',
        progress: 0,
      });
    });

    it('should update download progress', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      // Add a download first
      act(() => {
        result.current.addDownload(
          'https://youtube.com/watch?v=test',
          'test-video.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
      });

      const downloadId = result.current.downloads[0].id;

      // Update the download
      act(() => {
        result.current.updateDownload(downloadId, {
          progress: 50,
          status: 'downloading',
          downloadSpeed: '2.5 MB/s',
        });
      });

      expect(result.current.downloads[0]).toMatchObject({
        progress: 50,
        status: 'downloading',
        downloadSpeed: '2.5 MB/s',
      });
    });

    it('should remove a download', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      // Add a download first
      act(() => {
        result.current.addDownload(
          'https://youtube.com/watch?v=test',
          'test-video.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
      });

      const downloadId = result.current.downloads[0].id;

      // Remove the download
      act(() => {
        result.current.removeDownload(downloadId);
      });

      expect(result.current.downloads).toHaveLength(0);
    });

    it('should clear completed downloads', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      // Add multiple downloads with different statuses
      act(() => {
        result.current.addDownload(
          'https://youtube.com/watch?v=test1',
          'video1.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
        result.current.addDownload(
          'https://youtube.com/watch?v=test2',
          'video2.mp4',
          '720p',
          '/Users/<USER>/Downloads'
        );
      });

      // Mark one as completed
      act(() => {
        result.current.updateDownload(result.current.downloads[0].id, {
          status: 'completed',
          progress: 100,
        });
      });

      // Clear completed downloads
      act(() => {
        result.current.clearCompleted();
      });

      expect(result.current.downloads).toHaveLength(1);
      expect(result.current.downloads[0].status).toBe('pending');
    });
  });

  describe('Download Path Management', () => {
    it('should set default download path', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      act(() => {
        result.current.setDefaultDownloadPath('/custom/path');
      });

      expect(result.current.defaultDownloadPath).toBe('/custom/path');
    });
  });

  describe('URL Validation', () => {
    it('should handle social media URLs correctly', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      // Test with YouTube URL (should be allowed)
      act(() => {
        result.current.addDownload(
          'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
          'rick-roll.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
      });

      expect(result.current.downloads).toHaveLength(1);
      expect(result.current.downloads[0].url).toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    });
  });

  describe('Error Handling', () => {
    it('should handle download errors gracefully', () => {
      const { result } = renderHook(() => useDownloadStore());
      
      // Add a download
      act(() => {
        result.current.addDownload(
          'https://youtube.com/watch?v=test',
          'test-video.mp4',
          '1080p',
          '/Users/<USER>/Downloads'
        );
      });

      const downloadId = result.current.downloads[0].id;

      // Simulate an error
      act(() => {
        result.current.updateDownload(downloadId, {
          status: 'error',
          error: 'Network timeout',
        });
      });

      expect(result.current.downloads[0]).toMatchObject({
        status: 'error',
        error: 'Network timeout',
      });
    });
  });
});
