import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import { Download as LegacyDownload } from './downloadStore';
import { DownloadAdapter } from '../adapters/DownloadAdapter';
import { getFeatureFlags } from '../adapters/FeatureFlags';
import { downloadApplicationService } from '../application/services/ApplicationServiceFactory';
import { getDefaultDownloadsPath } from '../utils/folderUtils';

// Enhanced store state that can work with both legacy and domain entities
interface EnhancedDownloadState {
  downloads: LegacyDownload[]; // Keep legacy interface for UI compatibility
  defaultDownloadPath: string;
  isInitialized: boolean;
  loading: boolean;
  error: string | null;
  
  // Migration-specific state
  migrationStatus: 'not_started' | 'in_progress' | 'completed' | 'failed';
  migrationError: string | null;
  useDomainLayer: boolean;
}

interface EnhancedDownloadActions {
  // Original legacy methods (preserved for compatibility)
  addDownload: (url: string, filename: string, quality: string, downloadPath: string) => void;
  updateDownload: (id: string, updates: Partial<LegacyDownload>) => void;
  removeDownload: (id: string) => void;
  clearCompleted: () => void;
  setDefaultDownloadPath: (path: string) => void;
  initializeDefaultPath: () => Promise<void>;
  startRealDownload: (download: LegacyDownload) => Promise<void>;
  
  // New domain-driven methods
  createDownloadWithDomain: (url: string, filename: string, quality: string, downloadPath: string) => Promise<void>;
  pauseDownloadWithDomain: (id: string) => Promise<void>;
  resumeDownloadWithDomain: (id: string) => Promise<void>;
  cancelDownloadWithDomain: (id: string) => Promise<void>;
  
  // Migration methods
  startMigration: () => Promise<void>;
  rollbackMigration: () => void;
  clearMigrationError: () => void;
  refreshFromDomain: () => Promise<void>;
  
  // Utility methods
  clearError: () => void;
}

type EnhancedDownloadStore = EnhancedDownloadState & EnhancedDownloadActions;

export const useEnhancedDownloadStore = create<EnhancedDownloadStore>()(
  subscribeWithSelector((set, get) => ({
    // State
    downloads: [],
    defaultDownloadPath: 'Downloads',
    isInitialized: false,
    loading: false,
    error: null,
    migrationStatus: 'not_started',
    migrationError: null,
    useDomainLayer: false,

    // Initialize and check feature flags
    initializeDefaultPath: async () => {
      const featureFlags = getFeatureFlags();
      
      try {
        console.log('Initializing enhanced download store...');
        const defaultPath = await getDefaultDownloadsPath();
        
        set({
          defaultDownloadPath: defaultPath,
          useDomainLayer: featureFlags.useDomainLayer,
          isInitialized: true,
        });

        // If domain layer is enabled, try to load from domain repository
        if (featureFlags.useDomainLayer) {
          await get().refreshFromDomain();
        }
        
      } catch (error) {
        console.error('Failed to initialize enhanced download store:', error);
        set({
          error: error instanceof Error ? error.message : 'Initialization failed',
          defaultDownloadPath: 'Downloads',
          isInitialized: true,
        });
      }
    },

    // Legacy method - preserved for backward compatibility
    addDownload: (url: string, filename: string, quality: string, downloadPath: string) => {
      const featureFlags = getFeatureFlags();
      
      if (featureFlags.useDomainLayer) {
        // Use domain layer
        get().createDownloadWithDomain(url, filename, quality, downloadPath);
      } else {
        // Use legacy implementation
        const newDownload: LegacyDownload = {
          id: `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          url,
          filename,
          quality,
          downloadPath,
          status: 'pending',
          progress: 0,
          createdAt: new Date(),
        };

        set((state) => ({
          downloads: [newDownload, ...state.downloads],
        }));

        // Start download using legacy method
        Promise.resolve().then(() => {
          get().startRealDownload(newDownload);
        });
      }
    },

    // New domain-driven methods
    createDownloadWithDomain: async (url: string, filename: string, quality: string, downloadPath: string) => {
      set({ loading: true, error: null });

      try {
        await downloadApplicationService.createDownload(url, filename, quality, downloadPath);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to create download',
          loading: false
        });
      }
    },

    pauseDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.pauseDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to pause download'
        });
      }
    },

    resumeDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.resumeDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to resume download'
        });
      }
    },

    cancelDownloadWithDomain: async (id: string) => {
      try {
        await downloadApplicationService.cancelDownload(id);
        await get().refreshFromDomain();
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to cancel download'
        });
      }
    },

    // Migration methods
    startMigration: async () => {
      set({ migrationStatus: 'in_progress', migrationError: null });
      
      try {
        const currentDownloads = get().downloads;
        console.log(`Starting migration of ${currentDownloads.length} downloads...`);
        
        // Convert legacy downloads to domain entities and save them
        for (const legacyDownload of currentDownloads) {
          try {
            const domainDownload = DownloadAdapter.toDomain(legacyDownload);
            await downloadApplicationService.repository.save(domainDownload);
          } catch (error) {
            console.warn(`Failed to migrate download ${legacyDownload.id}:`, error);
          }
        }
        
        // Enable domain layer
        set({ 
          migrationStatus: 'completed',
          useDomainLayer: true 
        });
        
        // Refresh from domain
        await get().refreshFromDomain();
        
        console.log('Migration completed successfully');
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Migration failed';
        set({
          migrationStatus: 'failed',
          migrationError: errorMessage
        });
        console.error('Migration failed:', error);
      }
    },

    rollbackMigration: () => {
      set({
        migrationStatus: 'not_started',
        migrationError: null,
        useDomainLayer: false
      });
      
      // Clear domain repository storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('flowdownload_downloads_v2');
      }
      
      console.log('Migration rolled back');
    },

    refreshFromDomain: async () => {
      if (!get().useDomainLayer) return;

      set({ loading: true });
      try {
        const domainDownloads = await downloadApplicationService.getAllDownloads();
        const legacyDownloads = domainDownloads.map(DownloadAdapter.toLegacy);
        
        set({ 
          downloads: legacyDownloads,
          loading: false 
        });
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : 'Failed to refresh from domain',
          loading: false
        });
      }
    },

    // Legacy methods - updated to work with domain layer when enabled
    updateDownload: (id: string, updates: Partial<LegacyDownload>) => {
      if (get().useDomainLayer) {
        console.warn('updateDownload called on domain layer - use domain methods instead');
        return;
      }

      set((state) => ({
        downloads: state.downloads.map((download) =>
          download.id === id ? { ...download, ...updates } : download
        ),
      }));
    },

    removeDownload: (id: string) => {
      if (get().useDomainLayer) {
        get().cancelDownloadWithDomain(id);
        return;
      }

      // Legacy cleanup
      if ((window as any).downloadCleanupMap?.has(id)) {
        const cleanup = (window as any).downloadCleanupMap.get(id);
        try {
          cleanup?.();
        } catch (error) {
          console.warn('Error during cleanup:', error);
        }
        (window as any).downloadCleanupMap.delete(id);
      }

      set((state) => ({
        downloads: state.downloads.filter((download) => download.id !== id),
      }));
    },

    clearCompleted: () => {
      if (get().useDomainLayer) {
        downloadApplicationService.clearCompletedDownloads()
          .then(() => get().refreshFromDomain())
          .catch(error => {
            set({
              error: error instanceof Error ? error.message : 'Failed to clear completed downloads'
            });
          });
        return;
      }

      // Legacy implementation
      set((state) => {
        const completedDownloads = state.downloads.filter(
          (download) => download.status === 'completed'
        );
        completedDownloads.forEach((download) => {
          if ((window as any).downloadCleanupMap?.has(download.id)) {
            const cleanup = (window as any).downloadCleanupMap.get(download.id);
            try {
              cleanup?.();
            } catch (error) {
              console.warn('Error during cleanup:', error);
            }
            (window as any).downloadCleanupMap.delete(download.id);
          }
        });

        return {
          downloads: state.downloads.filter(
            (download) => download.status !== 'completed'
          ),
        };
      });
    },

    setDefaultDownloadPath: (path: string) => {
      set({ defaultDownloadPath: path });
    },

    // Keep legacy startRealDownload for backward compatibility
    startRealDownload: async (download: LegacyDownload) => {
      if (get().useDomainLayer) {
        console.warn('startRealDownload called on domain layer - use createDownloadWithDomain instead');
        return;
      }

      // Legacy implementation - import from original store if needed
      const { useDownloadStore } = await import('./downloadStore');
      const legacyStore = useDownloadStore.getState();
      return legacyStore.startRealDownload(download);
    },

    clearError: () => {
      set({ error: null, migrationError: null });
    },

    clearMigrationError: () => {
      set({ migrationError: null });
    },
  }))
);

// Backward compatibility hook - can be used as drop-in replacement
export const useDownloadStore = useEnhancedDownloadStore;
