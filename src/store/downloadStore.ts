import { create } from 'zustand';
import { getDefaultDownloadsPath } from '../utils/folderUtils';
import { ERROR_MESSAGES } from '../config/constants';
import {
  isInTauriEnvironment,
  importTauriAPIs,
} from '../utils/tauriUtils';

export interface Download {
  id: string;
  url: string;
  filename: string;
  quality: string; // Video quality: 'auto', '144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'
  downloadPath: string; // Where the file will be saved
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'paused';
  error?: string;
  createdAt: Date;
  completedAt?: Date;
  downloadSpeed?: string; // e.g., "1.2 MB/s"
  fileSize?: string; // e.g., "45.6 MB"
  estimatedTimeRemaining?: string; // e.g., "2m 30s"
  bytesDownloaded?: number; // Raw bytes downloaded
  bytesTotal?: number; // Raw total bytes
}

interface DownloadState {
  downloads: Download[];
  defaultDownloadPath: string;
  isInitialized: boolean;
  addDownload: (
    url: string,
    filename: string,
    quality: string,
    downloadPath: string
  ) => void;
  updateDownload: (id: string, updates: Partial<Download>) => void;
  removeDownload: (id: string) => void;
  clearCompleted: () => void;
  setDefaultDownloadPath: (path: string) => void;
  initializeDefaultPath: () => Promise<void>;
  startRealDownload: (download: Download) => Promise<void>;
  loadDownloadHistory: () => void;
  saveDownloadHistory: () => void;
}

// Helper function to format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Helper function - always use yt-dlp for maximum compatibility
const isSocialMediaUrl = (_url: string): boolean => {
  // Always return true to use yt-dlp for all downloads
  return true;
};

// Constants for localStorage
const DOWNLOAD_HISTORY_KEY = 'flowdownload_history';
const MAX_HISTORY_ITEMS = 100;

export const useDownloadStore = create<DownloadState>((set, get) => ({
  downloads: [],
  defaultDownloadPath: 'Downloads', // Temporary fallback until initialized
  isInitialized: false,

  loadDownloadHistory: () => {
    try {
      const stored = localStorage.getItem(DOWNLOAD_HISTORY_KEY);
      if (stored) {
        const history = JSON.parse(stored) as Download[];
        // Convert date strings back to Date objects
        const parsedHistory = history.map(download => ({
          ...download,
          createdAt: new Date(download.createdAt),
          completedAt: download.completedAt ? new Date(download.completedAt) : undefined
        }));
        set({ downloads: parsedHistory });
        console.log(`Loaded ${parsedHistory.length} downloads from history`);
      }
    } catch (error) {
      console.error('Failed to load download history:', error);
    }
  },

  saveDownloadHistory: () => {
    try {
      const state = get();
      // Keep only the most recent downloads
      const recentDownloads = state.downloads.slice(0, MAX_HISTORY_ITEMS);
      localStorage.setItem(DOWNLOAD_HISTORY_KEY, JSON.stringify(recentDownloads));
    } catch (error) {
      console.error('Failed to save download history:', error);
    }
  },

  initializeDefaultPath: async () => {
    try {
      console.log('Initializing default download path...');
      const defaultPath = await getDefaultDownloadsPath();
      console.log('Got default path:', defaultPath);

      set({
        defaultDownloadPath: defaultPath,
        isInitialized: true,
      });
      
      // Load download history after initialization
      get().loadDownloadHistory();
    } catch (error) {
      console.error('Failed to initialize default download path:', error);
      set({
        defaultDownloadPath: 'Downloads',
        isInitialized: true,
      });
      
      // Load download history even if path initialization fails
      get().loadDownloadHistory();
    }
  },

  addDownload: (
    url: string,
    filename: string,
    quality: string,
    downloadPath: string
  ) => {
    const newDownload: Download = {
      id: `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      url,
      filename,
      quality,
      downloadPath,
      status: 'pending',
      progress: 0,
      createdAt: new Date(),
    };

    set((state) => ({
      downloads: [newDownload, ...state.downloads],
    }));

    // Save to localStorage
    get().saveDownloadHistory();

    // Start real download after state update using Promise.resolve for proper async handling
    Promise.resolve().then(() => {
      get().startRealDownload(newDownload);
    });
  },

  updateDownload: (id: string, updates: Partial<Download>) => {
    console.log(`🔄 Updating download ${id}:`, updates);
    set((state) => ({
      downloads: state.downloads.map((download) =>
        download.id === id ? { ...download, ...updates } : download
      ),
    }));
    
    // Save to localStorage after update
    get().saveDownloadHistory();
  },

  removeDownload: (id: string) => {
    // Clean up any pending listeners before removing
    if (window.downloadCleanupMap?.has(id)) {
      const cleanup = window.downloadCleanupMap.get(id);
      try {
        cleanup?.();
      } catch (error) {
        console.warn('Error during cleanup:', error);
      }
      window.downloadCleanupMap.delete(id);
    }

    set((state) => ({
      downloads: state.downloads.filter((download) => download.id !== id),
    }));
    
    // Save to localStorage after removal
    get().saveDownloadHistory();
  },

  clearCompleted: () => {
    set((state) => {
      // Clean up listeners for completed downloads before removing them
      const completedDownloads = state.downloads.filter(
        (download) => download.status === 'completed'
      );
      completedDownloads.forEach((download) => {
        if (window.downloadCleanupMap?.has(download.id)) {
          const cleanup = window.downloadCleanupMap.get(download.id);
          try {
            cleanup?.();
          } catch (error) {
            console.warn('Error during cleanup:', error);
          }
          window.downloadCleanupMap.delete(download.id);
        }
      });

      return {
        downloads: state.downloads.filter(
          (download) => download.status !== 'completed'
        ),
      };
    });
    
    // Save to localStorage after clearing completed
    get().saveDownloadHistory();
  },

  setDefaultDownloadPath: (path: string) => {
    set({ defaultDownloadPath: path });
  },

  startRealDownload: async (download: Download) => {
    const { updateDownload } = get();

    try {
      // Use the robust Tauri environment detection
      if (isInTauriEnvironment()) {
        let invoke: any;
        let listen: any;

        try {
          console.log('🚀 Importing Tauri APIs for download...');
          const apis = await importTauriAPIs();
          invoke = apis.invoke;

          // Import event API separately
          const eventModule = await import('@tauri-apps/api/event');
          if (!eventModule?.listen) {
            throw new Error('Event listen function not available');
          }
          listen = eventModule.listen;

          console.log('✅ All Tauri APIs successfully imported and validated');
        } catch (importError) {
          console.error('❌ Failed to import Tauri APIs:', importError);
          updateDownload(download.id, {
            status: 'error',
            error:
              'Desktop features not available. Please ensure you are running the desktop version of the app.',
          });
          return;
        }

        // Set up progress listener with enhanced metrics and error handling
        let progressUnlisten: (() => void) | undefined;
        try {
          console.log(`🎧 Setting up progress listener for download ${download.id}`);
          progressUnlisten = await listen(
            'download-progress',
            (event: { payload?: unknown }) => {
              console.log('📥 Received download-progress event:', event);
              try {
                const payload = event?.payload || event;
                console.log('📦 Event payload:', payload);
                
                if (payload && typeof payload === 'object' && 'id' in payload) {
                  const typedPayload = payload as Record<string, unknown>;
                  const eventId = typedPayload.id;
                  console.log(`🔍 Comparing IDs: event="${eventId}" vs expected="${download.id}"`);
                  
                  if (eventId === download.id) {
                    // The backend sends downloaded as percentage (0-100) and total as 100
                    const progress = Number(typedPayload.downloaded) || 0;
                    
                    console.log(`📊 Progress Update for ${download.id}:`, {
                      progress,
                      speed: typedPayload.speed,
                      eta: typedPayload.estimated_remaining
                    });
                    
                    updateDownload(download.id, {
                      status: 'downloading',
                      progress,
                      downloadSpeed: String(
                        typedPayload.speed || 'Calculating...'
                      ),
                      estimatedTimeRemaining: String(
                        typedPayload.estimated_remaining || ''
                      ),
                    });
                  } else {
                    console.log(`⚠️ Event ID mismatch - ignoring event for ${eventId}`);
                  }
                } else {
                  console.log('⚠️ Event payload missing or invalid:', payload);
                }
              } catch (error) {
                console.error('Error in progress listener:', error);
              }
            }
          );
        } catch (listenerError) {
          console.error('Failed to set up progress listener:', listenerError);
        }

        // Set up completion listener with error handling
        let completeUnlisten: (() => void) | undefined;
        try {
          completeUnlisten = await listen(
            'download-complete',
            (event: { payload?: unknown }) => {
              try {
                const payload = event?.payload || event;
                if (
                  payload === download.id ||
                  (typeof payload === 'object' &&
                    payload &&
                    'id' in payload &&
                    (payload as Record<string, unknown>).id === download.id)
                ) {
                  updateDownload(download.id, {
                    status: 'completed',
                    progress: 100,
                    downloadSpeed: undefined,
                    estimatedTimeRemaining: undefined,
                    completedAt: new Date(),
                  });
                  // Clean up listeners
                  try {
                    progressUnlisten?.();
                    completeUnlisten?.();
                  } catch (cleanupError) {
                    console.warn('Error cleaning up listeners:', cleanupError);
                  }
                }
              } catch (error) {
                console.error('Error in completion listener:', error);
              }
            }
          );
        } catch (listenerError) {
          console.error('Failed to set up completion listener:', listenerError);
        }

        // Set up error listener with proper cleanup and error handling
        let errorUnlisten: (() => void) | undefined;
        try {
          errorUnlisten = await listen(
            'download-error',
            (event: { payload?: unknown }) => {
              try {
                console.log('📥 Download error event received:', event);
                const payload = event?.payload || event;
                console.log('📥 Error payload:', payload);
                const errorData =
                  typeof payload === 'string'
                    ? { id: payload }
                    : (payload as Record<string, unknown>);
                console.log('📥 Parsed error data:', errorData);
                if (
                  errorData &&
                  typeof errorData === 'object' &&
                  'id' in errorData &&
                  errorData.id === download.id
                ) {
                  console.log('❌ Download error for ID:', download.id, 'Error:', errorData.error);
                  updateDownload(download.id, {
                    status: 'error',
                    progress: 0,
                    error: String(
                      errorData.error || ERROR_MESSAGES.DOWNLOAD_FAILED
                    ),
                  });
                  // Clean up all listeners
                  try {
                    progressUnlisten?.();
                    completeUnlisten?.();
                    errorUnlisten?.();
                  } catch (cleanupError) {
                    console.warn(
                      'Error cleaning up error listeners:',
                      cleanupError
                    );
                  }
                }
              } catch (error) {
                console.error('Error in error listener:', error);
              }
            }
          );
        } catch (listenerError) {
          console.error('Failed to set up error listener:', listenerError);
        }

        // Store cleanup function for potential cancellation
        const cleanup = () => {
          progressUnlisten?.();
          completeUnlisten?.();
          errorUnlisten?.();
        };

        // Add cleanup to a Map for later use if needed
        if (!window.downloadCleanupMap) {
          window.downloadCleanupMap = new Map();
        }
        window.downloadCleanupMap.set(download.id, cleanup);

        // Ensure invoke function is available before proceeding
        if (!invoke) {
          updateDownload(download.id, {
            status: 'error',
            error:
              'Download functionality not available. Tauri invoke function is missing.',
          });
          return;
        }

        // Start the download
        console.log('🚀 Starting download for:', download.id);
        console.log('📁 Download path:', download.downloadPath);
        console.log('📄 Filename:', download.filename);
        console.log('🆔 Full download ID:', download.id);
        console.log('🆔 ID type:', typeof download.id);
        console.log('🔑 ID being sent to backend:', download.id);
        updateDownload(download.id, { status: 'downloading' });

        const filePath = `${download.downloadPath}/${download.filename}`;
        console.log('📍 Full file path:', filePath);

        // Special handling for social media platforms and search terms
        if (isSocialMediaUrl(download.url)) {
          // Check if it's a search term (not a valid URL)
          const isSearchTerm =
            !download.url.startsWith('http://') &&
            !download.url.startsWith('https://');
          if (isSearchTerm) {
            console.log(
              'Search term detected, will search YouTube for:',
              download.url
            );
          } else {
            console.log(
              'Social media URL detected, checking if tools are available...'
            );
          }

          // Check if yt-dlp is available before showing error
          try {
            const ytdlpAvailable = await invoke('check_ytdlp_availability');
            console.log('yt-dlp availability check result:', ytdlpAvailable);

            if (!ytdlpAvailable) {
              const errorMessage = 'yt-dlp is not installed. Please install yt-dlp to enable downloads.';
              
              updateDownload(download.id, {
                status: 'error',
                error: errorMessage,
              });
              return;
            } else {
              console.log(
                '✅ yt-dlp is available, proceeding with download...'
              );
              // Continue with the download since yt-dlp is available
            }
          } catch (checkError) {
            console.error('Failed to check yt-dlp availability:', checkError);
            // Continue with download attempt, let backend handle the check
          }
        }

        try {
          // Extract quality value from the label (e.g., "1080p" from "1080p")
          // The download.quality contains the label like "1080p" or "Audio Only (High)"
          let qualityValue = download.quality;
          if (download.quality.includes('Audio Only')) {
            qualityValue = download.quality.includes('High') ? 'audio-high' : 
                          download.quality.includes('Medium') ? 'audio-medium' : 'audio-low';
          } else if (download.quality === 'Best Quality Available') {
            qualityValue = 'best';
          }
          
          // Extract format from filename extension
          const format = filePath.split('.').pop() || 'mp4';
          
          console.log('📤 Invoking download_file with:', {
            id: download.id,
            url: download.url,
            filePath: filePath,
            quality: qualityValue,
            format: format,
          });
          await invoke('download_file', {
            id: download.id,
            url: download.url,
            filePath: filePath,
            quality: qualityValue,
            format: format,
          });
          console.log('✅ Download command invoked successfully');
        } catch (error: unknown) {
          console.error('❌ Download failed:', error);
          updateDownload(download.id, {
            status: 'error',
            error: error instanceof Error ? error.message : 'Download failed',
          });
        }
      } else {
        // Web environment not supported for downloads
        console.log('Download functionality is only available in the desktop app');
        updateDownload(download.id, {
          status: 'error',
          error: 'Downloads are only available in the desktop application',
        });
      }
    } catch (error: unknown) {
      console.error('Download failed:', error);
      updateDownload(download.id, {
        status: 'error',
        error: error instanceof Error ? error.message : 'Download failed',
      });
    }
  },
}));
