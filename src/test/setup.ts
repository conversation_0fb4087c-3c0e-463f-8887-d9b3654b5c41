import '@testing-library/jest-dom';
import { beforeEach, vi } from 'vitest';

// Mock Tauri API
const mockTauri = {
  invoke: vi.fn(),
  event: {
    listen: vi.fn(),
    emit: vi.fn(),
  },
  window: {
    getCurrent: vi.fn(() => ({
      setTitle: vi.fn(),
      minimize: vi.fn(),
      maximize: vi.fn(),
      close: vi.fn(),
    })),
  },
  path: {
    downloadDir: vi.fn().mockResolvedValue('/Users/<USER>/Downloads'),
    appConfigDir: vi.fn().mockResolvedValue('/Users/<USER>/.config/flowdownload'),
    appLogDir: vi.fn().mockResolvedValue('/Users/<USER>/.logs/flowdownload'),
    tempDir: vi.fn().mockResolvedValue('/tmp'),
    appDataDir: vi.fn().mockResolvedValue('/Users/<USER>/.local/share/flowdownload'),
    join: vi.fn((...paths: string[]) => paths.join('/')),
  },
  fs: {
    readTextFile: vi.fn(),
    writeTextFile: vi.fn(),
    exists: vi.fn(),
    create: vi.fn(),
  },
  dialog: {
    open: vi.fn(),
    save: vi.fn(),
  },
  shell: {
    open: vi.fn(),
  },
};

// Mock window.__TAURI__
Object.defineProperty(window, '__TAURI__', {
  value: mockTauri,
  writable: true,
});

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    reload: vi.fn(),
    href: 'http://localhost:3000',
  },
  writable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
});

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
  takeRecords: vi.fn(() => [])
})) as any;

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn()
})) as any;

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});
