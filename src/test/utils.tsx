import { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { ThemeProvider } from '../ThemeContext';
import { vi } from 'vitest';

// Custom render function that includes providers
const AllProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider>
      {children}
    </ThemeProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllProviders, ...options });

// Mock utilities for Tauri commands
export const mockTauriCommand = (command: string, result: any) => {
  if (!window.__TAURI__) {
    throw new Error('Tauri not available in test environment');
  }
  const mockInvoke = vi.mocked(window.__TAURI__.invoke);
  mockInvoke.mockImplementation((cmd: string, ..._args: any[]) => {
    if (cmd === command) {
      return Promise.resolve(result);
    }
    return Promise.reject(new Error(`Unmocked command: ${cmd}`));
  });
};

export const mockTauriCommandError = (command: string, error: string) => {
  if (!window.__TAURI__) {
    throw new Error('Tauri not available in test environment');
  }
  const mockInvoke = vi.mocked(window.__TAURI__.invoke);
  mockInvoke.mockImplementation((cmd: string, ..._args: any[]) => {
    if (cmd === command) {
      return Promise.reject(new Error(error));
    }
    return Promise.reject(new Error(`Unmocked command: ${cmd}`));
  });
};

// Mock download data for testing
export const createMockDownload = (overrides = {}) => ({
  id: 'test-download-1',
  url: 'https://example.com/video.mp4',
  filename: 'test-video.mp4',
  quality: '1080p',
  downloadPath: '/Users/<USER>/Downloads',
  progress: 50,
  status: 'downloading' as const,
  createdAt: new Date('2024-01-01T10:00:00Z'),
  downloadSpeed: '2.5 MB/s',
  fileSize: '100 MB',
  estimatedTimeRemaining: '1m 30s',
  bytesDownloaded: 52428800,
  bytesTotal: 104857600,
  ...overrides,
});

// Mock config data
export const createMockConfig = (overrides = {}) => ({
  app: {
    name: 'FlowDownload',
    version: '1.0.0',
    identifier: 'com.flowdownload.app',
    title: 'FlowDownload Desktop Pro',
    description: 'Modern desktop downloader',
  },
  window: {
    defaultWidth: 1280,
    defaultHeight: 800,
    minWidth: 800,
    minHeight: 600,
    resizable: true,
    center: true,
  },
  downloads: {
    maxConcurrent: 3,
    retryAttempts: 3,
    timeoutSeconds: 30,
    chunkSizeBytes: 1048576,
    defaultQuality: 'auto',
    supportedFormats: ['mp4', 'webm', 'mp3'],
    progressUpdateInterval: 1000,
  },
  security: {
    allowedDomains: ['youtube.com', 'vimeo.com'],
    maxFileSize: 10737418240,
    sanitizeFilenames: true,
    validateUrls: true,
  },
  ...overrides,
});

// Wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// User event helpers
export const createUserEvent = () => {
  // This will be imported from @testing-library/user-event in actual tests
  return {
    click: vi.fn(),
    type: vi.fn(),
    clear: vi.fn(),
    selectOptions: vi.fn(),
  };
};

// Re-export everything from testing library
export * from '@testing-library/react';
export { customRender as render };
