import React, { createContext, useContext, useState, useEffect } from 'react';

export type TabType = 'dashboard' | 'download' | 'upload' | 'analytics' | 'ai';

interface NavigationContextType {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const NavigationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');

  useEffect(() => {
    const handleNavigateToTab = (event: CustomEvent) => {
      const tab = event.detail as TabType;
      if (['dashboard', 'download', 'upload', 'analytics', 'ai'].includes(tab)) {
        setActiveTab(tab);
      }
    };

    window.addEventListener('navigate-to-tab' as any, handleNavigateToTab);

    return () => {
      window.removeEventListener('navigate-to-tab' as any, handleNavigateToTab);
    };
  }, []);

  return (
    <NavigationContext.Provider value={{ activeTab, setActiveTab }}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within NavigationProvider');
  }
  return context;
};