/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly MODE: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// Enhanced Tauri global types
declare global {
  interface Window {
    __TAURI__?: {
      invoke: (cmd: string, args?: Record<string, unknown>) => Promise<unknown>;
    };
    __TAURI_INVOKE__?: (cmd: string, args?: Record<string, unknown>) => Promise<unknown>;
    __TAURI_METADATA__?: {
      currentWindow: {
        label: string;
      };
    };
    downloadCleanupMap?: Map<string, () => void>;
  }
}
