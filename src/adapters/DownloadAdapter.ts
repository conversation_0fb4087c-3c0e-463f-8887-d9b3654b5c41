import { Download as DomainDownload } from '../domain/models';
import { Download as LegacyDownload } from '../store/downloadStore';

export class DownloadAdapter {
  static toDomain(legacy: LegacyDownload): DomainDownload {
    // Convert legacy download to domain entity
    const fullPath = `${legacy.downloadPath}/${legacy.filename}`;
    const domain = DomainDownload.create(
      legacy.url,
      fullPath,
      legacy.quality
    );

    // Apply current state based on legacy status
    try {
      switch (legacy.status) {
        case 'downloading':
          domain.start();
          if (legacy.bytesDownloaded && legacy.bytesTotal) {
            domain.updateProgress(
              legacy.bytesDownloaded,
              legacy.bytesTotal,
              this.parseSpeed(legacy.downloadSpeed || '0 B/s')
            );
          }
          break;
        case 'completed':
          domain.start();
          // Preserve progress for completed downloads
          if (legacy.bytesDownloaded && legacy.bytesTotal) {
            domain.updateProgress(
              legacy.bytesDownloaded,
              legacy.bytesTotal,
              this.parseSpeed(legacy.downloadSpeed || '0 B/s')
            );
          }
          domain.complete();
          break;
        case 'error':
          domain.fail(legacy.error || 'Unknown error');
          break;
        case 'paused':
          domain.start();
          domain.pause();
          break;
        // 'pending' is the default state, no action needed
      }
    } catch (error) {
      console.warn('Error applying legacy state to domain entity:', error);
      // If state transition fails, keep in pending state
    }

    return domain;
  }

  static toLegacy(domain: DomainDownload): LegacyDownload {
    const dto = domain.toDTO();
    return {
      id: dto.id,
      url: dto.url,
      filename: dto.filename,
      quality: dto.quality,
      downloadPath: dto.downloadPath,
      progress: dto.progress,
      status: dto.status as any, // Cast to legacy status type
      error: dto.error,
      createdAt: dto.createdAt,
      completedAt: dto.completedAt,
      downloadSpeed: dto.downloadSpeed,
      fileSize: this.formatFileSize(dto.bytesTotal),
      estimatedTimeRemaining: this.formatTimeRemaining(dto.estimatedTimeRemaining),
      bytesDownloaded: dto.bytesDownloaded,
      bytesTotal: dto.bytesTotal
    };
  }

  private static parseSpeed(speedString: string): number {
    // Parse speed string like "1.5 MB/s" back to bytes per second
    const match = speedString.match(/([0-9.]+)\s*(B|KB|MB|GB)\/s/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    switch (unit) {
      case 'GB': return value * 1024 * 1024 * 1024;
      case 'MB': return value * 1024 * 1024;
      case 'KB': return value * 1024;
      case 'B': return value;
      default: return 0;
    }
  }

  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private static formatTimeRemaining(seconds: number | null): string {
    if (!seconds || seconds <= 0) return '';
    
    if (seconds < 60) {
      return `${Math.ceil(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.ceil(seconds % 60);
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }
}
