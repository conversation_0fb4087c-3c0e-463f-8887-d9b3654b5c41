export interface FeatureFlags {
  useDomainLayer: boolean;
  useEnhancedRepository: boolean;
  enableBatchOperations: boolean;
  enableAnalytics: boolean;
  enableMigrationMode: boolean;
}

export const getFeatureFlags = (): FeatureFlags => {
  // Start with all flags off for safety
  const defaultFlags: FeatureFlags = {
    useDomainLayer: false,
    useEnhancedRepository: false,
    enableBatchOperations: false,
    enableAnalytics: false,
    enableMigrationMode: false
  };

  // Allow override via localStorage for development
  if (typeof window !== 'undefined') {
    return {
      useDomainLayer: localStorage.getItem('ff_domain_layer') === 'true',
      useEnhancedRepository: localStorage.getItem('ff_enhanced_repository') === 'true',
      enableBatchOperations: localStorage.getItem('ff_batch_operations') === 'true',
      enableAnalytics: localStorage.getItem('ff_analytics') === 'true',
      enableMigrationMode: localStorage.getItem('ff_migration_mode') === 'true'
    };
  }

  return defaultFlags;
};

export const setFeatureFlag = (flag: keyof FeatureFlags, enabled: boolean): void => {
  if (typeof window !== 'undefined') {
    const storageKey = `ff_${flag.replace(/([A-Z])/g, '_$1').toLowerCase()}`;
    localStorage.setItem(storageKey, enabled.toString());
  }
};
