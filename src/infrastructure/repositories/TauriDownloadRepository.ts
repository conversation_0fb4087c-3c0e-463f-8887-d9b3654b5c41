import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class TauriDownloadRepository implements DownloadRepository {
  async save(download: Download): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dto = download.toDTO();
      await invoke('save_download', { download: dto });
    } catch (error) {
      throw new Error(`Failed to save download: ${error}`);
    }
  }

  async findById(id: DownloadId): Promise<Download | null> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dto = await invoke<any>('find_download_by_id', { id: id.toString() });
      return dto ? Download.fromDTO(dto) : null;
    } catch (error) {
      console.error('Error finding download by id:', error);
      return null;
    }
  }

  async findAll(): Promise<Download[]> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dtos = await invoke<any[]>('find_all_downloads');
      return dtos.map(dto => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error finding all downloads:', error);
      return [];
    }
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dtos = await invoke<any[]>('find_downloads_by_status', { status });
      return dtos.map(dto => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error finding downloads by status:', error);
      return [];
    }
  }

  async delete(id: DownloadId): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('delete_download', { id: id.toString() });
    } catch (error) {
      throw new Error(`Failed to delete download: ${error}`);
    }
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('delete_downloads_by_status', { status });
    } catch (error) {
      throw new Error(`Failed to delete downloads by status: ${error}`);
    }
  }
}
