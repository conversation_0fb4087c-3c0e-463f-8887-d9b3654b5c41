// Factory function to create appropriate repository based on environment
import { DownloadRepository } from '../../domain/repositories';
import { InMemoryDownloadRepository } from './InMemoryDownloadRepository';
import { LocalStorageDownloadRepository } from './LocalStorageDownloadRepository';
import { TauriDownloadRepository } from './TauriDownloadRepository';
import { isInTauriEnvironment } from '../../utils/tauriUtils';

export function createDownloadRepository(): DownloadRepository {
  if (typeof window === 'undefined') {
    // Server-side rendering or testing
    return new InMemoryDownloadRepository();
  }
  
  if (isInTauriEnvironment()) {
    // Desktop app with Tauri backend
    return new TauriDownloadRepository();
  }
  
  // Web browser
  return new LocalStorageDownloadRepository();
}

export { InMemoryDownloadRepository, LocalStorageDownloadRepository, TauriDownloadRepository };
