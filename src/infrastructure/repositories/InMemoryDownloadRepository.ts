import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class InMemoryDownloadRepository implements DownloadRepository {
  private downloads = new Map<string, Download>();

  async save(download: Download): Promise<void> {
    this.downloads.set(download.getId().toString(), download);
  }

  async findById(id: DownloadId): Promise<Download | null> {
    return this.downloads.get(id.toString()) || null;
  }

  async findAll(): Promise<Download[]> {
    return Array.from(this.downloads.values());
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    return Array.from(this.downloads.values())
      .filter(download => download.getStatus() === status);
  }

  async delete(id: DownloadId): Promise<void> {
    this.downloads.delete(id.toString());
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    const toDelete = await this.findByStatus(status);
    toDelete.forEach(download => {
      this.downloads.delete(download.getId().toString());
    });
  }
}
