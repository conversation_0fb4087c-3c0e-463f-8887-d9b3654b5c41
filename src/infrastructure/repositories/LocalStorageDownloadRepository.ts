import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class LocalStorageDownloadRepository implements DownloadRepository {
  private readonly storageKey = 'flowdownload_downloads_v2'; // v2 to avoid conflicts

  private async getStoredDownloads(): Promise<Download[]> {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return [];
      
      const dtos = JSON.parse(stored);
      return dtos.map((dto: any) => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error reading downloads from localStorage:', error);
      return [];
    }
  }

  private async saveDownloads(downloads: Download[]): Promise<void> {
    try {
      const dtos = downloads.map(download => download.toDTO());
      localStorage.setItem(this.storageKey, JSON.stringify(dtos));
    } catch (error) {
      console.error('Error saving downloads to localStorage:', error);
      throw new Error('Failed to save downloads');
    }
  }

  async save(download: Download): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const existingIndex = downloads.findIndex(d => 
      d.getId().equals(download.getId())
    );

    if (existingIndex >= 0) {
      downloads[existingIndex] = download;
    } else {
      downloads.push(download);
    }

    await this.saveDownloads(downloads);
  }

  async findById(id: DownloadId): Promise<Download | null> {
    const downloads = await this.getStoredDownloads();
    return downloads.find(download => download.getId().equals(id)) || null;
  }

  async findAll(): Promise<Download[]> {
    return this.getStoredDownloads();
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    const downloads = await this.getStoredDownloads();
    return downloads.filter(download => download.getStatus() === status);
  }

  async delete(id: DownloadId): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const filtered = downloads.filter(download => !download.getId().equals(id));
    await this.saveDownloads(filtered);
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const filtered = downloads.filter(download => download.getStatus() !== status);
    await this.saveDownloads(filtered);
  }
}
