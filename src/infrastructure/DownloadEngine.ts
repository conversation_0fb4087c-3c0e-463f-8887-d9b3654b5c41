import { isInTauriEnvironment } from '../utils/tauriUtils';

export interface DownloadEngine {
  startDownload(id: string, url: string, filePath: string): Promise<void>;
  pauseDownload(id: string): Promise<void>;
  resumeDownload(id: string): Promise<void>;
  cancelDownload(id: string): Promise<void>;
}

export class TauriDownloadEngine implements DownloadEngine {
  async startDownload(id: string, url: string, filePath: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('download_file', { id, url, filePath: filePath }); // Changed to camelCase for Tauri v2
  }

  async pauseDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('pause_download', { id });
  }

  async resumeDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('resume_download', { id });
  }

  async cancelDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('cancel_download_new', { id });
  }
}

export class MockDownloadEngine implements DownloadEngine {
  private activeDownloads = new Set<string>();

  async startDownload(id: string, url: string, filePath: string): Promise<void> {
    this.activeDownloads.add(id);
    console.log(`Mock download started: ${id}, ${url} -> ${filePath}`);
    
    // Simulate download completion after 2 seconds for testing
    setTimeout(() => {
      this.activeDownloads.delete(id);
      console.log(`Mock download completed: ${id}`);
    }, 2000);
  }

  async pauseDownload(id: string): Promise<void> {
    console.log(`Mock download paused: ${id}`);
  }

  async resumeDownload(id: string): Promise<void> {
    console.log(`Mock download resumed: ${id}`);
  }

  async cancelDownload(id: string): Promise<void> {
    this.activeDownloads.delete(id);
    console.log(`Mock download cancelled: ${id}`);
  }
}

// Factory function
export function createDownloadEngine(): DownloadEngine {
  if (isInTauriEnvironment()) {
    return new TauriDownloadEngine();
  }
  return new MockDownloadEngine();
}
