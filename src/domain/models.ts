// Value Objects - Ensuring data integrity and business rules

export class DownloadId {
  private constructor(private readonly value: string) {
    if (!value || value.length < 1) {
      throw new Error('DownloadId cannot be empty');
    }
  }

  static create(): DownloadId {
    const id = `download_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    return new DownloadId(id);
  }

  static fromString(value: string): DownloadId {
    return new DownloadId(value);
  }

  toString(): string {
    return this.value;
  }

  equals(other: DownloadId): boolean {
    return this.value === other.value;
  }
}

export class DownloadUrl {
  private constructor(private readonly value: string) {}

  static create(url: string): DownloadUrl {
    // Comprehensive URL validation
    if (!url || url.trim().length === 0) {
      throw new Error('URL cannot be empty');
    }

    const trimmedUrl = url.trim();
    
    // Check for valid URL patterns
    const isValidUrl = trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://');
    const hasVideoExtension = ['.mp4', '.mkv', '.webm', '.avi', '.mov']
      .some(ext => trimmedUrl.toLowerCase().endsWith(ext));
    const containsSocialMedia = ['youtube.com', 'youtu.be', 'twitter.com', 'x.com', 
                                'facebook.com', 'instagram.com', 'tiktok.com']
      .some(domain => trimmedUrl.includes(domain));

    if (!isValidUrl && !hasVideoExtension && !containsSocialMedia) {
      throw new Error('Invalid URL format');
    }

    return new DownloadUrl(trimmedUrl);
  }

  getValue(): string {
    return this.value;
  }

  isSocialMedia(): boolean {
    const socialDomains = ['youtube.com', 'youtu.be', 'twitter.com', 'x.com', 
                          'facebook.com', 'instagram.com', 'tiktok.com'];
    return socialDomains.some(domain => this.value.includes(domain));
  }

  requiresExternalTool(): boolean {
    return this.isSocialMedia();
  }
}

export class FilePath {
  private constructor(private readonly value: string) {}

  static create(path: string): FilePath {
    if (!path || path.trim().length === 0) {
      throw new Error('File path cannot be empty');
    }

    // Security validation
    if (path.includes('..') || path.includes('\0')) {
      throw new Error('Invalid file path - security violation');
    }

    return new FilePath(path.trim());
  }

  getValue(): string {
    return this.value;
  }

  getDirectory(): string {
    const lastSlash = Math.max(this.value.lastIndexOf('/'), this.value.lastIndexOf('\\'));
    return lastSlash !== -1 ? this.value.substring(0, lastSlash) : '.';
  }

  getFileName(): string {
    const lastSlash = Math.max(this.value.lastIndexOf('/'), this.value.lastIndexOf('\\'));
    return lastSlash !== -1 ? this.value.substring(lastSlash + 1) : this.value;
  }
}

export enum DownloadStatus {
  PENDING = 'pending',
  DOWNLOADING = 'downloading',
  COMPLETED = 'completed',
  ERROR = 'error',
  PAUSED = 'paused',
  CANCELLED = 'cancelled'
}

export class DownloadProgress {
  constructor(
    public readonly bytesDownloaded: number,
    public readonly totalBytes: number,
    public readonly speedBytesPerSecond: number
  ) {
    if (bytesDownloaded < 0) throw new Error('Downloaded bytes cannot be negative');
    if (totalBytes < 0) throw new Error('Total bytes cannot be negative');
    if (speedBytesPerSecond < 0) throw new Error('Speed cannot be negative');
    if (bytesDownloaded > totalBytes && totalBytes > 0) {
      throw new Error('Downloaded bytes cannot exceed total bytes');
    }
  }

  getPercentage(): number {
    if (this.totalBytes === 0) return 0;
    return Math.round((this.bytesDownloaded / this.totalBytes) * 100);
  }

  getEstimatedTimeRemaining(): number | null {
    if (this.speedBytesPerSecond === 0 || this.totalBytes === 0) return null;
    const remainingBytes = this.totalBytes - this.bytesDownloaded;
    return Math.ceil(remainingBytes / this.speedBytesPerSecond);
  }

  getFormattedSpeed(): string {
    const speed = this.speedBytesPerSecond;
    if (speed > 1024 * 1024) {
      return `${(speed / (1024 * 1024)).toFixed(1)} MB/s`;
    } else if (speed > 1024) {
      return `${(speed / 1024).toFixed(1)} KB/s`;
    } else {
      return `${speed.toFixed(0)} B/s`;
    }
  }
}

// Domain Entity - Download Aggregate Root
export class Download {
  private constructor(
    private readonly id: DownloadId,
    private readonly url: DownloadUrl,
    private readonly filePath: FilePath,
    private readonly quality: string,
    private status: DownloadStatus,
    private progress: DownloadProgress,
    private readonly createdAt: Date,
    private completedAt?: Date,
    private errorMessage?: string
  ) {}

  static create(
    url: string,
    filePath: string,
    quality: string
  ): Download {
    return new Download(
      DownloadId.create(),
      DownloadUrl.create(url),
      FilePath.create(filePath),
      quality,
      DownloadStatus.PENDING,
      new DownloadProgress(0, 0, 0),
      new Date()
    );
  }

  // Domain methods that enforce business rules
  start(): void {
    if (this.status !== DownloadStatus.PENDING && this.status !== DownloadStatus.PAUSED) {
      throw new Error(`Cannot start download in ${this.status} status`);
    }
    this.status = DownloadStatus.DOWNLOADING;
  }

  pause(): void {
    if (this.status !== DownloadStatus.DOWNLOADING) {
      throw new Error(`Cannot pause download in ${this.status} status`);
    }
    this.status = DownloadStatus.PAUSED;
  }

  complete(): void {
    if (this.status !== DownloadStatus.DOWNLOADING) {
      throw new Error(`Cannot complete download in ${this.status} status`);
    }
    this.status = DownloadStatus.COMPLETED;
    this.completedAt = new Date();
  }

  fail(error: string): void {
    if (this.status === DownloadStatus.COMPLETED) {
      throw new Error('Cannot fail a completed download');
    }
    this.status = DownloadStatus.ERROR;
    this.errorMessage = error;
  }

  updateProgress(bytesDownloaded: number, totalBytes: number, speed: number): void {
    if (this.status !== DownloadStatus.DOWNLOADING) {
      throw new Error(`Cannot update progress for download in ${this.status} status`);
    }
    this.progress = new DownloadProgress(bytesDownloaded, totalBytes, speed);
  }

  // Getters
  getId(): DownloadId { return this.id; }
  getUrl(): DownloadUrl { return this.url; }
  getFilePath(): FilePath { return this.filePath; }
  getQuality(): string { return this.quality; }
  getStatus(): DownloadStatus { return this.status; }
  getProgress(): DownloadProgress { return this.progress; }
  getCreatedAt(): Date { return this.createdAt; }
  getCompletedAt(): Date | undefined { return this.completedAt; }
  getErrorMessage(): string | undefined { return this.errorMessage; }

  // For serialization to/from store
  toDTO() {
    return {
      id: this.id.toString(),
      url: this.url.getValue(),
      filePath: this.filePath.getValue(),
      filename: this.filePath.getFileName(),
      downloadPath: this.filePath.getDirectory(),
      quality: this.quality,
      status: this.status,
      progress: this.progress.getPercentage(),
      bytesDownloaded: this.progress.bytesDownloaded,
      bytesTotal: this.progress.totalBytes,
      downloadSpeed: this.progress.getFormattedSpeed(),
      estimatedTimeRemaining: this.progress.getEstimatedTimeRemaining(),
      createdAt: this.createdAt,
      completedAt: this.completedAt,
      error: this.errorMessage
    };
  }

  static fromDTO(dto: any): Download {
    const download = new Download(
      DownloadId.fromString(dto.id),
      DownloadUrl.create(dto.url),
      FilePath.create(dto.filePath),
      dto.quality,
      dto.status as DownloadStatus,
      new DownloadProgress(dto.bytesDownloaded || 0, dto.bytesTotal || 0, 0),
      new Date(dto.createdAt),
      dto.completedAt ? new Date(dto.completedAt) : undefined,
      dto.error
    );
    return download;
  }
}
