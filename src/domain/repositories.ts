// Repository interfaces - defining contracts for data access

import { Download, DownloadId, DownloadStatus } from './models';

export interface DownloadRepository {
  save(download: Download): Promise<void>;
  findById(id: DownloadId): Promise<Download | null>;
  findAll(): Promise<Download[]>;
  findByStatus(status: DownloadStatus): Promise<Download[]>;
  delete(id: DownloadId): Promise<void>;
  deleteByStatus(status: DownloadStatus): Promise<void>;
}

export interface ConfigurationRepository {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T): Promise<void>;
  delete(key: string): Promise<void>;
}
