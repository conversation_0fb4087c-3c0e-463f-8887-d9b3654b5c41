// Domain services for complex business logic

import { Download, DownloadUrl, FilePath } from '../models';

export class DownloadDomainService {
  static validateDownloadRequest(url: string, filePath: string, quality: string): void {
    // Business rule validation - check individual parameters first for specific error messages
    if (!url) {
      throw new Error('URL cannot be empty');
    }
    if (!filePath) {
      throw new Error('File path cannot be empty');
    }
    if (!quality) {
      throw new Error('Quality cannot be empty');
    }

    // Validate URL can be processed
    const downloadUrl = DownloadUrl.create(url);
    
    // Validate file path is secure
    FilePath.create(filePath);
    
    // Business rule: social media URLs require external tools
    if (downloadUrl.requiresExternalTool()) {
      // This would check external tool availability in a real implementation
      console.log('External tool validation required for:', url);
    }
  }

  static calculatePriority(download: Download): number {
    // Business logic for download priority
    // Higher numbers = higher priority
    let priority = 5; // Default priority

    // Prioritize by file size (smaller files first)
    const progress = download.getProgress();
    if (progress.totalBytes > 0) {
      if (progress.totalBytes < 10 * 1024 * 1024) { // < 10MB
        priority += 2;
      } else if (progress.totalBytes > 1024 * 1024 * 1024) { // > 1GB
        priority -= 1;
      }
    }

    // Prioritize by creation time (newer first)
    const hoursSinceCreation = (Date.now() - download.getCreatedAt().getTime()) / (1000 * 60 * 60);
    if (hoursSinceCreation < 1) {
      priority += 1;
    }

    return priority;
  }

  static canDownloadsConcurrently(downloads: Download[]): boolean {
    // Business rule: limit concurrent downloads
    const activeDownloads = downloads.filter(d => 
      d.getStatus() === 'downloading'
    );
    
    return activeDownloads.length < 3; // Max 3 concurrent downloads
  }
}
