import React, { useState, useEffect } from 'react';
import { Activity, Wifi, HardDrive, Download, FolderOpen, Settings, BarChart3, AlertCircle } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { colors, typography } from '../../design-system';
import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-shell';

interface SystemStatus {
  networkSpeed: 'fast' | 'medium' | 'slow';
  storageSpace: number;
  totalStorage: number;
  activeDownloads: number;
}

interface RecentActivity {
  id: string;
  type: 'download' | 'upload' | 'system';
  title: string;
  timestamp: string;
  status: 'completed' | 'active' | 'failed';
  icon?: React.ReactNode;
}

const RecentActivitySidebar: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    networkSpeed: 'fast',
    storageSpace: 890 * 1024 * 1024 * 1024, // 890 GB in bytes
    totalStorage: 1024 * 1024 * 1024 * 1024, // 1 TB in bytes
    activeDownloads: 0,
  });

  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [showNoActivity, setShowNoActivity] = useState(true);

  useEffect(() => {
    loadSystemStatus();
    loadRecentActivities();

    // Update every 5 seconds
    const interval = setInterval(() => {
      loadSystemStatus();
      loadRecentActivities();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const loadSystemStatus = async () => {
    try {
      // Get active downloads
      const activeDownloads = await invoke<any[]>('get_active_downloads');
      
      // Get system info (this would be a real API call)
      const systemInfo = await invoke<any>('get_system_info').catch(() => ({
        availableSpace: 890 * 1024 * 1024 * 1024,
        totalSpace: 1024 * 1024 * 1024 * 1024,
      }));

      // Calculate network speed based on active downloads
      const totalSpeed = activeDownloads.reduce((sum, d) => sum + (d.speed || 0), 0);
      const networkSpeed = totalSpeed > 10 * 1024 * 1024 ? 'fast' : 
                          totalSpeed > 1 * 1024 * 1024 ? 'medium' : 'slow';

      setSystemStatus({
        networkSpeed,
        storageSpace: systemInfo.availableSpace,
        totalStorage: systemInfo.totalSpace,
        activeDownloads: activeDownloads.length,
      });
    } catch (error) {
      console.error('Failed to load system status:', error);
    }
  };

  const loadRecentActivities = async () => {
    try {
      const downloads = await invoke<any[]>('get_downloads');
      
      // Convert downloads to activities
      const activities: RecentActivity[] = downloads
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
        .map(download => ({
          id: download.id,
          type: 'download',
          title: download.file_name || 'Unknown file',
          timestamp: download.created_at,
          status: download.status === 'completed' ? 'completed' : 
                 download.status === 'failed' ? 'failed' : 'active',
        }));

      setRecentActivities(activities);
      setShowNoActivity(activities.length === 0);
    } catch (error) {
      console.error('Failed to load recent activities:', error);
    }
  };

  const formatBytes = (bytes: number): string => {
    const gb = bytes / (1024 * 1024 * 1024);
    return `${gb.toFixed(0)} GB`;
  };

  const getNetworkSpeedColor = (speed: string) => {
    switch (speed) {
      case 'fast':
        return 'text-green-400';
      case 'medium':
        return 'text-yellow-400';
      case 'slow':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getActivityIcon = (activity: RecentActivity) => {
    if (activity.icon) return activity.icon;
    
    switch (activity.type) {
      case 'download':
        return <Download className="w-4 h-4" />;
      case 'upload':
        return <Activity className="w-4 h-4" />;
      case 'system':
        return <Settings className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400';
      case 'active':
        return 'text-blue-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  };

  const handleOpenDownloadsFolder = async () => {
    try {
      const downloadPath = await invoke<string>('get_download_path');
      await open(downloadPath);
    } catch (error) {
      console.error('Failed to open downloads folder:', error);
    }
  };

  const handleOpenPreferences = () => {
    // Open settings modal
    const event = new CustomEvent('open-settings');
    window.dispatchEvent(event);
  };

  const handleViewStatistics = () => {
    // Navigate to analytics tab
    const event = new CustomEvent('navigate-to-tab', { detail: 'analytics' });
    window.dispatchEvent(event);
  };

  const handleAddDownload = () => {
    // Switch to download tab
    const event = new CustomEvent('navigate-to-tab', { detail: 'download' });
    window.dispatchEvent(event);
  };

  return (
    <div className="h-full flex flex-col p-4 space-y-4">
      {/* Recent Activity */}
      <div className="flex-1">
        <h3 
          className="text-sm font-semibold text-gray-400 mb-3"
          style={{ fontFamily: typography.fonts.heading }}
        >
          Recent Activity
        </h3>
        
        {showNoActivity ? (
          <GlassCard variant="default" className="p-6 text-center">
            <AlertCircle className="w-12 h-12 text-gray-500 mx-auto mb-3" />
            <p className="text-gray-400 mb-2">No download activity yet</p>
            <AnimatedButton
              variant="primary"
              size="sm"
              onClick={handleAddDownload}
            >
              Add a download
            </AnimatedButton>
            <span className="text-gray-300 ml-2">to get started</span>
          </GlassCard>
        ) : (
          <div className="space-y-2">
            {recentActivities.map((activity) => (
              <div
                key={activity.id}
                className="glass-light rounded-lg p-3 hover:glass-medium transition-all duration-200"
              >
                <div className="flex items-start gap-3">
                  <div className={`mt-1 ${getActivityColor(activity.status)}`}>
                    {getActivityIcon(activity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-white truncate">{activity.title}</p>
                    <p className="text-xs text-gray-500">{formatTimestamp(activity.timestamp)}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* System Status */}
      <div>
        <GlassCard variant="interactive">
          <div className="p-4 space-y-3">
            <StatusItem
              icon={<Wifi className="w-4 h-4" />}
              label="Network Speed"
              value={systemStatus.networkSpeed.charAt(0).toUpperCase() + systemStatus.networkSpeed.slice(1)}
              valueColor={getNetworkSpeedColor(systemStatus.networkSpeed)}
            />
            <StatusItem
              icon={<HardDrive className="w-4 h-4" />}
              label="Storage Space"
              value={`${formatBytes(systemStatus.storageSpace)} Free`}
              valueColor="text-blue-400"
            />
            <StatusItem
              icon={<Download className="w-4 h-4" />}
              label="Active Downloads"
              value={`${systemStatus.activeDownloads} Running`}
              valueColor={systemStatus.activeDownloads > 0 ? 'text-green-400' : 'text-gray-400'}
            />
          </div>
        </GlassCard>
      </div>

      {/* Quick Actions */}
      <div className="space-y-2">
        <QuickActionButton
          icon={<FolderOpen className="w-4 h-4" />}
          label="Open Downloads Folder"
          onClick={handleOpenDownloadsFolder}
        />
        <QuickActionButton
          icon={<Settings className="w-4 h-4" />}
          label="Preferences"
          onClick={handleOpenPreferences}
        />
        <QuickActionButton
          icon={<BarChart3 className="w-4 h-4" />}
          label="View Statistics"
          onClick={handleViewStatistics}
        />
      </div>
    </div>
  );
};

const StatusItem: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: string;
  valueColor?: string;
}> = ({ icon, label, value, valueColor = 'text-white' }) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center gap-2">
      <div className="text-gray-400">{icon}</div>
      <span className="text-xs text-gray-400">{label}</span>
    </div>
    <span className={`text-xs font-medium ${valueColor}`}>{value}</span>
  </div>
);

const QuickActionButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
}> = ({ icon, label, onClick }) => (
  <button
    onClick={onClick}
    className="w-full flex items-center gap-3 p-3 glass-light rounded-lg hover:glass-medium transition-all duration-200 text-left"
  >
    <div className="text-gray-400">{icon}</div>
    <span className="text-sm text-gray-300">{label}</span>
  </button>
);

export default RecentActivitySidebar;