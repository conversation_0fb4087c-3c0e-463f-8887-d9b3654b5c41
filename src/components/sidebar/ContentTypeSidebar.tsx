import React, { useState, useEffect } from 'react';
import { FileVideo, Music, Image, FileText, Download, TrendingUp, HardDrive, CheckCircle } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import { colors, typography } from '../../design-system';
import { invoke } from '@tauri-apps/api/core';

interface ContentTypeStats {
  type: string;
  icon: React.ReactNode;
  label: string;
  description: string;
  count: number;
  size: number;
  isSelected: boolean;
}

interface SessionStats {
  downloads: number;
  dataSaved: number;
  successRate: number;
  avgSpeed: number;
}

const ContentTypeSidebar: React.FC = () => {
  const [contentTypes, setContentTypes] = useState<ContentTypeStats[]>([
    {
      type: 'video',
      icon: <FileVideo className="w-5 h-5" />,
      label: 'Video Content',
      description: 'HD/4K Video Downloads',
      count: 0,
      size: 0,
      isSelected: true,
    },
    {
      type: 'audio',
      icon: <Music className="w-5 h-5" />,
      label: 'Audio Files',
      description: 'Music & Podcasts',
      count: 0,
      size: 0,
      isSelected: false,
    },
    {
      type: 'image',
      icon: <Image className="w-5 h-5" />,
      label: 'Images',
      description: 'Photos & Graphics',
      count: 0,
      size: 0,
      isSelected: false,
    },
    {
      type: 'document',
      icon: <FileText className="w-5 h-5" />,
      label: 'Documents',
      description: 'PDFs & Files',
      count: 0,
      size: 0,
      isSelected: false,
    },
  ]);

  const [sessionStats, setSessionStats] = useState<SessionStats>({
    downloads: 0,
    dataSaved: 0,
    successRate: 0,
    avgSpeed: 0,
  });

  useEffect(() => {
    loadContentStats();
    loadSessionStats();

    // Update every 10 seconds
    const interval = setInterval(() => {
      loadContentStats();
      loadSessionStats();
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  const loadContentStats = async () => {
    try {
      const downloads = await invoke<any[]>('get_downloads');
      
      // Calculate stats by content type
      const stats = contentTypes.map(type => {
        const filtered = downloads.filter(d => {
          const ext = d.file_path?.split('.').pop()?.toLowerCase() || '';
          switch (type.type) {
            case 'video':
              return ['mp4', 'mkv', 'avi', 'mov', 'webm'].includes(ext);
            case 'audio':
              return ['mp3', 'wav', 'flac', 'aac', 'm4a'].includes(ext);
            case 'image':
              return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext);
            case 'document':
              return ['pdf', 'doc', 'docx', 'txt', 'epub'].includes(ext);
            default:
              return false;
          }
        });

        return {
          ...type,
          count: filtered.length,
          size: filtered.reduce((sum, d) => sum + (d.file_size || 0), 0),
        };
      });

      setContentTypes(stats);
    } catch (error) {
      console.error('Failed to load content stats:', error);
    }
  };

  const loadSessionStats = async () => {
    try {
      const downloads = await invoke<any[]>('get_downloads');
      const activeDownloads = await invoke<any[]>('get_active_downloads');
      
      // Calculate session stats
      const completed = downloads.filter(d => d.status === 'completed').length;
      const total = downloads.length;
      const successRate = total > 0 ? (completed / total) * 100 : 0;
      
      // Calculate total data saved
      const totalSize = downloads.reduce((sum, d) => sum + (d.file_size || 0), 0);
      
      // Calculate average speed from active downloads
      const avgSpeed = activeDownloads.reduce((sum, d) => sum + (d.speed || 0), 0) / 
                      (activeDownloads.length || 1);

      setSessionStats({
        downloads: downloads.length,
        dataSaved: totalSize,
        successRate,
        avgSpeed,
      });
    } catch (error) {
      console.error('Failed to load session stats:', error);
    }
  };

  const toggleContentType = (type: string) => {
    setContentTypes(prev =>
      prev.map(ct => ({
        ...ct,
        isSelected: ct.type === type ? !ct.isSelected : ct.isSelected,
      }))
    );
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    if (bytesPerSecond === 0) return '0 B/s';
    return `${formatBytes(bytesPerSecond)}/s`;
  };

  return (
    <div className="h-full flex flex-col p-4 space-y-4">
      {/* Content Type Filter */}
      <div>
        <h3 
          className="text-sm font-semibold text-gray-400 mb-3"
          style={{ fontFamily: typography.fonts.heading }}
        >
          Content Type
        </h3>
        <div className="space-y-2">
          {contentTypes.map((type) => (
            <div
              key={type.type}
              onClick={() => toggleContentType(type.type)}
              className={`
                relative cursor-pointer rounded-lg border transition-all duration-200
                ${type.isSelected 
                  ? 'glass-strong border-primary-500' 
                  : 'glass-light border-glass-border hover:glass-medium'
                }
              `}
            >
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div 
                      className={`p-2 rounded-lg ${type.isSelected ? 'bg-primary-500/20' : 'bg-glass-light'}`}
                    >
                      <div className={type.isSelected ? 'text-primary-400' : 'text-gray-400'}>
                        {type.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white">{type.label}</h4>
                      <p className="text-xs text-gray-400">{type.description}</p>
                    </div>
                  </div>
                  {type.isSelected && (
                    <CheckCircle className="w-4 h-4 text-primary-400" />
                  )}
                </div>
                {type.count > 0 && (
                  <div className="mt-2 pt-2 border-t border-glass-border">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">{type.count} files</span>
                      <span className="text-gray-400">{formatBytes(type.size)}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Session Statistics */}
      <div className="flex-1">
        <h3 
          className="text-sm font-semibold text-gray-400 mb-3"
          style={{ fontFamily: typography.fonts.heading }}
        >
          Session Statistics
        </h3>
        <GlassCard variant="default" className="h-full">
          <div className="p-4 space-y-4">
            <StatItem
              icon={<Download className="w-4 h-4" />}
              label="Downloads"
              value={sessionStats.downloads}
              color="text-blue-400"
            />
            <StatItem
              icon={<HardDrive className="w-4 h-4" />}
              label="Data Saved"
              value={formatBytes(sessionStats.dataSaved)}
              color="text-green-400"
            />
            <StatItem
              icon={<CheckCircle className="w-4 h-4" />}
              label="Success Rate"
              value={`${sessionStats.successRate.toFixed(0)}%`}
              color="text-purple-400"
            />
            <StatItem
              icon={<TrendingUp className="w-4 h-4" />}
              label="Avg Speed"
              value={formatSpeed(sessionStats.avgSpeed)}
              color="text-orange-400"
            />
          </div>
        </GlassCard>
      </div>
    </div>
  );
};

const StatItem: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: string | number;
  color: string;
}> = ({ icon, label, value, color }) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center gap-2">
      <div className={color}>{icon}</div>
      <span className="text-sm text-gray-400">{label}</span>
    </div>
    <span className={`text-sm font-medium ${color}`}>{value}</span>
  </div>
);

export default ContentTypeSidebar;