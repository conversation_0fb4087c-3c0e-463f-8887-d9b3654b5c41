import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DownloadList from './DownloadList';
import { useDownloadStore } from '../store/downloadStore';
import { createMockDownload } from '../test/utils';
import type { Download } from '../store/downloadStore';

// Mock the download store
vi.mock('../store/downloadStore', () => ({
  useDownloadStore: vi.fn(),
}));

// Mock DownloadItem component
vi.mock('./DownloadItem', () => ({
  default: ({ download }: { download: Download }) => (
    <div data-testid={`download-item-${download.id}`}>
      <span>{download.filename}</span>
      <span>{download.status}</span>
      {download.error && <span>{download.error}</span>}
    </div>
  ),
}));

describe('DownloadList Component', () => {
  const mockClearCompleted = vi.fn();
  let mockDownloads: Download[];

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockDownloads = [
      createMockDownload({
        id: '1',
        filename: 'video1.mp4',
        status: 'pending',
      }),
      createMockDownload({
        id: '2',
        filename: 'video2.mp4',
        status: 'downloading',
        progress: 50,
      }),
      createMockDownload({
        id: '3',
        filename: 'video3.mp4',
        status: 'completed',
      }),
    ];

    // Mock the download store
    vi.mocked(useDownloadStore).mockReturnValue({
      downloads: mockDownloads,
      clearCompleted: mockClearCompleted,
      removeDownload: vi.fn(),
      updateDownload: vi.fn(),
      addDownload: vi.fn(),
      setDefaultDownloadPath: vi.fn(),
      initializeDefaultPath: vi.fn(),
      defaultDownloadPath: '/Users/<USER>/Downloads',
      isInitialized: true,
    });
  });

  describe('Rendering', () => {
    it('should render all downloads', () => {
      render(<DownloadList />);

      expect(screen.getByTestId('download-item-1')).toBeInTheDocument();
      expect(screen.getByTestId('download-item-2')).toBeInTheDocument();
      expect(screen.getByTestId('download-item-3')).toBeInTheDocument();

      expect(screen.getByText('video1.mp4')).toBeInTheDocument();
      expect(screen.getByText('video2.mp4')).toBeInTheDocument();
      expect(screen.getByText('video3.mp4')).toBeInTheDocument();
    });

    it('should display download count in header', () => {
      render(<DownloadList />);

      expect(screen.getByText('Downloads (3)')).toBeInTheDocument();
    });

    it('should show clear completed button when there are completed downloads', () => {
      render(<DownloadList />);

      const clearButton = screen.getByText('Clear Completed (1)');
      expect(clearButton).toBeInTheDocument();
    });

    it('should not show clear completed button when no completed downloads', () => {
      const pendingDownloads = [
        createMockDownload({
          id: '1',
          filename: 'video1.mp4',
          status: 'pending',
        }),
        createMockDownload({
          id: '2',
          filename: 'video2.mp4',
          status: 'downloading',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: pendingDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.queryByText(/Clear Completed/)).not.toBeInTheDocument();
    });

    it('should show empty state when no downloads', () => {
      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: [],
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('No downloads yet')).toBeInTheDocument();
      expect(screen.getByText('Add a URL above to get started')).toBeInTheDocument();
    });
  });

  describe('Error Scenarios', () => {
    it('should display downloads with error status', () => {
      const downloadsWithErrors = [
        createMockDownload({
          id: '1',
          filename: 'failed-video.mp4',
          status: 'error',
          error: 'Download failed',
        }),
        createMockDownload({
          id: '2',
          filename: 'timeout-video.mp4',
          status: 'error',
          error: 'Network timeout',
        }),
        createMockDownload({
          id: '3',
          filename: 'missing-tool.mp4',
          status: 'error',
          error: 'yt-dlp not found',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: downloadsWithErrors,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('Download failed')).toBeInTheDocument();
      expect(screen.getByText('Network timeout')).toBeInTheDocument();
      expect(screen.getByText('yt-dlp not found')).toBeInTheDocument();
    });

    it('should handle mixed download states including errors', () => {
      const mixedDownloads = [
        createMockDownload({
          id: '1',
          filename: 'success.mp4',
          status: 'completed',
        }),
        createMockDownload({
          id: '2',
          filename: 'failed.mp4',
          status: 'error',
          error: 'Download failed',
        }),
        createMockDownload({
          id: '3',
          filename: 'pending.mp4',
          status: 'pending',
        }),
        createMockDownload({
          id: '4',
          filename: 'downloading.mp4',
          status: 'downloading',
          progress: 25,
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: mixedDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('Downloads (4)')).toBeInTheDocument();
      expect(screen.getByText('Clear Completed (1)')).toBeInTheDocument();
      expect(screen.getByText('success.mp4')).toBeInTheDocument();
      expect(screen.getByText('failed.mp4')).toBeInTheDocument();
      expect(screen.getByText('pending.mp4')).toBeInTheDocument();
      expect(screen.getByText('downloading.mp4')).toBeInTheDocument();
      expect(screen.getByText('Download failed')).toBeInTheDocument();
    });

    it('should handle downloads with unknown size/time errors', () => {
      const unknownDownloads = [
        createMockDownload({
          id: '1',
          filename: 'unknown-size.mp4',
          status: 'downloading',
          progress: 0,
          fileSize: undefined,
          downloadSpeed: undefined,
          timeRemaining: undefined,
        }),
        createMockDownload({
          id: '2',
          filename: 'size-failed.mp4',
          status: 'error',
          error: 'Unknown size • Unknown time',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: unknownDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('unknown-size.mp4')).toBeInTheDocument();
      expect(screen.getByText('size-failed.mp4')).toBeInTheDocument();
      expect(screen.getByText('Unknown size • Unknown time')).toBeInTheDocument();
    });

    it('should display only error downloads when all downloads have failed', () => {
      const errorDownloads = [
        createMockDownload({
          id: '1',
          filename: 'error1.mp4',
          status: 'error',
          error: 'Download failed',
        }),
        createMockDownload({
          id: '2',
          filename: 'error2.mp4',
          status: 'error',
          error: 'Network timeout',
        }),
        createMockDownload({
          id: '3',
          filename: 'error3.mp4',
          status: 'error',
          error: 'Permission denied',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: errorDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('Downloads (3)')).toBeInTheDocument();
      expect(screen.queryByText(/Clear Completed/)).not.toBeInTheDocument();
      expect(screen.getByText('Download failed')).toBeInTheDocument();
      expect(screen.getByText('Network timeout')).toBeInTheDocument();
      expect(screen.getByText('Permission denied')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call clearCompleted when clear button is clicked', async () => {
      const user = userEvent.setup();
      render(<DownloadList />);

      const clearButton = screen.getByText('Clear Completed (1)');
      await user.click(clearButton);

      expect(mockClearCompleted).toHaveBeenCalledTimes(1);
    });

    it('should update completed count correctly', () => {
      const multipleCompletedDownloads = [
        createMockDownload({
          id: '1',
          filename: 'completed1.mp4',
          status: 'completed',
        }),
        createMockDownload({
          id: '2',
          filename: 'completed2.mp4',
          status: 'completed',
        }),
        createMockDownload({
          id: '3',
          filename: 'completed3.mp4',
          status: 'completed',
        }),
        createMockDownload({
          id: '4',
          filename: 'pending.mp4',
          status: 'pending',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: multipleCompletedDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('Downloads (4)')).toBeInTheDocument();
      expect(screen.getByText('Clear Completed (3)')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty downloads array gracefully', () => {
      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: [],
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText('No downloads yet')).toBeInTheDocument();
      expect(screen.queryByText(/Downloads \(/)).not.toBeInTheDocument();
    });

    it('should handle downloads with missing data gracefully', () => {
      const incompleteDownloads = [
        createMockDownload({
          id: '1',
          filename: '',
          status: 'pending',
        }),
        createMockDownload({
          id: '2',
          filename: 'normal.mp4',
          status: 'downloading',
          progress: undefined as any,
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: incompleteDownloads,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      expect(() => render(<DownloadList />)).not.toThrow();
      expect(screen.getByText('Downloads (2)')).toBeInTheDocument();
    });

    it('should handle very long error messages', () => {
      const longErrorDownload = [
        createMockDownload({
          id: '1',
          filename: 'long-error.mp4',
          status: 'error',
          error: 'This is a very long error message that might occur when there are multiple issues with the download process including network connectivity problems, server errors, authentication failures, and various other issues that could cause the download to fail completely',
        }),
      ];

      vi.mocked(useDownloadStore).mockReturnValue({
        downloads: longErrorDownload,
        clearCompleted: mockClearCompleted,
        removeDownload: vi.fn(),
        updateDownload: vi.fn(),
        addDownload: vi.fn(),
        setDefaultDownloadPath: vi.fn(),
        initializeDefaultPath: vi.fn(),
        defaultDownloadPath: '/Users/<USER>/Downloads',
        isInitialized: true,
      });

      render(<DownloadList />);

      expect(screen.getByText(/This is a very long error message/)).toBeInTheDocument();
    });
  });
});
