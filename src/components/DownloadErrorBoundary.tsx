import { Component, ReactNode, ErrorInfo } from 'react';
import { <PERSON>ertCircle, RefreshCw, ExternalLink, Info } from 'lucide-react';
import { AppError, ErrorCode, ErrorSeverity, createAppError, isAppError } from '../types/errors';
import { getErrorReportingService } from '../services/errorReporting';

interface Props {
  children: ReactNode;
  onRetry?: () => void;
  onError?: (error: AppError) => void;
  downloadId?: string;
  downloadUrl?: string;
}

interface State {
  hasError: boolean;
  appError: AppError | null;
  showDetails: boolean;
}

class DownloadErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, appError: null, showDetails: false };
  }

  static getDerivedStateFromError(_error: Error): Partial<State> {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Download error boundary caught an error:', error, errorInfo);
    
    // Convert to AppError if it's not already one
    let appError: AppError;
    
    if (isAppError(error)) {
      appError = error;
    } else {
      // Try to determine the appropriate error type based on the error message
      let errorCode = ErrorCode.DOWNLOAD_FAILED;
      
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout')) {
        errorCode = ErrorCode.NETWORK_TIMEOUT;
      } else if (errorMessage.includes('permission') || errorMessage.includes('access')) {
        errorCode = ErrorCode.PERMISSION_DENIED;
      } else if (errorMessage.includes('space') || errorMessage.includes('disk')) {
        errorCode = ErrorCode.DISK_FULL;
      } else if (errorMessage.includes('path') || errorMessage.includes('directory')) {
        errorCode = ErrorCode.INVALID_PATH;
      }
      
      appError = createAppError(error, {
        component: 'DownloadErrorBoundary',
        action: 'download_operation',
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        additionalData: {
          downloadId: this.props.downloadId,
          downloadUrl: this.props.downloadUrl,
          componentStack: errorInfo.componentStack
        }
      }, errorCode);
    }

    this.setState({ appError });

    // Report the error
    this.reportError(appError);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(appError);
    }
  }

  private async reportError(error: AppError) {
    try {
      const errorReporting = getErrorReportingService();
      await errorReporting.reportError(error);
    } catch (reportingError) {
      console.warn('Failed to report download error:', reportingError);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, appError: null, showDetails: false });
    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  handleHelpLink = () => {
    if (this.state.appError?.helpUrl) {
      window.open(this.state.appError.helpUrl, '_blank');
    }
  };

  executeRecoveryAction = async (action: () => void | Promise<void>) => {
    try {
      await action();
      // If recovery action succeeds, reset the error state
      this.setState({ hasError: false, appError: null, showDetails: false });
    } catch (recoveryError) {
      console.error('Recovery action failed:', recoveryError);
    }
  };

  render() {
    if (this.state.hasError && this.state.appError) {
      const { appError } = this.state;
      
      const severityColors = {
        [ErrorSeverity.LOW]: 'bg-blue-50 border-blue-200 text-blue-800',
        [ErrorSeverity.MEDIUM]: 'bg-yellow-50 border-yellow-200 text-yellow-800',
        [ErrorSeverity.HIGH]: 'bg-red-50 border-red-200 text-red-800',
        [ErrorSeverity.CRITICAL]: 'bg-red-100 border-red-300 text-red-900'
      };
      
      const iconColors = {
        [ErrorSeverity.LOW]: 'text-blue-500',
        [ErrorSeverity.MEDIUM]: 'text-yellow-500',
        [ErrorSeverity.HIGH]: 'text-red-500',
        [ErrorSeverity.CRITICAL]: 'text-red-600'
      };

      return (
        <div className={`border rounded-lg p-4 ${severityColors[appError.severity]}`}>
          <div className="flex items-start">
            <AlertCircle className={`h-5 w-5 mr-3 mt-0.5 ${iconColors[appError.severity]}`} />
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">
                  Download Error
                  {appError.code !== ErrorCode.DOWNLOAD_FAILED && (
                    <span className="ml-2 text-xs font-mono bg-white bg-opacity-50 px-1.5 py-0.5 rounded">
                      {appError.code}
                    </span>
                  )}
                </h3>
                {(appError.helpUrl || this.state.showDetails) && (
                  <div className="flex items-center space-x-2">
                    {appError.helpUrl && (
                      <button
                        onClick={this.handleHelpLink}
                        className="text-xs hover:underline flex items-center"
                        title="Get help with this error"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        Help
                      </button>
                    )}
                    <button
                      onClick={this.toggleDetails}
                      className="text-xs hover:underline flex items-center"
                      title="Toggle error details"
                    >
                      <Info className="h-3 w-3 mr-1" />
                      {this.state.showDetails ? 'Hide' : 'Details'}
                    </button>
                  </div>
                )}
              </div>
              
              <p className="text-sm mt-1">
                {appError.userMessage}
              </p>

              {/* Recovery Actions */}
              {appError.recoveryActions && appError.recoveryActions.length > 0 && (
                <div className="mt-3">
                  <div className="flex flex-wrap gap-2">
                    {appError.recoveryActions.map((action, index) => (
                      <button
                        key={index}
                        onClick={() => this.executeRecoveryAction(action.action)}
                        className={`text-xs px-2 py-1 rounded border ${
                          action.primary
                            ? 'bg-white bg-opacity-80 border-current font-medium'
                            : 'bg-white bg-opacity-50 border-current'
                        }`}
                      >
                        {action.label}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Error Details */}
              {this.state.showDetails && (
                <div className="mt-3 p-3 bg-white bg-opacity-50 rounded text-xs">
                  <div className="space-y-1">
                    <div><strong>Severity:</strong> {appError.severity}</div>
                    <div><strong>Retryable:</strong> {appError.retryable ? 'Yes' : 'No'}</div>
                    <div><strong>Technical:</strong> {appError.technicalMessage}</div>
                    {this.props.downloadId && (
                      <div><strong>Download ID:</strong> {this.props.downloadId}</div>
                    )}
                    {this.props.downloadUrl && (
                      <div><strong>URL:</strong> <span className="break-all">{this.props.downloadUrl}</span></div>
                    )}
                    {appError.context.additionalData && (
                      <div>
                        <strong>Context:</strong>
                        <pre className="mt-1 text-xs overflow-auto">
                          {JSON.stringify(appError.context.additionalData, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-2 ml-3">
              {appError.retryable && (
                <button
                  onClick={this.handleRetry}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded bg-white bg-opacity-80 hover:bg-opacity-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current"
                  title="Retry the download"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Retry
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default DownloadErrorBoundary;