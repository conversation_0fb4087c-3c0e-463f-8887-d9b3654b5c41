import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { 
  diagnoseDownloadFolder, 
  testDownloadFunctionality, 
  setupDownloadFolder,
  exportDiagnosticInfo,
  DownloadFolderDiagnostic 
} from '../utils/downloadFolderTest';
import { openFolderInExplorer, selectDownloadFolder } from '../utils/folderUtils';
import { updateConfig } from '../config/configManager';
import AnimatedButton from './ui/AnimatedButton';
import { typography } from '../design-system';

interface DownloadFolderDiagnosticProps {
  isOpen: boolean;
  onClose: () => void;
  onFolderFixed?: (path: string) => void;
}

const DownloadFolderDiagnosticComponent: React.FC<DownloadFolderDiagnosticProps> = ({ 
  isOpen, 
  onClose, 
  onFolderFixed 
}) => {
  const [diagnostic, setDiagnostic] = useState<DownloadFolderDiagnostic | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isFixing, setIsFixing] = useState(false);

  useEffect(() => {
    if (isOpen) {
      runDiagnostic();
    }
  }, [isOpen]);

  const runDiagnostic = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🔍 Running download folder diagnostic...');
      const result = await diagnoseDownloadFolder();
      setDiagnostic(result);
      
      if (result.status === 'success') {
        toast.success('Download folder is working correctly!');
      } else if (result.status === 'warning') {
        toast.warning(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      toast.error('Failed to run diagnostic');
    } finally {
      setIsRunning(false);
    }
  };

  const runFullTest = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      console.log('🧪 Running full download functionality test...');
      const result = await testDownloadFunctionality();
      setTestResults(result.details);
      
      if (result.success) {
        toast.success('All download functionality tests passed!');
        if (onFolderFixed) {
          onFolderFixed(result.path);
        }
      } else {
        toast.error('Download functionality test failed');
      }
    } catch (error) {
      console.error('❌ Test failed:', error);
      toast.error('Failed to run test');
    } finally {
      setIsRunning(false);
    }
  };

  const fixDownloadFolder = async () => {
    setIsFixing(true);
    
    try {
      console.log('🔧 Attempting to fix download folder...');
      const fixedPath = await setupDownloadFolder();
      
      // Update configuration with the fixed path
      await updateConfig({
        paths: {
          defaultDownloadDir: fixedPath,
          configDir: '',
          logDir: '',
          tempDir: '',
          pluginDir: ''
        }
      });
      
      toast.success(`Download folder fixed: ${fixedPath}`);
      
      if (onFolderFixed) {
        onFolderFixed(fixedPath);
      }
      
      // Re-run diagnostic to confirm fix
      await runDiagnostic();
    } catch (error) {
      console.error('❌ Fix failed:', error);
      toast.error('Failed to fix download folder');
    } finally {
      setIsFixing(false);
    }
  };

  const selectNewFolder = async () => {
    try {
      const selectedPath = await selectDownloadFolder();
      if (selectedPath) {
        // Update configuration
        await updateConfig({
          paths: {
            defaultDownloadDir: selectedPath,
            configDir: '',
            logDir: '',
            tempDir: '',
            pluginDir: ''
          }
        });
        
        toast.success(`Download folder updated: ${selectedPath}`);
        
        if (onFolderFixed) {
          onFolderFixed(selectedPath);
        }
        
        // Re-run diagnostic
        await runDiagnostic();
      }
    } catch (error) {
      console.error('❌ Folder selection failed:', error);
      toast.error('Failed to select folder');
    }
  };

  const openCurrentFolder = async () => {
    if (diagnostic?.path) {
      try {
        await openFolderInExplorer(diagnostic.path);
      } catch (error) {
        console.error('❌ Failed to open folder:', error);
        toast.error('Failed to open folder');
      }
    }
  };

  const exportDiagnostic = async () => {
    try {
      const diagnosticInfo = await exportDiagnosticInfo();
      
      // Create and download the diagnostic file
      const blob = new Blob([diagnosticInfo], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `flowdownload-diagnostic-${new Date().toISOString().slice(0, 19)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Diagnostic information exported');
    } catch (error) {
      console.error('❌ Export failed:', error);
      toast.error('Failed to export diagnostic');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fade-in">
      <div className="glass-strong rounded-2xl shadow-glass-xl w-4/5 h-4/5 max-w-4xl max-h-[90vh] flex flex-col animate-scale-in border border-glass-border">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-glass-border">
          <h2 
            className="text-2xl font-bold text-white"
            style={{ fontFamily: typography.fonts.heading }}
          >
            Download Folder Diagnostic
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-2 glass-light hover:glass-medium rounded-lg transition-all hover:scale-105"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Current Status */}
          {diagnostic && (
            <div className={`p-4 rounded-lg mb-6 glass-medium border animate-slide-up ${
              diagnostic.status === 'success' ? 'border-green-400/30' :
              diagnostic.status === 'warning' ? 'border-yellow-400/30' :
              'border-red-400/30'
            }`}>
              <div className="flex items-center mb-2">
                <div className={`w-3 h-3 rounded-full mr-3 animate-pulse ${
                  diagnostic.status === 'success' ? 'bg-green-400' :
                  diagnostic.status === 'warning' ? 'bg-yellow-400' :
                  'bg-red-400'
                }`}></div>
                <h3 className="font-semibold text-white">
                  {diagnostic.status === 'success' ? 'Working' :
                   diagnostic.status === 'warning' ? 'Warning' : 'Error'}
                </h3>
              </div>
              <p className="text-gray-300 mb-2">{diagnostic.message}</p>
              <div className="text-sm text-gray-400 space-y-1">
                <p><span className="text-gray-500">Path:</span> <span className="text-white">{diagnostic.path}</span></p>
                <p><span className="text-gray-500">Exists:</span> <span className="text-white">{diagnostic.exists ? 'Yes' : 'No'}</span></p>
                <p><span className="text-gray-500">Writable:</span> <span className="text-white">{diagnostic.canWrite ? 'Yes' : 'No'}</span></p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <AnimatedButton
              onClick={runDiagnostic}
              disabled={isRunning}
              variant="primary"
              loading={isRunning}
              glow
            >
              {isRunning ? 'Running...' : 'Run Diagnostic'}
            </AnimatedButton>
            
            <AnimatedButton
              onClick={runFullTest}
              disabled={isRunning}
              variant="accent"
              glow
            >
              Full Test
            </AnimatedButton>
            
            <AnimatedButton
              onClick={fixDownloadFolder}
              disabled={isFixing || isRunning}
              variant="success"
              loading={isFixing}
              glow
            >
              {isFixing ? 'Fixing...' : 'Auto Fix'}
            </AnimatedButton>
            
            <AnimatedButton
              onClick={selectNewFolder}
              disabled={isRunning}
              variant="secondary"
              className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
            >
              Select Folder
            </AnimatedButton>
          </div>

          {/* Additional Actions */}
          <div className="flex gap-4 mb-6">
            <AnimatedButton
              onClick={openCurrentFolder}
              disabled={!diagnostic?.path || isRunning}
              variant="secondary"
            >
              Open Folder
            </AnimatedButton>
            
            <AnimatedButton
              onClick={exportDiagnostic}
              variant="primary"
              glow
            >
              Export Diagnostic
            </AnimatedButton>
          </div>

          {/* Suggestions */}
          {diagnostic?.suggestions && diagnostic.suggestions.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-white mb-3">
                Suggested Alternative Paths:
              </h4>
              <div className="space-y-2">
                {diagnostic.suggestions.map((suggestion, index) => (
                  <div key={index} className="p-3 glass-medium rounded-lg border border-glass-border">
                    <code className="text-sm text-primary-300">{suggestion}</code>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Test Results */}
          {testResults.length > 0 && (
            <div>
              <h4 className="font-semibold text-white mb-3">
                Test Results:
              </h4>
              <div className="glass-medium rounded-lg p-4 max-h-64 overflow-y-auto border border-glass-border">
                {testResults.map((result, index) => (
                  <div key={index} className="text-sm text-gray-300 mb-1">
                    {result}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end items-center p-6 border-t border-glass-border glass-light">
          <AnimatedButton
            onClick={onClose}
            variant="secondary"
          >
            Close
          </AnimatedButton>
        </div>
      </div>
    </div>
  );
};

export default DownloadFolderDiagnosticComponent;
