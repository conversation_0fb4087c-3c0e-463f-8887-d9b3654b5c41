import { useState, useEffect } from 'react';
import { writeTextFile, readTextFile, BaseDirectory } from '@tauri-apps/plugin-fs';
import { toast } from 'react-toastify';
import { Key, Save, Eye, EyeOff } from 'lucide-react';
import AnimatedButton from './ui/AnimatedButton';
import { typography } from '../design-system';

interface ApiKeys {
  youtube_client_id: string;
  youtube_client_secret: string;
  tiktok_client_key: string;
  tiktok_client_secret: string;
}

export default function ApiKeyConfig() {
  const [keys, setKeys] = useState<ApiKeys>({
    youtube_client_id: '',
    youtube_client_secret: '',
    tiktok_client_key: '',
    tiktok_client_secret: ''
  });
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadKeys();
  }, []);

  const loadKeys = async () => {
    try {
      const content = await readTextFile('.flowdownload/api_keys.json', {
        baseDir: BaseDirectory.AppConfig
      });
      const savedKeys = JSON.parse(content);
      setKeys(savedKeys);
    } catch (error) {
      // File doesn't exist yet, that's okay
      console.log('No saved API keys found');
    }
  };

  const saveKeys = async () => {
    try {
      setIsLoading(true);
      
      // Save to app config directory
      await writeTextFile('.flowdownload/api_keys.json', JSON.stringify(keys, null, 2), {
        baseDir: BaseDirectory.AppConfig
      });
      
      // Also create an .env file template
      const envContent = `# FlowDownload API Keys
# Add these to your system environment variables or .env file

# YouTube API - Get from https://console.cloud.google.com/
YOUTUBE_CLIENT_ID=${keys.youtube_client_id}
YOUTUBE_CLIENT_SECRET=${keys.youtube_client_secret}

# TikTok API - Get from https://developers.tiktok.com/
TIKTOK_CLIENT_KEY=${keys.tiktok_client_key}
TIKTOK_CLIENT_SECRET=${keys.tiktok_client_secret}
`;
      
      await writeTextFile('.flowdownload/.env.template', envContent, {
        baseDir: BaseDirectory.AppConfig
      });
      
      toast.success('API keys saved successfully!');
      toast.info('Remember to set these as environment variables before running the app');
    } catch (error) {
      console.error('Error saving keys:', error);
      toast.error('Failed to save API keys');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleShowSecret = (field: string) => {
    setShowSecrets(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const handleChange = (field: keyof ApiKeys, value: string) => {
    setKeys(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="glass-medium rounded-lg border border-glass-border p-6">
        <h2 
          className="text-2xl font-bold mb-6 flex items-center gap-2 text-white"
          style={{ fontFamily: typography.fonts.heading }}
        >
          <Key className="w-6 h-6 text-primary-400" />
          API Key Configuration
        </h2>
        
        <div className="space-y-6">
          {/* YouTube API Keys */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-red-400">YouTube API</h3>
            <p className="text-sm text-gray-400 mb-4">
              Get your API credentials from the{' '}
              <a 
                href="https://console.cloud.google.com/apis/credentials" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary-400 hover:text-primary-300 underline"
              >
                Google Cloud Console
              </a>
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Client ID</label>
                <input
                  type="text"
                  value={keys.youtube_client_id}
                  onChange={(e) => handleChange('youtube_client_id', e.target.value)}
                  className="w-full px-4 py-3 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 bg-transparent focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter YouTube Client ID"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Client Secret</label>
                <div className="relative">
                  <input
                    type={showSecrets.youtube_secret ? 'text' : 'password'}
                    value={keys.youtube_client_secret}
                    onChange={(e) => handleChange('youtube_client_secret', e.target.value)}
                    className="w-full px-4 py-3 pr-12 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 bg-transparent focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter YouTube Client Secret"
                  />
                  <button
                    onClick={() => toggleShowSecret('youtube_secret')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white p-2 rounded hover:bg-glass-medium transition-colors"
                  >
                    {showSecrets.youtube_secret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* TikTok API Keys */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-white">TikTok API</h3>
            <p className="text-sm text-gray-400 mb-4">
              Get your API credentials from the{' '}
              <a 
                href="https://developers.tiktok.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary-400 hover:text-primary-300 underline"
              >
                TikTok Developers Portal
              </a>
            </p>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Client Key</label>
                <input
                  type="text"
                  value={keys.tiktok_client_key}
                  onChange={(e) => handleChange('tiktok_client_key', e.target.value)}
                  className="w-full px-4 py-3 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 bg-transparent focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter TikTok Client Key"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-300">Client Secret</label>
                <div className="relative">
                  <input
                    type={showSecrets.tiktok_secret ? 'text' : 'password'}
                    value={keys.tiktok_client_secret}
                    onChange={(e) => handleChange('tiktok_client_secret', e.target.value)}
                    className="w-full px-4 py-3 pr-12 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 bg-transparent focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter TikTok Client Secret"
                  />
                  <button
                    onClick={() => toggleShowSecret('tiktok_secret')}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white p-2 rounded hover:bg-glass-medium transition-colors"
                  >
                    {showSecrets.tiktok_secret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="pt-4">
            <AnimatedButton
              onClick={saveKeys}
              disabled={isLoading}
              variant="primary"
              fullWidth
              loading={isLoading}
              icon={<Save className="w-4 h-4" />}
            >
              {isLoading ? 'Saving...' : 'Save Configuration'}
            </AnimatedButton>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 glass-light rounded-lg border border-yellow-500/30">
            <h4 className="font-medium mb-2 text-yellow-400">Important Notes:</h4>
            <ul className="text-sm space-y-1 list-disc list-inside text-gray-300">
              <li>These keys are saved locally in your app configuration</li>
              <li>For production use, set them as environment variables</li>
              <li>Never commit API keys to version control</li>
              <li>Make sure to enable YouTube Data API v3 in Google Cloud Console</li>
              <li>TikTok requires business verification for production access</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}