import React, { ReactNode } from 'react';
import * as Sen<PERSON> from '@sentry/react';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import AnimatedButton from './ui/AnimatedButton';

interface Props {
  children: ReactNode;
  showDialog?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
}

const SentryErrorBoundary: React.FC<Props> = ({ children, showDialog = true }) => {
  const navigate = useNavigate();

  return (
    <Sentry.ErrorBoundary
      fallback={({ error, resetError }) => (
        <div className="min-h-screen flex items-center justify-center bg-gray-900 p-4">
          <div className="max-w-md w-full">
            <div className="bg-gray-800 rounded-lg shadow-xl p-6 border border-gray-700">
              <div className="flex items-center mb-4">
                <AlertCircle className="w-8 h-8 text-red-500 mr-3" />
                <h1 className="text-xl font-bold text-white">Something went wrong</h1>
              </div>
              
              <p className="text-gray-300 mb-4">
                We apologize for the inconvenience. An error has occurred and has been reported to our team.
              </p>
              
              {error && (
                <details className="mb-4">
                  <summary className="cursor-pointer text-sm text-gray-400 hover:text-gray-300">
                    Technical details
                  </summary>
                  <pre className="mt-2 text-xs text-gray-500 bg-gray-900 p-2 rounded overflow-auto">
                    {error.toString()}
                    {error.stack && '\n\n' + error.stack}
                  </pre>
                </details>
              )}
              
              <div className="flex gap-3">
                <AnimatedButton
                  onClick={resetError}
                  variant="primary"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Try Again
                </AnimatedButton>
                
                <AnimatedButton
                  onClick={() => {
                    resetError();
                    navigate('/');
                  }}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  <Home className="w-4 h-4" />
                  Go Home
                </AnimatedButton>
              </div>
            </div>
          </div>
        </div>
      )}
      showDialog={showDialog}
    >
      {children}
    </Sentry.ErrorBoundary>
  );
};

export default SentryErrorBoundary;