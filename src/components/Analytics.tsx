import React, { useState, useEffect } from 'react';
import { Tren<PERSON>Up, <PERSON><PERSON>hart3, Pie<PERSON>hart, Activity, Calendar, Globe, Users, Eye, Heart, MessageSquare, Share2, Download } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { colors, typography } from '../design-system';
import { invoke } from '@tauri-apps/api/core';

interface PlatformMetrics {
  platform: string;
  views: number;
  engagement: number;
  followers: number;
  revenue: number;
  topContent: string[];
}

interface ContentPerformance {
  id: string;
  title: string;
  platform: string;
  views: number;
  likes: number;
  comments: number;
  shares: number;
  uploadDate: string;
  duration: number;
}

interface TimeSeriesData {
  date: string;
  views: number;
  engagement: number;
  uploads: number;
}

interface AnalyticsData {
  totalViews: number;
  totalEngagement: number;
  totalRevenue: number;
  totalFollowers: number;
  platformMetrics: PlatformMetrics[];
  contentPerformance: ContentPerformance[];
  timeSeriesData: TimeSeriesData[];
  growthRate: {
    views: number;
    engagement: number;
    followers: number;
    revenue: number;
  };
}

const Analytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalViews: 0,
    totalEngagement: 0,
    totalRevenue: 0,
    totalFollowers: 0,
    platformMetrics: [],
    contentPerformance: [],
    timeSeriesData: [],
    growthRate: {
      views: 0,
      engagement: 0,
      followers: 0,
      revenue: 0,
    },
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalytics();
  }, [timeRange, selectedPlatform]);

  const loadAnalytics = async () => {
    setIsLoading(true);
    try {
      // In a real app, this would fetch from backend
      // For now, we'll simulate with empty data
      const data = await invoke<AnalyticsData>('get_analytics', {
        timeRange,
        platform: selectedPlatform,
      }).catch(() => ({
        totalViews: 0,
        totalEngagement: 0,
        totalRevenue: 0,
        totalFollowers: 0,
        platformMetrics: [],
        contentPerformance: [],
        timeSeriesData: [],
        growthRate: {
          views: 0,
          engagement: 0,
          followers: 0,
          revenue: 0,
        },
      }));
      
      setAnalyticsData(data);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const MetricCard: React.FC<{
    icon: React.ReactNode;
    label: string;
    value: string | number;
    growth?: number;
    color: string;
  }> = ({ icon, label, value, growth, color }) => (
    <GlassCard variant="interactive">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div 
            className="p-3 rounded-lg"
            style={{ background: color }}
          >
            {icon}
          </div>
          {growth !== undefined && growth !== 0 && (
            <div className={`flex items-center gap-1 text-sm ${growth > 0 ? 'text-green-400' : 'text-red-400'}`}>
              <TrendingUp className={`w-4 h-4 ${growth < 0 ? 'rotate-180' : ''}`} />
              <span>{Math.abs(growth)}%</span>
            </div>
          )}
        </div>
        <h3 className="text-3xl font-bold text-white mb-1" style={{ fontFamily: typography.fonts.heading }}>
          {value}
        </h3>
        <p className="text-sm text-gray-400">{label}</p>
      </div>
    </GlassCard>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 
            className="text-3xl font-bold text-white mb-2"
            style={{ fontFamily: typography.fonts.heading }}
          >
            Analytics Dashboard
          </h1>
          <p className="text-gray-400">Track your content performance across all platforms</p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex items-center gap-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <AnimatedButton
              key={range}
              variant={timeRange === range ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range === '7d' ? '7 Days' : 
               range === '30d' ? '30 Days' : 
               range === '90d' ? '90 Days' : '1 Year'}
            </AnimatedButton>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          icon={<Eye className="w-6 h-6 text-white" />}
          label="Total Views"
          value={formatNumber(analyticsData.totalViews)}
          growth={analyticsData.growthRate.views}
          color={colors.primary.gradient}
        />
        <MetricCard
          icon={<Activity className="w-6 h-6 text-white" />}
          label="Engagement Rate"
          value={analyticsData.totalEngagement > 0 ? `${analyticsData.totalEngagement.toFixed(1)}%` : '0%'}
          growth={analyticsData.growthRate.engagement}
          color={colors.accent.gradient}
        />
        <MetricCard
          icon={<Users className="w-6 h-6 text-white" />}
          label="Total Followers"
          value={formatNumber(analyticsData.totalFollowers)}
          growth={analyticsData.growthRate.followers}
          color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        />
        <MetricCard
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          label="Revenue"
          value={formatCurrency(analyticsData.totalRevenue)}
          growth={analyticsData.growthRate.revenue}
          color="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
        />
      </div>

      {/* Platform Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <GlassCard variant="default">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
              Platform Performance
            </h3>
            {analyticsData.platformMetrics.length > 0 ? (
              <div className="space-y-4">
                {analyticsData.platformMetrics.map((platform) => (
                  <div key={platform.platform} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-white">{platform.platform}</span>
                      <span className="text-sm text-gray-400">{formatNumber(platform.views)} views</span>
                    </div>
                    <div className="w-full h-2 bg-glass-light rounded-full overflow-hidden">
                      <div 
                        className="h-full rounded-full transition-all duration-500"
                        style={{ 
                          width: `${(platform.views / Math.max(...analyticsData.platformMetrics.map(p => p.views))) * 100}%`,
                          background: colors.primary.gradient
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <PieChart className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No platform data available</p>
                <p className="text-xs mt-1">Connect your accounts to see analytics</p>
              </div>
            )}
          </div>
        </GlassCard>

        <GlassCard variant="default">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
              Engagement Breakdown
            </h3>
            {analyticsData.contentPerformance.length > 0 ? (
              <div className="space-y-4">
                <EngagementMetric 
                  icon={<Heart className="w-4 h-4" />} 
                  label="Likes" 
                  value={analyticsData.contentPerformance.reduce((sum, c) => sum + c.likes, 0)} 
                />
                <EngagementMetric 
                  icon={<MessageSquare className="w-4 h-4" />} 
                  label="Comments" 
                  value={analyticsData.contentPerformance.reduce((sum, c) => sum + c.comments, 0)} 
                />
                <EngagementMetric 
                  icon={<Share2 className="w-4 h-4" />} 
                  label="Shares" 
                  value={analyticsData.contentPerformance.reduce((sum, c) => sum + c.shares, 0)} 
                />
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Heart className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p className="text-sm">No engagement data available</p>
                <p className="text-xs mt-1">Upload content to track engagement</p>
              </div>
            )}
          </div>
        </GlassCard>
      </div>

      {/* Content Performance Table */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white" style={{ fontFamily: typography.fonts.heading }}>
              Top Performing Content
            </h3>
            <AnimatedButton
              variant="secondary"
              size="sm"
              icon={<Download className="w-4 h-4" />}
              onClick={() => {
                // Export analytics data
                console.log('Exporting analytics...');
              }}
            >
              Export
            </AnimatedButton>
          </div>
          
          {analyticsData.contentPerformance.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-glass-border">
                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-400">Content</th>
                    <th className="text-left py-3 px-4 text-sm font-medium text-gray-400">Platform</th>
                    <th className="text-right py-3 px-4 text-sm font-medium text-gray-400">Views</th>
                    <th className="text-right py-3 px-4 text-sm font-medium text-gray-400">Engagement</th>
                    <th className="text-right py-3 px-4 text-sm font-medium text-gray-400">Upload Date</th>
                  </tr>
                </thead>
                <tbody>
                  {analyticsData.contentPerformance.slice(0, 5).map((content) => (
                    <tr key={content.id} className="border-b border-glass-border hover:bg-glass-light transition-colors">
                      <td className="py-3 px-4">
                        <p className="text-sm font-medium text-white">{content.title}</p>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm text-gray-400">{content.platform}</span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <span className="text-sm text-white">{formatNumber(content.views)}</span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <span className="text-sm text-gray-400">
                          {formatNumber(content.likes + content.comments + content.shares)}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-right">
                        <span className="text-sm text-gray-400">
                          {new Date(content.uploadDate).toLocaleDateString()}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg mb-2">No content data available</p>
              <p className="text-sm">Start uploading content to see performance metrics</p>
            </div>
          )}
        </div>
      </GlassCard>

      {/* Growth Chart Placeholder */}
      <GlassCard variant="default">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
            Growth Trend
          </h3>
          <div className="text-center py-16 text-gray-500">
            <TrendingUp className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">Chart visualization coming soon</p>
            <p className="text-sm">Track your growth over time</p>
          </div>
        </div>
      </GlassCard>
    </div>
  );
};

const EngagementMetric: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: number;
}> = ({ icon, label, value }) => (
  <div className="flex items-center justify-between p-3 glass-light rounded-lg">
    <div className="flex items-center gap-3">
      <div className="text-primary-400">{icon}</div>
      <span className="text-sm font-medium text-white">{label}</span>
    </div>
    <span className="text-sm text-gray-400">{value.toLocaleString()}</span>
  </div>
);

export default Analytics;