import React from 'react';
import { useDownloadStore } from '../store/downloadStore';
import DownloadItem from './DownloadItem';

const DownloadList: React.FC = () => {
  const { downloads, clearCompleted } = useDownloadStore();

  if (downloads.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center text-gray-500">
          <svg
            className="w-12 h-12 mx-auto mb-4 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p>No downloads yet</p>
          <p className="text-sm">Add a URL above to get started</p>
        </div>
      </div>
    );
  }

  const completedCount = downloads.filter(d => d.status === 'completed').length;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          Downloads ({downloads.length})
        </h2>
        {completedCount > 0 && (
          <button
            onClick={clearCompleted}
            className="btn btn-secondary px-4 py-2 text-sm"
          >
            Clear Completed ({completedCount})
          </button>
        )}
      </div>
      
      <div className="divide-y divide-gray-200">
        {downloads.map((download) => (
          <DownloadItem key={download.id} download={download} />
        ))}
      </div>
    </div>
  );
};

export default DownloadList; 