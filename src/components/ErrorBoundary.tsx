import { Component, ReactNode, ErrorInfo } from 'react';
// First install the package:
// npm install lucide-react
// or
// yarn add lucide-react
import { AlertTriangle, RefreshCw, Home, Bug, ExternalLink } from 'lucide-react';
import { AppError, ErrorCode, ErrorSeverity, createAppError, isAppError } from '../types/errors';
import { getErrorReportingService } from '../services/errorReporting';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: AppError) => void;
  showErrorDetails?: boolean;
}

interface State {
  hasError: boolean;
  appError: AppError | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, appError: null, errorInfo: null };
  }

  static getDerivedStateFromError(_error: Error): Partial<State> {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Convert to AppError if it's not already one
    const appError = isAppError(error) 
      ? error 
      : createAppError(error, {
          component: 'ErrorBoundary',
          action: 'component_render',
          timestamp: new Date(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          additionalData: {
            componentStack: errorInfo.componentStack
          }
        }, ErrorCode.UNEXPECTED_ERROR);

    this.setState({ appError, errorInfo });

    // Report the error
    this.reportError(appError);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(appError);
    }
  }

  private async reportError(error: AppError) {
    try {
      const errorReporting = getErrorReportingService();
      await errorReporting.reportError(error);
    } catch (reportingError) {
      console.warn('Failed to report error:', reportingError);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, appError: null, errorInfo: null });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportBug = () => {
    if (!this.state.appError) return;

    const errorDetails = {
      id: `error_${Date.now()}`,
      code: this.state.appError.code,
      message: this.state.appError.technicalMessage,
      userMessage: this.state.appError.userMessage,
      severity: this.state.appError.severity,
      context: this.state.appError.context,
      stack: this.state.appError.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    // In a real app, this would integrate with your bug tracking system
    console.log('Bug report data:', errorDetails);
    
    // Copy error details to clipboard for easy reporting
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please paste this information when contacting support.');
      })
      .catch(() => {
        alert('Error details have been logged to console. Please contact support if the issue persists.');
      });
  };

  handleHelpLink = () => {
    if (this.state.appError?.helpUrl) {
      window.open(this.state.appError.helpUrl, '_blank');
    }
  };

  executeRecoveryAction = async (action: () => void | Promise<void>) => {
    try {
      await action();
      // If recovery action succeeds, reset the error state
      this.setState({ hasError: false, appError: null, errorInfo: null });
    } catch (recoveryError) {
      console.error('Recovery action failed:', recoveryError);
      // Could show a toast or additional error message here
    }
  };

  render() {
    if (this.state.hasError && this.state.appError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { appError } = this.state;
      const showDetails = this.props.showErrorDetails ?? (process.env.NODE_ENV === 'development');

      return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
          <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
            <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
              <div className="flex flex-col items-center">
                <AlertTriangle 
                  className={`h-12 w-12 mb-4 ${
                    appError.severity === ErrorSeverity.CRITICAL ? 'text-red-600' :
                    appError.severity === ErrorSeverity.HIGH ? 'text-red-500' :
                    appError.severity === ErrorSeverity.MEDIUM ? 'text-yellow-500' :
                    'text-blue-500'
                  }`} 
                />
                
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  {appError.severity === ErrorSeverity.CRITICAL ? 'Critical Error' :
                   appError.severity === ErrorSeverity.HIGH ? 'Error Occurred' :
                   'Something went wrong'}
                </h2>
                
                <p className="text-sm text-gray-600 text-center mb-4">
                  {appError.userMessage}
                </p>

                {appError.helpUrl && (
                  <button
                    onClick={this.handleHelpLink}
                    className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 mb-4"
                  >
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Get Help
                  </button>
                )}

                {/* Recovery Actions */}
                {appError.recoveryActions && appError.recoveryActions.length > 0 && (
                  <div className="mb-6 w-full">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Suggested Actions:</h3>
                    <div className="space-y-2">
                      {appError.recoveryActions.map((action, index) => (
                        <button
                          key={index}
                          onClick={() => this.executeRecoveryAction(action.action)}
                          className={`w-full text-left px-3 py-2 text-sm rounded border ${
                            action.primary 
                              ? 'bg-blue-50 border-blue-200 text-blue-800 hover:bg-blue-100'
                              : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {action.label}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
                
                {showDetails && (
                  <details className="mb-6 w-full">
                    <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                      Technical Details
                    </summary>
                    <div className="bg-gray-100 p-3 rounded text-xs font-mono overflow-auto max-h-60">
                      <div className="mb-2">
                        <strong>Error Code:</strong> {appError.code}
                      </div>
                      <div className="mb-2">
                        <strong>Severity:</strong> {appError.severity}
                      </div>
                      <div className="mb-2">
                        <strong>Retryable:</strong> {appError.retryable ? 'Yes' : 'No'}
                      </div>
                      <div className="mb-2">
                        <strong>Technical Message:</strong> {appError.technicalMessage}
                      </div>
                      <div className="mb-2">
                        <strong>Context:</strong>
                        <pre className="whitespace-pre-wrap">{JSON.stringify(appError.context, null, 2)}</pre>
                      </div>
                      {appError.stack && (
                        <div className="mb-2">
                          <strong>Stack Trace:</strong>
                          <pre className="whitespace-pre-wrap">{appError.stack}</pre>
                        </div>
                      )}
                      {this.state.errorInfo && (
                        <div>
                          <strong>Component Stack:</strong>
                          <pre className="whitespace-pre-wrap">{this.state.errorInfo.componentStack}</pre>
                        </div>
                      )}
                    </div>
                  </details>
                )}
                
                <div className="flex flex-wrap gap-3 justify-center">
                  {appError.retryable && (
                    <button
                      onClick={this.handleRetry}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Try Again
                    </button>
                  )}
                  
                  <button
                    onClick={this.handleGoHome}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <Home className="h-4 w-4 mr-2" />
                    Go Home
                  </button>
                  
                  <button
                    onClick={this.handleReportBug}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <Bug className="h-4 w-4 mr-2" />
                    Report Issue
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;