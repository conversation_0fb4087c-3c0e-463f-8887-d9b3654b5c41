import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Sparkles,
  Image,
  Video,
  Wand2,
  Download,
  Share2,
  <PERSON>tings,
  Loader,
  AlertCircle,
  Check,
  Zap,
  Clock,
  DollarSign
} from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { unifiedAIGateway } from '../../ai/gateway/UnifiedAIGateway';
import { GenerationOptions, GenerationType } from '../../ai/gateway/types';
import { invoke } from '@tauri-apps/api/core';
import { convertFileSrc } from '@tauri-apps/api/core';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';
import { toast } from 'react-toastify';

interface GenerationRequest {
  id: string;
  type: GenerationType;
  prompt: string;
  status: 'pending' | 'generating' | 'completed' | 'failed';
  result?: any;
  error?: string;
  cost?: number;
  time?: number;
}

interface AIGenerationPanelProps {
  onOpenSettings?: () => void;
}

export const AIGenerationPanel: React.FC<AIGenerationPanelProps> = ({ onOpenSettings }) => {
  const [prompt, setPrompt] = useState('');
  const [generationType, setGenerationType] = useState<GenerationType>('text_to_image');
  const [isGenerating, setIsGenerating] = useState(false);
  const [requests, setRequests] = useState<GenerationRequest[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [advancedOptions, setAdvancedOptions] = useState<Partial<GenerationOptions>>({
    quality: 'high',
    width: 1024,
    height: 1024,
    duration: 5,
    fps: 24
  });

  const handleGenerate = useCallback(async () => {
    if (!prompt.trim() || isGenerating) return;

    const requestId = `gen_${Date.now()}`;
    const newRequest: GenerationRequest = {
      id: requestId,
      type: generationType,
      prompt,
      status: 'pending'
    };

    setRequests(prev => [newRequest, ...prev]);
    setIsGenerating(true);

    try {
      // Update status to generating
      setRequests(prev => prev.map(r => 
        r.id === requestId ? { ...r, status: 'generating' } : r
      ));

      const startTime = Date.now();
      
      // Generate content using the unified gateway
      const result = await unifiedAIGateway.generateContent(prompt, {
        type: generationType,
        ...advancedOptions
      });

      if (result.success) {
        // Save the generated content locally
        let localPath = '';
        if (generationType === 'text_to_image' || generationType === 'thumbnail') {
          localPath = await invoke<string>('save_generated_image', {
            imageData: Array.from(new Uint8Array(result.data)),
            filename: `${generationType}_${Date.now()}.${result.metadata?.format || 'png'}`
          });
        } else if (generationType === 'text_to_video' || generationType === 'image_to_video') {
          localPath = await invoke<string>('save_generated_video', {
            videoData: Array.from(new Uint8Array(result.data)),
            filename: `${generationType}_${Date.now()}.${result.metadata?.format || 'mp4'}`
          });
        }

        setRequests(prev => prev.map(r => 
          r.id === requestId ? {
            ...r,
            status: 'completed',
            result: { ...result, localPath },
            cost: result.cost,
            time: Date.now() - startTime
          } : r
        ));
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      setRequests(prev => prev.map(r => 
        r.id === requestId ? {
          ...r,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Generation failed'
        } : r
      ));
    } finally {
      setIsGenerating(false);
    }
  }, [prompt, generationType, advancedOptions]);

  const generationTypes = [
    { id: 'text_to_image', label: 'Text to Image', icon: Image },
    { id: 'text_to_video', label: 'Text to Video', icon: Video },
    { id: 'image_to_video', label: 'Image to Video', icon: Video },
    { id: 'thumbnail', label: 'Thumbnail', icon: Image }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Sparkles className="w-8 h-8 text-purple-400" />
          <h2 className="text-2xl font-bold text-white">AI Generation Studio</h2>
        </div>
        <AnimatedButton
          onClick={() => {
            if (onOpenSettings) {
              onOpenSettings();
            } else {
              toast.info('Provider settings coming soon!');
            }
          }}
          variant="secondary"
          size="sm"
          icon={<Settings className="w-4 h-4" />}
        >
          Provider Settings
        </AnimatedButton>
      </div>

      {/* Generation Type Selector */}
      <GlassCard>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {generationTypes.map(type => (
            <button
              key={type.id}
              onClick={() => setGenerationType(type.id as GenerationType)}
              className={`
                p-4 rounded-lg border-2 transition-all
                ${generationType === type.id
                  ? 'border-purple-500 bg-purple-500/20'
                  : 'border-gray-700 hover:border-gray-600'
                }
              `}
            >
              <type.icon className="w-6 h-6 mx-auto mb-2 text-purple-400" />
              <span className="text-sm text-white">{type.label}</span>
            </button>
          ))}
        </div>
      </GlassCard>

      {/* Prompt Input */}
      <GlassCard>
        <div className="space-y-4">
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder={`Describe what you want to ${generationType.replace('_', ' ')}...`}
            className="w-full h-32 bg-gray-900/50 border border-gray-700 rounded-lg p-4 text-white placeholder-gray-500 resize-none focus:outline-none focus:border-purple-500"
          />
          
          {/* Advanced Options */}
          <details className="group">
            <summary className="cursor-pointer text-sm text-gray-400 hover:text-white transition-colors">
              Advanced Options
            </summary>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-400 mb-1">Quality</label>
                <select
                  value={advancedOptions.quality}
                  onChange={(e) => setAdvancedOptions({...advancedOptions, quality: e.target.value as any})}
                  className="w-full bg-gray-900/50 border border-gray-700 rounded p-2 text-white"
                >
                  <option value="low">Low (Fast)</option>
                  <option value="medium">Medium</option>
                  <option value="high">High (Slow)</option>
                </select>
              </div>
              
              {(generationType === 'text_to_image' || generationType === 'thumbnail') && (
                <>
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">Width</label>
                    <input
                      type="number"
                      value={advancedOptions.width}
                      onChange={(e) => setAdvancedOptions({...advancedOptions, width: parseInt(e.target.value)})}
                      className="w-full bg-gray-900/50 border border-gray-700 rounded p-2 text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">Height</label>
                    <input
                      type="number"
                      value={advancedOptions.height}
                      onChange={(e) => setAdvancedOptions({...advancedOptions, height: parseInt(e.target.value)})}
                      className="w-full bg-gray-900/50 border border-gray-700 rounded p-2 text-white"
                    />
                  </div>
                </>
              )}
              
              {(generationType === 'text_to_video' || generationType === 'image_to_video') && (
                <>
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">Duration (seconds)</label>
                    <input
                      type="number"
                      value={advancedOptions.duration}
                      onChange={(e) => setAdvancedOptions({...advancedOptions, duration: parseInt(e.target.value)})}
                      min="1"
                      max="10"
                      className="w-full bg-gray-900/50 border border-gray-700 rounded p-2 text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm text-gray-400 mb-1">FPS</label>
                    <select
                      value={advancedOptions.fps}
                      onChange={(e) => setAdvancedOptions({...advancedOptions, fps: parseInt(e.target.value)})}
                      className="w-full bg-gray-900/50 border border-gray-700 rounded p-2 text-white"
                    >
                      <option value="24">24 FPS</option>
                      <option value="30">30 FPS</option>
                      <option value="60">60 FPS</option>
                    </select>
                  </div>
                </>
              )}
            </div>
          </details>
          
          <AnimatedButton
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            variant="primary"
            size="lg"
            fullWidth
            loading={isGenerating}
            icon={<Wand2 className="w-5 h-5" />}
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </AnimatedButton>
        </div>
      </GlassCard>

      {/* Generation History */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">Recent Generations</h3>
        
        <AnimatePresence>
          {requests.map(request => (
            <motion.div
              key={request.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <GlassCard variant="elevated">
                <div className="flex items-start gap-4">
                  {/* Status Icon */}
                  <div className="flex-shrink-0">
                    {request.status === 'pending' && <Clock className="w-6 h-6 text-gray-400" />}
                    {request.status === 'generating' && <Loader className="w-6 h-6 text-blue-400 animate-spin" />}
                    {request.status === 'completed' && <Check className="w-6 h-6 text-green-400" />}
                    {request.status === 'failed' && <AlertCircle className="w-6 h-6 text-red-400" />}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 space-y-2">
                    <p className="text-white font-medium">{request.prompt}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-400">
                      <span className="capitalize">{request.type.replace('_', ' ')}</span>
                      {request.cost && (
                        <span className="flex items-center gap-1">
                          <DollarSign className="w-3 h-3" />
                          {request.cost.toFixed(3)}
                        </span>
                      )}
                      {request.time && (
                        <span className="flex items-center gap-1">
                          <Zap className="w-3 h-3" />
                          {(request.time / 1000).toFixed(1)}s
                        </span>
                      )}
                    </div>
                    
                    {/* Result Preview */}
                    {request.status === 'completed' && request.result?.localPath && (
                      <div className="mt-2">
                        {request.type.includes('image') || request.type === 'thumbnail' ? (
                          <img
                            src={convertFileSrc(request.result.localPath)}
                            alt="Generated"
                            className="rounded-lg max-w-full h-auto"
                            style={{ maxHeight: '300px' }}
                          />
                        ) : (
                          <video
                            src={convertFileSrc(request.result.localPath)}
                            controls
                            className="rounded-lg max-w-full h-auto"
                            style={{ maxHeight: '300px' }}
                          />
                        )}
                      </div>
                    )}
                    
                    {/* Error Message */}
                    {request.status === 'failed' && request.error && (
                      <p className="text-red-400 text-sm">{request.error}</p>
                    )}
                  </div>
                  
                  {/* Actions */}
                  {request.status === 'completed' && (
                    <div className="flex gap-2">
                      <AnimatedButton
                        onClick={async () => {
                          try {
                            const fileExt = request.type.includes('image') ? 'png' : 'mp4';
                            const fileName = `generated_${request.type}_${Date.now()}.${fileExt}`;
                            
                            const filePath = await save({
                              defaultPath: fileName,
                              filters: [{
                                name: request.type.includes('image') ? 'Images' : 'Videos',
                                extensions: [fileExt]
                              }]
                            });
                            
                            if (filePath && request.result?.localPath) {
                              // Copy the file to the selected location
                              const response = await fetch(convertFileSrc(request.result.localPath));
                              const blob = await response.blob();
                              const buffer = await blob.arrayBuffer();
                              await writeFile(filePath, new Uint8Array(buffer));
                              
                              toast.success(`Saved to ${filePath}`);
                            }
                          } catch (error) {
                            console.error('Download error:', error);
                            toast.error('Failed to download file');
                          }
                        }}
                        variant="secondary"
                        size="sm"
                        icon={<Download className="w-4 h-4" />}
                      >
                        <span className="sr-only">Download</span>
                      </AnimatedButton>
                      <AnimatedButton
                        onClick={async () => {
                          try {
                            if (request.result?.localPath) {
                              // Copy file path to clipboard
                              await navigator.clipboard.writeText(request.result.localPath);
                              toast.success('File path copied to clipboard!');
                            }
                          } catch (error) {
                            console.error('Share error:', error);
                            toast.error('Failed to copy path');
                          }
                        }}
                        variant="secondary"
                        size="sm"
                        icon={<Share2 className="w-4 h-4" />}
                      >
                        <span className="sr-only">Share</span>
                      </AnimatedButton>
                    </div>
                  )}
                </div>
              </GlassCard>
            </motion.div>
          ))}
        </AnimatePresence>
        
        {requests.length === 0 && (
          <GlassCard variant="elevated">
            <div className="text-center py-8">
              <Sparkles className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No generations yet. Start creating!</p>
            </div>
          </GlassCard>
        )}
      </div>
    </div>
  );
};