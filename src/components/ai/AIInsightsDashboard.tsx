import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  TrendingUp, 
  BarChart3, 
  MessageSquare, 
  Heart,
  Eye,
  Clock,
  Sparkles,
  AlertCircle,
  CheckCircle,
  PlayCircle,
  ChevronRight,
  Zap
} from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { colors, typography } from '../../design-system';
import { ContentAnalysis, ViralityPrediction, PlatformOptimization } from '../../ai/types';
import { contentAnalyzer } from '../../ai/ContentAnalyzer';

interface AIInsightsDashboardProps {
  filePath?: string;
  analysis?: ContentAnalysis;
  onAnalyze?: (analysis: ContentAnalysis) => void;
}

const AIInsightsDashboard: React.FC<AIInsightsDashboardProps> = ({
  filePath,
  analysis: providedAnalysis,
  onAnalyze
}) => {
  const [analysis, setAnalysis] = useState<ContentAnalysis | null>(providedAnalysis || null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'performance' | 'optimization'>('overview');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (filePath && !analysis) {
      analyzeContent();
    }
  }, [filePath]);

  const analyzeContent = async () => {
    if (!filePath) return;

    setIsAnalyzing(true);
    try {
      const result = await contentAnalyzer.analyzeContent(filePath, {
        enableTranscription: true,
        enableSceneDetection: true,
        enableSentimentAnalysis: true,
        enablePlatformOptimization: true,
        targetPlatforms: ['youtube', 'tiktok', 'instagram', 'twitter']
      });

      setAnalysis(result);
      onAnalyze?.(result);
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const renderViralityScore = (prediction?: ViralityPrediction) => {
    if (!prediction) return null;

    const getScoreColor = (score: number) => {
      if (score >= 80) return '#10B981'; // green
      if (score >= 60) return '#3B82F6'; // blue
      if (score >= 40) return '#F59E0B'; // yellow
      return '#EF4444'; // red
    };

    return (
      <GlassCard variant="interactive">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-primary-400" />
              Virality Prediction
            </h3>
            <div className="text-sm text-gray-400">
              {(prediction.confidence * 100).toFixed(0)}% confidence
            </div>
          </div>

          <div className="relative mb-6">
            <div className="flex items-center justify-center">
              <div 
                className="relative w-32 h-32 rounded-full flex items-center justify-center"
                style={{
                  background: `conic-gradient(${getScoreColor(prediction.score)} ${prediction.score * 3.6}deg, rgba(255,255,255,0.1) 0deg)`
                }}
              >
                <div className="absolute inset-2 bg-dark-200 rounded-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white">{prediction.score}</div>
                    <div className="text-xs text-gray-400">Virality Score</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {prediction.factors && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-400 mb-2">Key Factors</h4>
              {prediction.factors.slice(0, 5).map((factor, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">{factor.factor}</span>
                  <span className={`font-medium ${factor.impact === 'positive' ? 'text-green-400' : 'text-red-400'}`}>
                    {factor.impact === 'positive' ? '+' : '-'}{(factor.weight * 10).toFixed(0)}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </GlassCard>
    );
  };

  const renderPlatformOptimizations = (optimizations?: PlatformOptimization[]) => {
    if (!optimizations || optimizations.length === 0) return null;

    const platformIcons = {
      youtube: '🎬',
      tiktok: '🎵',
      instagram: '📸',
      twitter: '🐦',
      linkedin: '💼'
    };

    return (
      <div className="space-y-4">
        {optimizations.map((opt) => (
          <GlassCard key={opt.platform} variant="default">
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-white flex items-center gap-2">
                  <span className="text-2xl">{platformIcons[opt.platform] || '📱'}</span>
                  {opt.platform.charAt(0).toUpperCase() + opt.platform.slice(1)}
                </h4>
                <div className="flex items-center gap-2">
                  <div className="text-sm text-gray-400">Compatibility</div>
                  <div className="text-lg font-bold text-primary-400">{opt.score}%</div>
                </div>
              </div>

              {opt.recommendations && (
                <div className="space-y-2 text-sm">
                  {opt.recommendations.idealLength && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span>Ideal length: {Math.floor(opt.recommendations.idealLength / 60)}:{(opt.recommendations.idealLength % 60).toString().padStart(2, '0')}</span>
                    </div>
                  )}
                  {opt.recommendations.aspectRatio && (
                    <div className="flex items-center gap-2 text-gray-300">
                      <BarChart3 className="w-4 h-4 text-gray-500" />
                      <span>Aspect ratio: {opt.recommendations.aspectRatio}</span>
                    </div>
                  )}
                  {opt.recommendations.hashtags && opt.recommendations.hashtags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {opt.recommendations.hashtags.slice(0, 5).map((tag, index) => (
                        <span key={index} className="px-2 py-1 glass-light rounded text-xs text-primary-300">
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </GlassCard>
        ))}
      </div>
    );
  };

  const renderContentInsights = () => {
    if (!analysis) return null;

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentiment Timeline */}
        {analysis.sentiment && (
          <GlassCard variant="default">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Heart className="w-5 h-5 text-pink-400" />
                Sentiment Analysis
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Overall Sentiment</span>
                  <span className={`font-medium capitalize ${
                    analysis.sentiment.overall === 'positive' ? 'text-green-400' :
                    analysis.sentiment.overall === 'negative' ? 'text-red-400' :
                    'text-yellow-400'
                  }`}>
                    {analysis.sentiment.overall}
                  </span>
                </div>
                <div className="w-full h-2 bg-glass-light rounded-full overflow-hidden">
                  <div 
                    className="h-full transition-all duration-500"
                    style={{
                      width: `${((analysis.sentiment.score + 1) / 2) * 100}%`,
                      background: analysis.sentiment.score > 0 
                        ? 'linear-gradient(90deg, #10B981 0%, #34D399 100%)' 
                        : 'linear-gradient(90deg, #EF4444 0%, #F87171 100%)'
                    }}
                  />
                </div>
              </div>
            </div>
          </GlassCard>
        )}

        {/* Key Topics */}
        {analysis.topics && analysis.topics.length > 0 && (
          <GlassCard variant="default">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <MessageSquare className="w-5 h-5 text-blue-400" />
                Key Topics
              </h3>
              <div className="space-y-2">
                {analysis.topics.slice(0, 5).map((topic, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-gray-300">{topic.topic}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-24 h-2 bg-glass-light rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-primary-400 to-primary-600 transition-all duration-500"
                          style={{ width: `${topic.relevance * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-400">{(topic.relevance * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </GlassCard>
        )}

        {/* Scene Analysis */}
        {analysis.scenes && analysis.scenes.length > 0 && (
          <GlassCard variant="default">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <PlayCircle className="w-5 h-5 text-purple-400" />
                Scene Breakdown
              </h3>
              <div className="space-y-3">
                {analysis.scenes.map((scene, index) => (
                  <div key={index} className="flex items-center justify-between p-3 glass-light rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{
                        scene.sceneType === 'intro' ? '🎬' :
                        scene.sceneType === 'outro' ? '🎭' :
                        scene.sceneType === 'transition' ? '🔄' :
                        '📹'
                      }</div>
                      <div>
                        <div className="text-sm font-medium text-white capitalize">
                          {scene.sceneType.replace('_', ' ')}
                        </div>
                        <div className="text-xs text-gray-400">
                          {Math.floor(scene.startTime)}s - {Math.floor(scene.endTime)}s
                        </div>
                      </div>
                    </div>
                    {scene.mood && (
                      <span className="text-xs px-2 py-1 glass-medium rounded capitalize text-gray-300">
                        {scene.mood}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </GlassCard>
        )}

        {/* Quality Metrics */}
        <GlassCard variant="default">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              Quality Analysis
            </h3>
            <div className="space-y-4">
              {analysis.videoQuality && (
                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Video Quality</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Resolution</span>
                      <span className="text-gray-300">{analysis.videoQuality.resolution}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">FPS</span>
                      <span className="text-gray-300">{analysis.videoQuality.fps}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Codec</span>
                      <span className="text-gray-300">{analysis.videoQuality.codec}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Bitrate</span>
                      <span className="text-gray-300">{(analysis.videoQuality.bitrate / 1000000).toFixed(1)} Mbps</span>
                    </div>
                  </div>
                  {analysis.videoQuality.issues && analysis.videoQuality.issues.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {analysis.videoQuality.issues.map((issue, index) => (
                        <div key={index} className="flex items-start gap-2 text-xs text-yellow-400">
                          <AlertCircle className="w-3 h-3 mt-0.5" />
                          <span>{issue}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {analysis.audioQuality && (
                <div>
                  <h4 className="text-sm font-medium text-gray-400 mb-2">Audio Quality</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Sample Rate</span>
                      <span className="text-gray-300">{analysis.audioQuality.sampleRate / 1000} kHz</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Channels</span>
                      <span className="text-gray-300">{analysis.audioQuality.channels === 2 ? 'Stereo' : 'Mono'}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </GlassCard>
      </div>
    );
  };

  const tabClass = (tab: string) => `
    px-4 py-2 font-medium rounded-lg transition-all cursor-pointer
    ${activeTab === tab 
      ? 'glass-strong text-white' 
      : 'text-gray-400 hover:text-white hover:glass-light'
    }
  `;

  if (isAnalyzing) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="relative mb-6">
          <div className="w-20 h-20 rounded-full bg-gradient-to-r from-primary-400 to-accent-400 animate-spin" />
          <Brain className="absolute inset-0 m-auto w-10 h-10 text-white" />
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">Analyzing Content</h3>
        <p className="text-gray-400 text-center max-w-md">
          Our AI is processing your content to extract insights, detect scenes, analyze sentiment, and predict performance...
        </p>
      </div>
    );
  }

  if (!analysis) {
    return (
      <GlassCard variant="elevated">
        <div className="p-8 text-center">
          <Brain className="w-16 h-16 mx-auto mb-4 text-gray-500" />
          <h3 className="text-xl font-semibold text-white mb-2">No Analysis Available</h3>
          <p className="text-gray-400 mb-6">
            {filePath ? 'Click analyze to start AI content analysis' : 'Select a file to analyze'}
          </p>
          {filePath && (
            <AnimatedButton
              onClick={analyzeContent}
              variant="primary"
              icon={<Sparkles className="w-5 h-5" />}
            >
              Analyze Content
            </AnimatedButton>
          )}
        </div>
      </GlassCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 
            className="text-2xl font-bold text-white mb-2"
            style={{ fontFamily: typography.fonts.heading }}
          >
            AI Content Intelligence
          </h2>
          <p className="text-gray-400">
            Powered by advanced AI analysis for {analysis.fileName}
          </p>
        </div>
        <AnimatedButton
          onClick={analyzeContent}
          variant="secondary"
          size="sm"
          icon={<Sparkles className="w-4 h-4" />}
        >
          Re-analyze
        </AnimatedButton>
      </div>

      {/* Tabs */}
      <div className="flex gap-2 p-1 glass-light rounded-lg">
        <button onClick={() => setActiveTab('overview')} className={tabClass('overview')}>
          Overview
        </button>
        <button onClick={() => setActiveTab('content')} className={tabClass('content')}>
          Content Analysis
        </button>
        <button onClick={() => setActiveTab('performance')} className={tabClass('performance')}>
          Performance
        </button>
        <button onClick={() => setActiveTab('optimization')} className={tabClass('optimization')}>
          Optimization
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <MetricCard
                icon={<Clock className="w-5 h-5" />}
                label="Duration"
                value={analysis.duration ? `${Math.floor(analysis.duration / 60)}:${(analysis.duration % 60).toString().padStart(2, '0')}` : 'N/A'}
                color="text-blue-400"
              />
              <MetricCard
                icon={<Eye className="w-5 h-5" />}
                label="Scenes"
                value={analysis.scenes?.length || 0}
                color="text-purple-400"
              />
              <MetricCard
                icon={<MessageSquare className="w-5 h-5" />}
                label="Topics"
                value={analysis.topics?.length || 0}
                color="text-green-400"
              />
              <MetricCard
                icon={<Heart className="w-5 h-5" />}
                label="Sentiment"
                value={analysis.sentiment?.overall || 'N/A'}
                color="text-pink-400"
              />
            </div>

            {/* Platform Optimizations */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-4">Platform Recommendations</h3>
              {renderPlatformOptimizations(analysis.platformSuggestions)}
            </div>
          </div>

          <div>
            {renderViralityScore(analysis.virality)}
          </div>
        </div>
      )}

      {activeTab === 'content' && renderContentInsights()}

      {activeTab === 'performance' && (
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 mx-auto mb-4 text-gray-500" />
          <h3 className="text-xl font-semibold text-white mb-2">Performance Predictions</h3>
          <p className="text-gray-400">Upload your content to start tracking real performance data</p>
        </div>
      )}

      {activeTab === 'optimization' && (
        <div className="space-y-6">
          <GlassCard variant="elevated">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">AI Optimization Suggestions</h3>
              <div className="space-y-3">
                {analysis.videoQuality?.issues?.map((issue, index) => (
                  <OptimizationItem
                    key={index}
                    title={issue}
                    description="Click to apply AI-powered fix"
                    icon={<AlertCircle className="w-5 h-5 text-yellow-400" />}
                    action="Fix"
                  />
                ))}
                <OptimizationItem
                  title="Generate Captions"
                  description="Add AI-generated captions in multiple languages"
                  icon={<MessageSquare className="w-5 h-5 text-blue-400" />}
                  action="Generate"
                />
                <OptimizationItem
                  title="Create Highlights"
                  description="Extract best moments for shorts and reels"
                  icon={<Sparkles className="w-5 h-5 text-purple-400" />}
                  action="Create"
                />
                <OptimizationItem
                  title="Optimize for Platforms"
                  description="Auto-adjust format, length, and quality for each platform"
                  icon={<Zap className="w-5 h-5 text-green-400" />}
                  action="Optimize"
                />
              </div>
            </div>
          </GlassCard>
        </div>
      )}
    </div>
  );
};

const MetricCard: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: string | number;
  color: string;
}> = ({ icon, label, value, color }) => (
  <GlassCard variant="default">
    <div className="p-4">
      <div className={`${color} mb-2`}>{icon}</div>
      <div className="text-2xl font-bold text-white">{value}</div>
      <div className="text-xs text-gray-400">{label}</div>
    </div>
  </GlassCard>
);

const OptimizationItem: React.FC<{
  title: string;
  description: string;
  icon: React.ReactNode;
  action: string;
}> = ({ title, description, icon, action }) => (
  <div className="flex items-center justify-between p-4 glass-light rounded-lg hover:glass-medium transition-all cursor-pointer">
    <div className="flex items-center gap-3">
      {icon}
      <div>
        <h4 className="font-medium text-white">{title}</h4>
        <p className="text-sm text-gray-400">{description}</p>
      </div>
    </div>
    <AnimatedButton variant="secondary" size="sm">
      {action}
    </AnimatedButton>
  </div>
);

export default AIInsightsDashboard;