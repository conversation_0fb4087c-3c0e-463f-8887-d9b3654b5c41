import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sciss<PERSON>, 
  Mic, 
  Palette, 
  Film, 
  Sparkles, 
  Clock,
  Wand2,
  Zap,
  Check,
  X,
  AlertCircle,
  Loader2,
  Play,
  Settings,
  FileVideo,
  Download
} from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { smartEditor } from '../../ai/SmartEditor';
import { ContentAnalysis, EditSuggestion } from '../../ai/types';
import type { EditProject } from '../../ai/SmartEditor';

interface SmartEditPanelProps {
  filePath?: string;
  analysis?: ContentAnalysis;
  onExport?: (exportPath: string) => void;
  onClose?: () => void;
}

interface EditOption {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  recommended?: boolean;
}

const SmartEditPanel: React.FC<SmartEditPanelProps> = ({
  filePath,
  analysis,
  onExport,
  onClose
}) => {
  const [project, setProject] = useState<EditProject | null>(null);
  const [suggestions, setSuggestions] = useState<EditSuggestion[]>([]);
  const [selectedSuggestions, setSelectedSuggestions] = useState<Set<string>>(new Set());
  const [autoEditOptions, setAutoEditOptions] = useState({
    removeSilence: true,
    removeFillerWords: true,
    enhanceAudio: false,
    generateCaptions: false,
    colorCorrection: false,
    autoTransitions: false
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Initialize project and suggestions
  useEffect(() => {
    if (filePath && analysis) {
      initializeProject();
    }
  }, [filePath, analysis]);

  const initializeProject = async () => {
    if (!filePath || !analysis) return;

    try {
      const newProject = await smartEditor.createProject(filePath, analysis);
      setProject(newProject);

      const editSuggestions = await smartEditor.generateEditSuggestions(analysis);
      setSuggestions(editSuggestions);

      // Auto-select high impact suggestions
      const highImpact = editSuggestions
        .filter(s => s.impact === 'high' && s.autoApplicable)
        .map(s => s.id);
      setSelectedSuggestions(new Set(highImpact));
    } catch (error) {
      console.error('Failed to initialize project:', error);
      setError('Failed to initialize editing project');
    }
  };

  const handleAutoEdit = async () => {
    if (!project) return;

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      setCurrentStep('Applying automatic edits...');
      const result = await smartEditor.applyAutoEdits(project.id, autoEditOptions);
      
      setProject(result);
      setProgress(100);
      setCurrentStep('Edits applied successfully!');
      
      // Generate preview
      if (result.outputPath) {
        setPreviewUrl(result.outputPath);
      }
    } catch (error) {
      console.error('Auto-edit failed:', error);
      setError('Failed to apply automatic edits');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleApplySuggestion = async (suggestionId: string) => {
    if (!project) return;

    setIsProcessing(true);
    setError(null);

    try {
      const suggestion = suggestions.find(s => s.id === suggestionId);
      if (!suggestion) return;

      setCurrentStep(`Applying ${suggestion.description}...`);
      const result = await smartEditor.applyEditSuggestion(project.id, suggestionId);
      
      setProject(result);
      setSelectedSuggestions(prev => {
        const newSet = new Set(prev);
        newSet.delete(suggestionId);
        return newSet;
      });
    } catch (error) {
      console.error('Failed to apply suggestion:', error);
      setError('Failed to apply edit suggestion');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerateHighlights = async () => {
    if (!filePath || !analysis) return;

    setIsProcessing(true);
    setProgress(0);
    setError(null);

    try {
      setCurrentStep('Generating highlight reel...');
      const result = await smartEditor.generateHighlightReel(filePath, analysis, {
        duration: 60,
        criteria: 'engagement',
        includeHook: true,
        transitionStyle: 'fade'
      });

      setProgress(100);
      setCurrentStep('Highlights generated!');
      
      if (result.outputPath) {
        setPreviewUrl(result.outputPath);
      }
    } catch (error) {
      console.error('Highlight generation failed:', error);
      setError('Failed to generate highlights');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExport = async () => {
    if (!project || !project.outputPath) return;

    setIsProcessing(true);
    setError(null);

    try {
      setCurrentStep('Exporting video...');
      const exportPath = await smartEditor.exportProject(
        project.id,
        `${project.inputPath}_edited.mp4`,
        {
          format: 'mp4',
          quality: 'high',
          includeMetadata: true
        }
      );

      setCurrentStep('Export complete!');
      onExport?.(exportPath);
    } catch (error) {
      console.error('Export failed:', error);
      setError('Failed to export video');
    } finally {
      setIsProcessing(false);
    }
  };

  const editOptions: EditOption[] = [
    {
      id: 'removeSilence',
      label: 'Remove Silence',
      description: 'Automatically remove silent segments',
      icon: <Scissors className="w-5 h-5" />,
      enabled: autoEditOptions.removeSilence,
      recommended: true
    },
    {
      id: 'removeFillerWords',
      label: 'Remove Filler Words',
      description: 'Remove "um", "uh", and other filler words',
      icon: <Mic className="w-5 h-5" />,
      enabled: autoEditOptions.removeFillerWords,
      recommended: true
    },
    {
      id: 'enhanceAudio',
      label: 'Enhance Audio',
      description: 'Reduce noise and normalize volume',
      icon: <Wand2 className="w-5 h-5" />,
      enabled: autoEditOptions.enhanceAudio
    },
    {
      id: 'generateCaptions',
      label: 'Generate Captions',
      description: 'Add animated captions automatically',
      icon: <Film className="w-5 h-5" />,
      enabled: autoEditOptions.generateCaptions
    },
    {
      id: 'colorCorrection',
      label: 'Color Correction',
      description: 'Apply AI color grading',
      icon: <Palette className="w-5 h-5" />,
      enabled: autoEditOptions.colorCorrection
    },
    {
      id: 'autoTransitions',
      label: 'Auto Transitions',
      description: 'Add smooth transitions between scenes',
      icon: <Sparkles className="w-5 h-5" />,
      enabled: autoEditOptions.autoTransitions
    }
  ];

  if (!filePath || !analysis) {
    return (
      <GlassCard variant="elevated" className="p-8 text-center">
        <FileVideo className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-200 mb-2">
          No Content Selected
        </h3>
        <p className="text-gray-400">
          Select a video to start smart editing
        </p>
      </GlassCard>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-100 flex items-center gap-2">
            <Wand2 className="w-6 h-6 text-purple-400" />
            Smart Edit Panel
          </h2>
          <p className="text-gray-400 mt-1">
            AI-powered editing for {analysis.fileName}
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-200 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        )}
      </div>

      {/* Auto-Edit Options */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-200 mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5 text-yellow-400" />
            Quick Edits
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {editOptions.map((option) => (
              <label
                key={option.id}
                className={`
                  flex items-start gap-3 p-4 rounded-lg border cursor-pointer
                  transition-all duration-200
                  ${option.enabled 
                    ? 'bg-purple-500/10 border-purple-500/50' 
                    : 'bg-gray-800/50 border-gray-700 hover:border-gray-600'
                  }
                `}
              >
                <input
                  type="checkbox"
                  checked={option.enabled}
                  onChange={(e) => setAutoEditOptions(prev => ({
                    ...prev,
                    [option.id]: e.target.checked
                  }))}
                  className="mt-1"
                  disabled={isProcessing}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className={option.enabled ? 'text-purple-400' : 'text-gray-400'}>
                      {option.icon}
                    </span>
                    <span className="font-medium text-gray-200">
                      {option.label}
                    </span>
                    {option.recommended && (
                      <span className="text-xs bg-green-500/20 text-green-400 px-2 py-0.5 rounded-full">
                        Recommended
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    {option.description}
                  </p>
                </div>
              </label>
            ))}
          </div>

          <AnimatedButton
            onClick={handleAutoEdit}
            disabled={isProcessing || Object.values(autoEditOptions).every(v => !v)}
            variant="primary"
            icon={<Zap className="w-4 h-4" />}
            className="w-full"
          >
            {isProcessing ? 'Processing...' : 'Apply Quick Edits'}
          </AnimatedButton>
        </div>
      </GlassCard>

      {/* Smart Suggestions */}
      {suggestions.length > 0 && (
        <GlassCard variant="elevated">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-4 flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-blue-400" />
              AI Suggestions
            </h3>

            <div className="space-y-3">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className={`
                    p-4 rounded-lg border transition-all
                    ${selectedSuggestions.has(suggestion.id)
                      ? 'bg-blue-500/10 border-blue-500/50'
                      : 'bg-gray-800/50 border-gray-700'
                    }
                  `}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-200">
                        {suggestion.description}
                      </h4>
                      <div className="flex items-center gap-4 mt-2">
                        <span className={`
                          text-xs px-2 py-1 rounded-full
                          ${suggestion.impact === 'high' 
                            ? 'bg-red-500/20 text-red-400'
                            : suggestion.impact === 'medium'
                            ? 'bg-yellow-500/20 text-yellow-400'
                            : 'bg-green-500/20 text-green-400'
                          }
                        `}>
                          {suggestion.impact} impact
                        </span>
                        {suggestion.autoApplicable && (
                          <span className="text-xs text-gray-500">
                            Auto-applicable
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <AnimatedButton
                      onClick={() => handleApplySuggestion(suggestion.id)}
                      disabled={isProcessing || !selectedSuggestions.has(suggestion.id)}
                      size="sm"
                      variant="secondary"
                    >
                      Apply
                    </AnimatedButton>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </GlassCard>
      )}

      {/* Highlight Generation */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-200 mb-4 flex items-center gap-2">
            <Film className="w-5 h-5 text-green-400" />
            Generate Highlights
          </h3>
          
          <p className="text-gray-400 mb-4">
            Create a 60-second highlight reel of your best moments
          </p>

          <AnimatedButton
            onClick={handleGenerateHighlights}
            disabled={isProcessing}
            variant="secondary"
            icon={<Film className="w-4 h-4" />}
            className="w-full"
          >
            Generate Highlight Reel
          </AnimatedButton>
        </div>
      </GlassCard>

      {/* Progress & Status */}
      {(isProcessing || currentStep) && (
        <GlassCard variant="elevated">
          <div className="p-6">
            <div className="flex items-center gap-3 mb-4">
              {isProcessing ? (
                <Loader2 className="w-5 h-5 text-purple-400 animate-spin" />
              ) : (
                <Check className="w-5 h-5 text-green-400" />
              )}
              <span className="text-gray-200">{currentStep}</span>
            </div>

            {isProcessing && (
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            )}
          </div>
        </GlassCard>
      )}

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-500/10 border border-red-500/50 rounded-lg p-4"
        >
          <div className="flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <p className="text-red-400">{error}</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Export Options */}
      {project?.outputPath && (
        <GlassCard variant="elevated">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-200 mb-4 flex items-center gap-2">
              <Download className="w-5 h-5 text-purple-400" />
              Export Options
            </h3>

            <div className="flex gap-3">
              <AnimatedButton
                onClick={handleExport}
                disabled={isProcessing}
                variant="primary"
                icon={<Download className="w-4 h-4" />}
                className="flex-1"
              >
                Export Video
              </AnimatedButton>
              
              {previewUrl && (
                <AnimatedButton
                  onClick={() => window.open(previewUrl, '_blank')}
                  variant="secondary"
                  icon={<Play className="w-4 h-4" />}
                >
                  Preview
                </AnimatedButton>
              )}
            </div>
          </div>
        </GlassCard>
      )}
    </div>
  );
};

export default SmartEditPanel;