import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AIInsightsDashboard from '../AIInsightsDashboard';
import { ContentAnalysis } from '../../../ai/types';

// Mock the AI services
vi.mock('../../../ai/ContentAnalyzer', () => ({
  contentAnalyzer: {
    analyzeContent: vi.fn()
  }
}));

// Mock UI components
vi.mock('../../ui/GlassCard', () => ({
  default: ({ children, variant }: any) => (
    <div data-testid={`glass-card-${variant}`}>{children}</div>
  )
}));

vi.mock('../../ui/AnimatedButton', () => ({
  default: ({ children, onClick, variant, icon }: any) => (
    <button data-testid={`animated-button-${variant}`} onClick={onClick}>
      {icon}
      {children}
    </button>
  )
}));

describe('AIInsightsDashboard', () => {
  const mockAnalysis: ContentAnalysis = {
    id: 'test-id',
    filePath: '/test/video.mp4',
    fileName: 'video.mp4',
    fileType: 'video',
    duration: 300,
    analysisDate: new Date(),
    scenes: [
      {
        startTime: 0,
        endTime: 60,
        sceneType: 'intro',
        description: 'Opening scene',
        keyframes: [],
        mood: 'energetic',
        pace: 'fast'
      },
      {
        startTime: 60,
        endTime: 240,
        sceneType: 'main_content',
        description: 'Main content',
        keyframes: [],
        mood: 'calm',
        pace: 'medium'
      }
    ],
    sentiment: {
      overall: 'positive',
      score: 0.75,
      timeline: []
    },
    topics: [
      {
        topic: 'Technology',
        relevance: 0.9,
        keywords: ['AI', 'innovation'],
        category: 'tech'
      },
      {
        topic: 'Tutorial',
        relevance: 0.7,
        keywords: ['howto', 'guide'],
        category: 'education'
      }
    ],
    virality: {
      score: 78,
      confidence: 0.82,
      factors: [
        {
          factor: 'Trending topic',
          impact: 'positive',
          weight: 1.5
        },
        {
          factor: 'Good pacing',
          impact: 'positive',
          weight: 1.2
        }
      ]
    },
    platformSuggestions: [
      {
        platform: 'youtube',
        recommendations: {
          idealLength: 600,
          aspectRatio: '16:9',
          hashtags: ['#AI', '#Tech', '#Tutorial'],
          bestPostingTimes: ['14:00', '20:00']
        },
        score: 85
      },
      {
        platform: 'tiktok',
        recommendations: {
          idealLength: 60,
          aspectRatio: '9:16',
          hashtags: ['#TechTok', '#AIExplained'],
          bestPostingTimes: ['18:00', '21:00']
        },
        score: 65
      }
    ],
    videoQuality: {
      resolution: '1920x1080',
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      hasAudio: true,
      issues: []
    },
    audioQuality: {
      sampleRate: 48000,
      bitrate: 128000,
      channels: 2,
      codec: 'aac',
      volumeLevels: {
        average: -20,
        peak: -10,
        silent_segments: []
      }
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should show loading state when analyzing', async () => {
      const { contentAnalyzer } = await import('../../../ai/ContentAnalyzer');
      vi.mocked(contentAnalyzer.analyzeContent).mockImplementation(
        () => new Promise(() => {}) // Never resolves to keep loading
      );

      render(<AIInsightsDashboard filePath="/test/video.mp4" />);

      await waitFor(() => {
        expect(screen.getByText('Analyzing Content')).toBeInTheDocument();
      });
    });

    it('should show empty state when no analysis', () => {
      render(<AIInsightsDashboard />);

      expect(screen.getByText('No Analysis Available')).toBeInTheDocument();
      expect(screen.getByText('Select a file to analyze')).toBeInTheDocument();
    });

    it('should display analysis when provided', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      expect(screen.getByText('AI Content Intelligence')).toBeInTheDocument();
      expect(screen.getByText('video.mp4')).toBeInTheDocument();
    });
  });

  describe('Tab Navigation', () => {
    it('should switch between tabs', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      // Check initial tab
      expect(screen.getByText('Platform Recommendations')).toBeInTheDocument();

      // Switch to content tab
      fireEvent.click(screen.getByText('Content Analysis'));
      expect(screen.getByText('Sentiment Analysis')).toBeInTheDocument();

      // Switch to optimization tab
      fireEvent.click(screen.getByText('Optimization'));
      expect(screen.getByText('AI Optimization Suggestions')).toBeInTheDocument();
    });
  });

  describe('Overview Tab', () => {
    it('should display key metrics', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      expect(screen.getByText('5:00')).toBeInTheDocument(); // Duration
      expect(screen.getByText('2')).toBeInTheDocument(); // Scenes
      expect(screen.getByText('positive')).toBeInTheDocument(); // Sentiment
    });

    it('should display virality score', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      expect(screen.getByText('78')).toBeInTheDocument(); // Virality score
      expect(screen.getByText('82% confidence')).toBeInTheDocument();
      expect(screen.getByText('Trending topic')).toBeInTheDocument();
    });

    it('should display platform recommendations', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      expect(screen.getByText('Youtube')).toBeInTheDocument();
      expect(screen.getByText('85%')).toBeInTheDocument(); // Compatibility score
      expect(screen.getByText('Ideal length: 10:00')).toBeInTheDocument();
      
      expect(screen.getByText('Tiktok')).toBeInTheDocument();
      expect(screen.getByText('65%')).toBeInTheDocument();
    });
  });

  describe('Content Analysis Tab', () => {
    it('should display sentiment analysis', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Content Analysis'));

      expect(screen.getByText('Sentiment Analysis')).toBeInTheDocument();
      expect(screen.getByText('Overall Sentiment')).toBeInTheDocument();
      expect(screen.getByText('positive')).toBeInTheDocument();
    });

    it('should display key topics', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Content Analysis'));

      expect(screen.getByText('Key Topics')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
      expect(screen.getByText('90%')).toBeInTheDocument(); // Relevance
      expect(screen.getByText('Tutorial')).toBeInTheDocument();
      expect(screen.getByText('70%')).toBeInTheDocument();
    });

    it('should display scene breakdown', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Content Analysis'));

      expect(screen.getByText('Scene Breakdown')).toBeInTheDocument();
      expect(screen.getByText('Intro')).toBeInTheDocument();
      expect(screen.getByText('0s - 60s')).toBeInTheDocument();
      expect(screen.getByText('energetic')).toBeInTheDocument();
    });

    it('should display quality analysis', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Content Analysis'));

      expect(screen.getByText('Quality Analysis')).toBeInTheDocument();
      expect(screen.getByText('Video Quality')).toBeInTheDocument();
      expect(screen.getByText('1920x1080')).toBeInTheDocument();
      expect(screen.getByText('30')).toBeInTheDocument(); // FPS
    });
  });

  describe('Optimization Tab', () => {
    it('should display optimization suggestions', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Optimization'));

      expect(screen.getByText('AI Optimization Suggestions')).toBeInTheDocument();
      expect(screen.getByText('Generate Captions')).toBeInTheDocument();
      expect(screen.getByText('Create Highlights')).toBeInTheDocument();
      expect(screen.getByText('Optimize for Platforms')).toBeInTheDocument();
    });

    it('should show action buttons for suggestions', () => {
      render(<AIInsightsDashboard analysis={mockAnalysis} />);
      
      fireEvent.click(screen.getByText('Optimization'));

      const generateButtons = screen.getAllByText('Generate');
      expect(generateButtons.length).toBeGreaterThan(0);
      
      const optimizeButtons = screen.getAllByText('Optimize');
      expect(optimizeButtons.length).toBeGreaterThan(0);
    });
  });

  describe('Actions', () => {
    it('should trigger re-analysis', async () => {
      const { contentAnalyzer } = await import('../../../ai/ContentAnalyzer');
      vi.mocked(contentAnalyzer.analyzeContent).mockResolvedValueOnce(mockAnalysis);

      render(<AIInsightsDashboard analysis={mockAnalysis} />);

      const reAnalyzeButton = screen.getByText('Re-analyze');
      fireEvent.click(reAnalyzeButton);

      await waitFor(() => {
        expect(contentAnalyzer.analyzeContent).toHaveBeenCalled();
      });
    });

    it('should call onAnalyze callback when analysis completes', async () => {
      const { contentAnalyzer } = await import('../../../ai/ContentAnalyzer');
      vi.mocked(contentAnalyzer.analyzeContent).mockResolvedValueOnce(mockAnalysis);

      const onAnalyze = vi.fn();
      render(
        <AIInsightsDashboard 
          filePath="/test/video.mp4" 
          onAnalyze={onAnalyze} 
        />
      );

      await waitFor(() => {
        expect(onAnalyze).toHaveBeenCalledWith(mockAnalysis);
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle analysis without optional data', () => {
      const minimalAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        analysisDate: new Date()
      };

      render(<AIInsightsDashboard analysis={minimalAnalysis} />);

      expect(screen.getByText('N/A')).toBeInTheDocument(); // Duration
      expect(screen.getByText('0')).toBeInTheDocument(); // Scenes/Topics
    });

    it('should handle empty platform optimizations', () => {
      const analysisWithoutPlatforms = {
        ...mockAnalysis,
        platformSuggestions: []
      };

      render(<AIInsightsDashboard analysis={analysisWithoutPlatforms} />);

      // Should not crash and display overview
      expect(screen.getByText('AI Content Intelligence')).toBeInTheDocument();
    });
  });
});