import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import SmartEditPanel from '../SmartEditPanel';
import { ContentAnalysis, EditSuggestion } from '../../../ai/types';
import type { EditProject } from '../../../ai/SmartEditor';

// Mock the AI services
vi.mock('../../../ai/SmartEditor', () => ({
  smartEditor: {
    createProject: vi.fn(),
    generateEditSuggestions: vi.fn(),
    applyAutoEdits: vi.fn(),
    applyEditSuggestion: vi.fn(),
    generateHighlightReel: vi.fn(),
    exportProject: vi.fn()
  }
}));

// Mock UI components
vi.mock('../../ui/GlassCard', () => ({
  default: ({ children, variant }: any) => (
    <div data-testid={`glass-card-${variant}`}>{children}</div>
  )
}));

vi.mock('../../ui/AnimatedButton', () => ({
  default: ({ children, onClick, disabled, variant, icon }: any) => (
    <button 
      data-testid={`animated-button-${variant}`} 
      onClick={onClick}
      disabled={disabled}
    >
      {icon}
      {children}
    </button>
  )
}));

describe('SmartEditPanel', () => {
  const mockAnalysis: ContentAnalysis = {
    id: 'test-id',
    filePath: '/test/video.mp4',
    fileName: 'video.mp4',
    fileType: 'video',
    duration: 300,
    analysisDate: new Date(),
    audioQuality: {
      sampleRate: 48000,
      bitrate: 128000,
      channels: 2,
      codec: 'aac',
      volumeLevels: {
        average: -20,
        peak: -10,
        silent_segments: [[50, 55], [200, 203]]
      },
      issues: ['background noise']
    },
    transcript: [
      {
        startTime: 0,
        endTime: 5,
        text: 'Um, hello everyone',
        confidence: 0.95
      }
    ]
  };

  const mockProject: EditProject = {
    id: 'project-1',
    inputPath: '/test/video.mp4',
    outputPath: undefined,
    analysis: mockAnalysis,
    timeline: { duration: 0, tracks: { video: [], audio: [] } },
    appliedEdits: [],
    suggestions: [],
    status: 'draft',
    createdAt: new Date(),
    modifiedAt: new Date()
  };

  const mockSuggestions: EditSuggestion[] = [
    {
      id: 'sug-1',
      type: 'remove_silence',
      description: 'Remove 2 silent segments',
      impact: 'high',
      autoApplicable: true,
      estimatedTimeSaved: 8,
      parameters: {}
    },
    {
      id: 'sug-2',
      type: 'remove_filler',
      description: 'Remove filler words',
      impact: 'medium',
      autoApplicable: true,
      parameters: {}
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should show empty state when no file is selected', () => {
      render(<SmartEditPanel />);

      expect(screen.getByText('No Content Selected')).toBeInTheDocument();
      expect(screen.getByText('Select a video to start smart editing')).toBeInTheDocument();
    });

    it('should initialize project when file and analysis are provided', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        expect(smartEditor.createProject).toHaveBeenCalledWith('/test/video.mp4', mockAnalysis);
        expect(smartEditor.generateEditSuggestions).toHaveBeenCalledWith(mockAnalysis);
      });
    });

    it('should display error if project initialization fails', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockRejectedValueOnce(new Error('Init failed'));

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Failed to initialize editing project')).toBeInTheDocument();
      });
    });
  });

  describe('Quick Edit Options', () => {
    it('should display all edit options', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Remove Silence')).toBeInTheDocument();
        expect(screen.getByText('Remove Filler Words')).toBeInTheDocument();
        expect(screen.getByText('Enhance Audio')).toBeInTheDocument();
        expect(screen.getByText('Generate Captions')).toBeInTheDocument();
        expect(screen.getByText('Color Correction')).toBeInTheDocument();
        expect(screen.getByText('Auto Transitions')).toBeInTheDocument();
      });
    });

    it('should toggle edit options', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        // Find checkbox by looking for the label text and then the associated checkbox
        const silenceLabel = screen.getByText('Remove Silence').closest('label');
        const silenceCheckbox = silenceLabel?.querySelector('input[type="checkbox"]') as HTMLInputElement;
        expect(silenceCheckbox).toBeTruthy();
        expect(silenceCheckbox.checked).toBe(true);
      });

      const audioLabel = screen.getByText('Enhance Audio').closest('label');
      const audioCheckbox = audioLabel?.querySelector('input[type="checkbox"]') as HTMLInputElement;
      
      expect(audioCheckbox.checked).toBe(false);
      fireEvent.click(audioCheckbox);
      expect(audioCheckbox.checked).toBe(true);
    });

    it('should apply quick edits', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      
      const editedProject = { ...mockProject, outputPath: '/tmp/edited.mp4' };
      vi.mocked(smartEditor.applyAutoEdits).mockResolvedValueOnce(editedProject);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        const applyButton = screen.getByText('Apply Quick Edits');
        expect(applyButton).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Apply Quick Edits'));

      await waitFor(() => {
        expect(smartEditor.applyAutoEdits).toHaveBeenCalledWith(
          'project-1',
          expect.objectContaining({
            removeSilence: true,
            removeFillerWords: true,
            enhanceAudio: false,
            generateCaptions: false
          })
        );
      });
    });
  });

  describe('AI Suggestions', () => {
    it('should display suggestions', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Remove 2 silent segments')).toBeInTheDocument();
        expect(screen.getByText('Remove filler words')).toBeInTheDocument();
        expect(screen.getByText('high impact')).toBeInTheDocument();
        expect(screen.getByText('medium impact')).toBeInTheDocument();
      });
    });

    it('should auto-select high impact suggestions', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        // High impact suggestion should be selected (has blue background)
        // Find the suggestion container div (has border and bg classes)
        const suggestions = screen.getByText('AI Suggestions')
          .parentElement?.parentElement?.querySelectorAll('div[class*="rounded-lg border"]');
        
        // First suggestion should be the high impact one and should be selected
        expect(suggestions?.[0]?.className).toContain('bg-blue-500/10');
      });
    });

    it('should apply individual suggestions', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      
      const editedProject = { ...mockProject, appliedEdits: [mockSuggestions[0]] };
      vi.mocked(smartEditor.applyEditSuggestion).mockResolvedValueOnce(editedProject);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        const applyButtons = screen.getAllByText('Apply');
        expect(applyButtons.length).toBeGreaterThan(0);
      });

      const firstApplyButton = screen.getAllByText('Apply')[0];
      fireEvent.click(firstApplyButton);

      await waitFor(() => {
        expect(smartEditor.applyEditSuggestion).toHaveBeenCalledWith('project-1', 'sug-1');
      });
    });
  });

  describe('Highlight Generation', () => {
    it('should generate highlights', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      
      const highlightResult = {
        outputPath: '/tmp/highlights.mp4',
        highlights: []
      };
      vi.mocked(smartEditor.generateHighlightReel).mockResolvedValueOnce(highlightResult);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        const highlightButton = screen.getByText('Generate Highlight Reel');
        expect(highlightButton).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Generate Highlight Reel'));

      await waitFor(() => {
        expect(smartEditor.generateHighlightReel).toHaveBeenCalledWith(
          '/test/video.mp4',
          mockAnalysis,
          expect.objectContaining({
            duration: 60,
            criteria: 'engagement',
            includeHook: true,
            transitionStyle: 'fade'
          })
        );
      });
    });
  });

  describe('Export Functionality', () => {
    it('should show export options when output is available', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      const projectWithOutput = { ...mockProject, outputPath: '/tmp/edited.mp4' };
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(projectWithOutput);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        expect(screen.getByText('Export Video')).toBeInTheDocument();
      });
      
      // Preview button only shows if previewUrl is set, which happens after processing
      const exportSection = screen.getByText('Export Options').parentElement;
      expect(exportSection).toBeInTheDocument();
    });

    it('should export video', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      const projectWithOutput = { ...mockProject, outputPath: '/tmp/edited.mp4' };
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(projectWithOutput);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      vi.mocked(smartEditor.exportProject).mockResolvedValueOnce('/output/final.mp4');

      const onExport = vi.fn();
      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis}
          onExport={onExport}
        />
      );

      await waitFor(() => {
        const exportButton = screen.getByText('Export Video');
        expect(exportButton).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Export Video'));

      await waitFor(() => {
        expect(smartEditor.exportProject).toHaveBeenCalled();
        expect(onExport).toHaveBeenCalledWith('/output/final.mp4');
      });
    });
  });

  describe('Progress and Status', () => {
    it('should show processing state', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      
      // Make applyAutoEdits take some time
      let resolveEdit: (value: any) => void;
      const editPromise = new Promise((resolve) => {
        resolveEdit = resolve;
      });
      vi.mocked(smartEditor.applyAutoEdits).mockReturnValue(editPromise as Promise<EditProject>);

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        const applyButton = screen.getByText('Apply Quick Edits');
        expect(applyButton).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Apply Quick Edits'));

      // Check for processing state immediately after click
      await waitFor(() => {
        expect(screen.getByText('Applying automatic edits...')).toBeInTheDocument();
      });
      
      // Now resolve the promise to complete the edit
      resolveEdit!({ ...mockProject, outputPath: '/tmp/edited.mp4' });
    });

    it('should show error messages', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);
      vi.mocked(smartEditor.applyAutoEdits).mockRejectedValueOnce(new Error('Edit failed'));

      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis} 
        />
      );

      await waitFor(() => {
        const applyButton = screen.getByText('Apply Quick Edits');
        expect(applyButton).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Apply Quick Edits'));

      await waitFor(() => {
        expect(screen.getByText('Failed to apply automatic edits')).toBeInTheDocument();
      });
    });
  });

  describe('Close Functionality', () => {
    it('should call onClose when close button is clicked', async () => {
      const { smartEditor } = await import('../../../ai/SmartEditor');
      vi.mocked(smartEditor.createProject).mockResolvedValueOnce(mockProject);
      vi.mocked(smartEditor.generateEditSuggestions).mockResolvedValueOnce(mockSuggestions);

      const onClose = vi.fn();
      render(
        <SmartEditPanel 
          filePath="/test/video.mp4" 
          analysis={mockAnalysis}
          onClose={onClose}
        />
      );

      await waitFor(() => {
        // Find the X icon button
        const closeButton = screen.getByText('Smart Edit Panel')
          .parentElement?.parentElement?.querySelector('button');
        expect(closeButton).toBeTruthy();
        fireEvent.click(closeButton!);
      });

      expect(onClose).toHaveBeenCalled();
    });
  });
});