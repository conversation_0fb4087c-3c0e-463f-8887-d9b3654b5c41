import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DownloadItem from './DownloadItem';
import { useDownloadStore } from '../store/downloadStore';
import { createMockDownload } from '../test/utils';
import type { Download } from '../store/downloadStore';

// Mock the download store
vi.mock('../store/downloadStore', () => ({
  useDownloadStore: vi.fn(),
}));

// Mock react-toastify
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock folder utils
vi.mock('../utils/folderUtils', () => ({
  formatPathForDisplay: vi.fn((path: string) => path),
}));

// Mock Tauri utils
vi.mock('../utils/tauriUtils', () => ({
  withTauriAPIs: vi.fn((fn) => fn),
  ensureFolderAccessible: vi.fn().mockResolvedValue(true),
}));

describe('DownloadItem Component', () => {
  const mockRemoveDownload = vi.fn();
  const mockUpdateDownload = vi.fn();
  let mockDownload: Download;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock download
    mockDownload = createMockDownload({
      id: '1',
      url: 'https://youtube.com/watch?v=test',
      filename: 'test-video.mp4',
      status: 'pending',
      progress: 0,
    });

    // Mock the download store
    vi.mocked(useDownloadStore).mockReturnValue({
      removeDownload: mockRemoveDownload,
      updateDownload: mockUpdateDownload,
      downloads: [mockDownload],
      addDownload: vi.fn(),
      clearCompleted: vi.fn(),
      setDefaultDownloadPath: vi.fn(),
      initializeDefaultPath: vi.fn(),
      defaultDownloadPath: '/Users/<USER>/Downloads',
      isInitialized: true,
    });

    // Window is already stubbed with vi.stubGlobal above
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Rendering', () => {
    it('should render download item with basic information', () => {
      render(<DownloadItem download={mockDownload} />);

      expect(screen.getByText('test-video.mp4')).toBeInTheDocument();
      expect(screen.getByText('https://youtube.com/watch?v=test')).toBeInTheDocument();
    });

    it('should display pending status correctly', () => {
      render(<DownloadItem download={mockDownload} />);

      const statusElement = screen.getByText(/pending/i);
      expect(statusElement).toBeInTheDocument();
      expect(statusElement).toHaveClass('text-gray-600');
    });

    it('should display downloading status with progress', () => {
      const downloadingDownload = createMockDownload({
        ...mockDownload,
        status: 'downloading',
        progress: 45,
        downloadSpeed: '2.5 MB/s',
        timeRemaining: '00:02:30',
      });

      render(<DownloadItem download={downloadingDownload} />);

      expect(screen.getByText(/downloading/i)).toBeInTheDocument();
      expect(screen.getByText('45%')).toBeInTheDocument();
      expect(screen.getByText('2.5 MB/s')).toBeInTheDocument();
      expect(screen.getByText('00:02:30')).toBeInTheDocument();
    });

    it('should display completed status', () => {
      const completedDownload = createMockDownload({
        ...mockDownload,
        status: 'completed',
        progress: 100,
        fileSize: 52428800, // 50 MB
      });

      render(<DownloadItem download={completedDownload} />);

      const statusElement = screen.getByText(/completed/i);
      expect(statusElement).toBeInTheDocument();
      expect(statusElement).toHaveClass('text-green-600');
      expect(screen.getByText('100%')).toBeInTheDocument();
    });

    it('should display error status with error message', () => {
      const errorDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Network timeout',
      });

      render(<DownloadItem download={errorDownload} />);

      const statusElement = screen.getByText(/error/i);
      expect(statusElement).toBeInTheDocument();
      expect(statusElement).toHaveClass('text-red-600');
      expect(screen.getByText('Network timeout')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display "Download failed" error correctly', () => {
      const failedDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Download failed',
      });

      render(<DownloadItem download={failedDownload} />);

      expect(screen.getByText('Download failed')).toBeInTheDocument();
      expect(screen.getByText(/error/i)).toHaveClass('text-red-600');
    });

    it('should display "Unknown size • Unknown time" for failed downloads', () => {
      const failedDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Download failed',
      });

      render(<DownloadItem download={failedDownload} />);

      expect(screen.getByText('Download Failed')).toBeInTheDocument();
      expect(screen.getByText('Unknown size • Unknown time')).toBeInTheDocument();
      expect(screen.getByText('Download failed')).toBeInTheDocument();
    });

    it('should identify missing tools errors', () => {
      const missingToolsDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'yt-dlp is required for this download',
      });

      render(<DownloadItem download={missingToolsDownload} />);

      expect(screen.getByText('yt-dlp is required for this download')).toBeInTheDocument();
      // Should show installation instructions
      expect(screen.getByText(/install the required tools/i)).toBeInTheDocument();
    });

    it('should show installation instructions for yt-dlp errors', () => {
      const ytDlpErrorDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'yt-dlp command not found',
      });

      render(<DownloadItem download={ytDlpErrorDownload} />);

      expect(screen.getByText(/brew install yt-dlp/)).toBeInTheDocument();
      expect(screen.getByText(/pip install yt-dlp/)).toBeInTheDocument();
    });

    it('should show installation instructions for gallery-dl errors', () => {
      const galleryDlErrorDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'gallery-dl not installed',
      });

      render(<DownloadItem download={galleryDlErrorDownload} />);

      expect(screen.getByText(/brew install gallery-dl/)).toBeInTheDocument();
      expect(screen.getByText(/pip install gallery-dl/)).toBeInTheDocument();
    });

    it('should handle network timeout errors', () => {
      const timeoutDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Network timeout after 30 seconds',
      });

      render(<DownloadItem download={timeoutDownload} />);

      expect(screen.getByText('Network timeout after 30 seconds')).toBeInTheDocument();
    });

    it('should handle permission denied errors', () => {
      const permissionErrorDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Permission denied: Cannot write to /restricted/folder',
      });

      render(<DownloadItem download={permissionErrorDownload} />);

      expect(screen.getByText('Permission denied: Cannot write to /restricted/folder')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should remove download when remove button is clicked', async () => {
      const user = userEvent.setup();
      render(<DownloadItem download={mockDownload} />);

      // Open dropdown menu
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Click remove button
      const removeButton = screen.getByText(/remove/i);
      await user.click(removeButton);

      expect(mockRemoveDownload).toHaveBeenCalledWith(mockDownload.id);
    });

    it('should open folder when open folder button is clicked', async () => {
      const user = userEvent.setup();
      const completedDownload = createMockDownload({
        ...mockDownload,
        status: 'completed',
      });

      render(<DownloadItem download={completedDownload} />);

      // Open dropdown menu
      const menuButton = screen.getByRole('button', { name: /options/i });
      await user.click(menuButton);

      // Click open folder button
      const openFolderButton = screen.getByText(/open folder/i);
      await user.click(openFolderButton);

      // The withTauriAPIs mock should be called - we can verify the behavior through toast messages
      // Since we're mocking withTauriAPIs to call the provided function directly,
      // we can check that the success toast was called
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('should retry download when retry button is clicked', async () => {
      const user = userEvent.setup();
      const errorDownload = createMockDownload({
        ...mockDownload,
        status: 'error',
        error: 'Network timeout',
      });

      render(<DownloadItem download={errorDownload} />);

      // Open dropdown menu
      const menuButton = screen.getByRole('button', { name: /options/i });
      await user.click(menuButton);

      // Click retry button
      const retryButton = screen.getByText(/retry/i);
      await user.click(retryButton);

      expect(mockUpdateDownload).toHaveBeenCalledWith(errorDownload.id, {
        status: 'pending',
        error: undefined,
        progress: 0,
      });
    });

    it('should close dropdown when clicking outside', async () => {
      const user = userEvent.setup();
      render(
        <div>
          <DownloadItem download={mockDownload} />
          <div data-testid="outside">Outside element</div>
        </div>
      );

      // Open dropdown menu
      const menuButton = screen.getByRole('button', { name: /options/i });
      await user.click(menuButton);

      // Verify dropdown is open
      expect(screen.getByText(/remove/i)).toBeInTheDocument();

      // Click outside
      const outsideElement = screen.getByTestId('outside');
      await user.click(outsideElement);

      // Wait for dropdown to close
      await waitFor(() => {
        expect(screen.queryByText(/remove/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Progress Display', () => {
    it('should display progress bar correctly', () => {
      const progressDownload = createMockDownload({
        ...mockDownload,
        status: 'downloading',
        progress: 75,
      });

      render(<DownloadItem download={progressDownload} />);

      const progressBar = screen.getByRole('progressbar');
      expect(progressBar).toBeInTheDocument();
      expect(progressBar).toHaveAttribute('aria-valuenow', '75');
      expect(progressBar).toHaveAttribute('aria-valuemin', '0');
      expect(progressBar).toHaveAttribute('aria-valuemax', '100');
    });

    it('should format file sizes correctly', () => {
      const largeFileDownload = createMockDownload({
        ...mockDownload,
        status: 'completed',
        fileSize: 1073741824, // 1 GB
      });

      render(<DownloadItem download={largeFileDownload} />);

      expect(screen.getByText('1.0 GB')).toBeInTheDocument();
    });

    it('should handle zero file size', () => {
      const zeroSizeDownload = createMockDownload({
        ...mockDownload,
        status: 'completed',
        fileSize: 0,
      });

      render(<DownloadItem download={zeroSizeDownload} />);

      expect(screen.getByText('0 B')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<DownloadItem download={mockDownload} />);

      const menuButton = screen.getByRole('button', { name: /options/i });
      expect(menuButton).toHaveAttribute('aria-label');

      if (mockDownload.status === 'downloading') {
        const progressBar = screen.getByRole('progressbar');
        expect(progressBar).toHaveAttribute('aria-label');
      }
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      render(<DownloadItem download={mockDownload} />);

      const menuButton = screen.getByRole('button', { name: /options/i });
      
      // Tab to the menu button
      await user.tab();
      expect(menuButton).toHaveFocus();

      // Press Enter to open menu
      await user.keyboard('{Enter}');
      expect(screen.getByText(/remove/i)).toBeInTheDocument();
    });
  });
});
