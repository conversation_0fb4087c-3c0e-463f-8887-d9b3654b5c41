import React, { useState } from 'react';
import { Monitor, Smartphone, Headphones } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import { colors, typography } from '../design-system';

interface DeviceOptimizationProps {
  onSelectDevice?: (device: 'desktop' | 'mobile' | 'audio', preset: string) => void;
}

interface DeviceOption {
  id: 'desktop' | 'mobile' | 'audio';
  icon: React.ReactNode;
  label: string;
  description: string;
  presets: string;
  color: string;
  gradient: string;
}

const DeviceOptimization: React.FC<DeviceOptimizationProps> = ({ onSelectDevice }) => {
  const [selectedDevice, setSelectedDevice] = useState<'desktop' | 'mobile' | 'audio' | null>(null);

  const deviceOptions: DeviceOption[] = [
    {
      id: 'desktop',
      icon: <Monitor className="w-8 h-8" />,
      label: 'Desktop',
      description: 'Ultra-high quality downloads optimized for large screens',
      presets: '1080p - 4K',
      color: 'text-blue-400',
      gradient: 'linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%)',
    },
    {
      id: 'mobile',
      icon: <Smartphone className="w-8 h-8" />,
      label: 'Mobile',
      description: 'Compressed formats perfect for mobile viewing',
      presets: '360p - 720p',
      color: 'text-green-400',
      gradient: 'linear-gradient(135deg, #10B981 0%, #047857 100%)',
    },
    {
      id: 'audio',
      icon: <Headphones className="w-8 h-8" />,
      label: 'Audio',
      description: 'Extract high-quality audio in multiple formats',
      presets: 'MP3, AAC, FLAC',
      color: 'text-purple-400',
      gradient: 'linear-gradient(135deg, #8B5CF6 0%, #6D28D9 100%)',
    },
  ];

  const handleDeviceSelect = (device: DeviceOption) => {
    setSelectedDevice(device.id);
    
    // Apply preset based on device
    let preset = '';
    switch(device.id) {
      case 'desktop':
        preset = '1080p';
        break;
      case 'mobile':
        preset = '720p';
        break;
      case 'audio':
        preset = 'Audio Only (High)';
        break;
    }
    
    if (onSelectDevice) {
      onSelectDevice(device.id, preset);
    }
    
    // Update the quality selector in the main form
    const qualitySelector = document.querySelector('[data-quality-selector]');
    if (qualitySelector) {
      // This would trigger the quality selection
      const event = new CustomEvent('device-optimize', { 
        detail: { device: device.id, preset } 
      });
      window.dispatchEvent(event);
    }
  };

  return (
    <GlassCard variant="elevated" className="mb-6">
      <div className="p-6">
        <h2 
          className="text-xl font-bold text-white mb-6"
          style={{ fontFamily: typography.fonts.heading }}
        >
          Device Optimization
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {deviceOptions.map((device) => (
            <div
              key={device.id}
              onClick={() => handleDeviceSelect(device)}
              className={`
                relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300
                ${selectedDevice === device.id 
                  ? 'glass-strong border-2 scale-105' 
                  : 'glass-light border border-glass-border hover:glass-medium hover:scale-102'
                }
              `}
              style={{
                borderColor: selectedDevice === device.id ? device.color.replace('text-', '') : undefined
              }}
            >
              {/* Background gradient when selected */}
              {selectedDevice === device.id && (
                <div 
                  className="absolute inset-0 opacity-10"
                  style={{
                    background: device.gradient,
                    filter: 'blur(30px)',
                  }}
                />
              )}

              <div className="relative z-10">
                <div className="flex flex-col items-center text-center">
                  <div 
                    className={`mb-4 ${device.color} ${selectedDevice === device.id ? 'animate-pulse' : ''}`}
                  >
                    {device.icon}
                  </div>
                  
                  <h3 
                    className="text-lg font-semibold text-white mb-2"
                    style={{ fontFamily: typography.fonts.heading }}
                  >
                    {device.label}
                  </h3>
                  
                  <p className="text-xs text-gray-400 mb-4">
                    {device.description}
                  </p>
                  
                  <div className="w-full pt-4 border-t border-glass-border">
                    <div className="flex justify-between items-center text-xs">
                      <span className="text-gray-500">Presets:</span>
                      <span className={`font-medium ${device.color}`}>
                        {device.presets}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </GlassCard>
  );
};

export default DeviceOptimization;