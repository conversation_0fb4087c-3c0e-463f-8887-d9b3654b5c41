import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Users, DollarSign, Eye, Heart, Zap, Activity } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { typography } from '../design-system';
import { invoke } from '@tauri-apps/api/core';

interface MetricData {
  value: number;
  change: number;
  trend: 'up' | 'down' | 'neutral';
  sparkline: number[];
}

interface PlatformMetrics {
  youtube: {
    subscribers: MetricData;
    views: MetricData;
    revenue: MetricData;
    engagement: MetricData;
  };
  tiktok: {
    followers: MetricData;
    views: MetricData;
    likes: MetricData;
    shares: MetricData;
  };
  instagram: {
    followers: MetricData;
    reach: MetricData;
    engagement: MetricData;
    stories: MetricData;
  };
}

interface LiveMetrics {
  activeUploads: number;
  processingQueue: number;
  totalBandwidth: number;
  systemHealth: number;
}

const HeroMetrics: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'24h' | '7d' | '30d' | '90d'>('7d');
  const [platformMetrics, setPlatformMetrics] = useState<PlatformMetrics>({
    youtube: {
      subscribers: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      views: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      revenue: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      engagement: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
    },
    tiktok: {
      followers: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      views: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      likes: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      shares: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
    },
    instagram: {
      followers: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      reach: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      engagement: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
      stories: { value: 0, change: 0, trend: 'neutral', sparkline: [] },
    },
  });
  
  const [liveMetrics, setLiveMetrics] = useState<LiveMetrics>({
    activeUploads: 0,
    processingQueue: 0,
    totalBandwidth: 0,
    systemHealth: 100,
  });

  const [totalMetrics, setTotalMetrics] = useState({
    totalFollowers: 0,
    totalViews: 0,
    totalRevenue: 0,
    avgEngagement: 0,
  });

  useEffect(() => {
    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000); // Update every 30 seconds
    
    // Real-time metrics update
    const realtimeInterval = setInterval(updateLiveMetrics, 5000); // Update every 5 seconds
    
    return () => {
      clearInterval(interval);
      clearInterval(realtimeInterval);
    };
  }, [selectedPeriod]);

  const fetchMetrics = async () => {
    try {
      // Fetch platform metrics
      const metrics = await invoke<any>('get_platform_metrics', { period: selectedPeriod });
      
      if (metrics) {
        setPlatformMetrics(metrics);
        
        // Calculate totals
        const ytSubs = metrics.youtube?.subscribers?.value || 0;
        const ttFollowers = metrics.tiktok?.followers?.value || 0;
        const igFollowers = metrics.instagram?.followers?.value || 0;
        
        const ytViews = metrics.youtube?.views?.value || 0;
        const ttViews = metrics.tiktok?.views?.value || 0;
        const igReach = metrics.instagram?.reach?.value || 0;
        
        const ytRevenue = metrics.youtube?.revenue?.value || 0;
        const ytEngagement = metrics.youtube?.engagement?.value || 0;
        const ttEngagement = ((metrics.tiktok?.likes?.value || 0) / (ttViews || 1)) * 100;
        const igEngagement = metrics.instagram?.engagement?.value || 0;
        
        setTotalMetrics({
          totalFollowers: ytSubs + ttFollowers + igFollowers,
          totalViews: ytViews + ttViews + igReach,
          totalRevenue: ytRevenue,
          avgEngagement: (ytEngagement + ttEngagement + igEngagement) / 3,
        });
      }
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
      // Use demo data for now
      generateDemoData();
    }
  };

  const updateLiveMetrics = async () => {
    try {
      const live = await invoke<LiveMetrics>('get_live_metrics');
      setLiveMetrics(live);
    } catch (error) {
      // Use demo data
      setLiveMetrics({
        activeUploads: Math.floor(Math.random() * 5),
        processingQueue: Math.floor(Math.random() * 10),
        totalBandwidth: Math.random() * 100,
        systemHealth: 95 + Math.random() * 5,
      });
    }
  };

  const generateDemoData = () => {
    // Generate realistic demo data
    const generateSparkline = () => Array.from({ length: 10 }, () => Math.random() * 100);
    
    setPlatformMetrics({
      youtube: {
        subscribers: { value: 125430, change: 5.2, trend: 'up', sparkline: generateSparkline() },
        views: { value: 2847500, change: 12.8, trend: 'up', sparkline: generateSparkline() },
        revenue: { value: 15782.50, change: 8.3, trend: 'up', sparkline: generateSparkline() },
        engagement: { value: 4.7, change: 0.3, trend: 'up', sparkline: generateSparkline() },
      },
      tiktok: {
        followers: { value: 89200, change: 15.4, trend: 'up', sparkline: generateSparkline() },
        views: { value: 5632000, change: 22.1, trend: 'up', sparkline: generateSparkline() },
        likes: { value: 423000, change: 18.7, trend: 'up', sparkline: generateSparkline() },
        shares: { value: 12300, change: -2.1, trend: 'down', sparkline: generateSparkline() },
      },
      instagram: {
        followers: { value: 67800, change: 3.2, trend: 'up', sparkline: generateSparkline() },
        reach: { value: 892000, change: -5.4, trend: 'down', sparkline: generateSparkline() },
        engagement: { value: 6.2, change: 0.8, trend: 'up', sparkline: generateSparkline() },
        stories: { value: 45200, change: 12.1, trend: 'up', sparkline: generateSparkline() },
      },
    });
    
    setTotalMetrics({
      totalFollowers: 282430,
      totalViews: 9371500,
      totalRevenue: 15782.50,
      avgEngagement: 5.2,
    });
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toFixed(0);
  };

  const formatCurrency = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(num);
  };

  const renderSparkline = (data: number[]) => {
    const max = Math.max(...data);
    const min = Math.min(...data);
    const height = 32;
    const width = 100;
    
    const points = data.map((value, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((value - min) / (max - min)) * height;
      return `${x},${y}`;
    }).join(' ');
    
    return (
      <svg width={width} height={height} className="ml-auto">
        <polyline
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          points={points}
          className="text-primary-400"
        />
      </svg>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 
            className="text-3xl font-bold text-white mb-2"
            style={{ fontFamily: typography.fonts.heading }}
          >
            Creator Dashboard
          </h2>
          <p className="text-gray-400">Real-time performance metrics across all platforms</p>
        </div>
        
        {/* Period Selector */}
        <div className="flex items-center space-x-2 glass-light rounded-lg p-1">
          {(['24h', '7d', '30d', '90d'] as const).map((period) => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                selectedPeriod === period
                  ? 'glass-medium text-white shadow-glow'
                  : 'text-gray-400 hover:text-white hover:bg-glass-light'
              }`}
            >
              {period === '24h' ? 'Day' : period === '7d' ? 'Week' : period === '30d' ? 'Month' : 'Quarter'}
            </button>
          ))}
        </div>
      </div>

      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Followers */}
        <GlassCard variant="elevated" className="p-6 hover:scale-105 transition-transform">
          <div className="flex items-start justify-between mb-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">Total Followers</p>
              <h3 className="text-3xl font-bold text-white">
                {formatNumber(totalMetrics.totalFollowers)}
              </h3>
            </div>
            <div className="p-3 glass-strong rounded-lg">
              <Users className="w-6 h-6 text-primary-400" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              <span className="text-sm text-green-400">+8.2%</span>
            </div>
            {renderSparkline([65, 70, 68, 72, 75, 78, 82, 85, 88, 92])}
          </div>
        </GlassCard>

        {/* Total Views */}
        <GlassCard variant="elevated" className="p-6 hover:scale-105 transition-transform">
          <div className="flex items-start justify-between mb-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">Total Views</p>
              <h3 className="text-3xl font-bold text-white">
                {formatNumber(totalMetrics.totalViews)}
              </h3>
            </div>
            <div className="p-3 glass-strong rounded-lg">
              <Eye className="w-6 h-6 text-accent-400" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              <span className="text-sm text-green-400">+15.4%</span>
            </div>
            {renderSparkline([55, 60, 58, 65, 70, 75, 80, 85, 90, 95])}
          </div>
        </GlassCard>

        {/* Revenue */}
        <GlassCard variant="elevated" className="p-6 hover:scale-105 transition-transform">
          <div className="flex items-start justify-between mb-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">Revenue</p>
              <h3 className="text-3xl font-bold text-white">
                {formatCurrency(totalMetrics.totalRevenue)}
              </h3>
            </div>
            <div className="p-3 glass-strong rounded-lg">
              <DollarSign className="w-6 h-6 text-green-400" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              <span className="text-sm text-green-400">+12.1%</span>
            </div>
            {renderSparkline([70, 72, 75, 73, 78, 82, 85, 88, 92, 95])}
          </div>
        </GlassCard>

        {/* Engagement Rate */}
        <GlassCard variant="elevated" className="p-6 hover:scale-105 transition-transform">
          <div className="flex items-start justify-between mb-4">
            <div>
              <p className="text-sm text-gray-400 mb-1">Avg Engagement</p>
              <h3 className="text-3xl font-bold text-white">
                {totalMetrics.avgEngagement.toFixed(1)}%
              </h3>
            </div>
            <div className="p-3 glass-strong rounded-lg">
              <Heart className="w-6 h-6 text-red-400" />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              <span className="text-sm text-green-400">+0.8%</span>
            </div>
            {renderSparkline([4.2, 4.3, 4.5, 4.4, 4.6, 4.7, 4.8, 4.9, 5.0, 5.2])}
          </div>
        </GlassCard>
      </div>

      {/* Live Activity Bar */}
      <GlassCard variant="elevated" className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center">
              <Activity className="w-5 h-5 text-green-400 mr-2 animate-pulse" />
              <span className="text-sm text-gray-400">Live Activity</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2" />
                <span className="text-sm text-white">{liveMetrics.activeUploads} Active Uploads</span>
              </div>
              
              <div className="flex items-center">
                <Zap className="w-4 h-4 text-yellow-400 mr-2" />
                <span className="text-sm text-white">{liveMetrics.processingQueue} Processing</span>
              </div>
              
              <div className="flex items-center">
                <div className="w-32 h-2 glass-light rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-primary-400 to-accent-400 transition-all duration-500"
                    style={{ width: `${liveMetrics.totalBandwidth}%` }}
                  />
                </div>
                <span className="text-sm text-gray-400 ml-2">{liveMetrics.totalBandwidth.toFixed(0)}% Bandwidth</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">System Health</span>
            <div className="flex items-center">
              <div className="w-24 h-2 glass-light rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-400 transition-all duration-500"
                  style={{ width: `${liveMetrics.systemHealth}%` }}
                />
              </div>
              <span className="text-sm text-green-400 ml-2">{liveMetrics.systemHealth.toFixed(0)}%</span>
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Platform Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* YouTube Metrics */}
        <GlassCard variant="elevated" className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">YouTube</h3>
            <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">YT</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Subscribers</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.youtube.subscribers.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.youtube.subscribers.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.youtube.subscribers.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.youtube.subscribers.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Views</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.youtube.views.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.youtube.views.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.youtube.views.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.youtube.views.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Revenue</span>
                <span className="text-sm font-medium text-white">
                  {formatCurrency(platformMetrics.youtube.revenue.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.youtube.revenue.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.youtube.revenue.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.youtube.revenue.change}%
                </span>
              </div>
            </div>
          </div>
        </GlassCard>

        {/* TikTok Metrics */}
        <GlassCard variant="elevated" className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">TikTok</h3>
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">TT</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Followers</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.tiktok.followers.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.tiktok.followers.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.tiktok.followers.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.tiktok.followers.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Views</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.tiktok.views.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.tiktok.views.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.tiktok.views.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.tiktok.views.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Likes</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.tiktok.likes.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.tiktok.likes.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.tiktok.likes.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.tiktok.likes.change}%
                </span>
              </div>
            </div>
          </div>
        </GlassCard>

        {/* Instagram Metrics */}
        <GlassCard variant="elevated" className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Instagram</h3>
            <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
              <span className="text-white text-xs font-bold">IG</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Followers</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.instagram.followers.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.instagram.followers.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.instagram.followers.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.instagram.followers.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Reach</span>
                <span className="text-sm font-medium text-white">
                  {formatNumber(platformMetrics.instagram.reach.value)}
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.instagram.reach.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.instagram.reach.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.instagram.reach.change}%
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-gray-400">Engagement</span>
                <span className="text-sm font-medium text-white">
                  {platformMetrics.instagram.engagement.value}%
                </span>
              </div>
              <div className="flex items-center">
                {platformMetrics.instagram.engagement.trend === 'up' ? (
                  <TrendingUp className="w-3 h-3 text-green-400 mr-1" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-400 mr-1" />
                )}
                <span className={`text-xs ${
                  platformMetrics.instagram.engagement.trend === 'up' ? 'text-green-400' : 'text-red-400'
                }`}>
                  {platformMetrics.instagram.engagement.change}%
                </span>
              </div>
            </div>
          </div>
        </GlassCard>
      </div>
    </div>
  );
};

export default HeroMetrics;