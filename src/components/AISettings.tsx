import React, { useState, useEffect } from 'react';
import { Settings, Key, Zap, Shield, DollarSign, Check, X } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import { AI_PROVIDERS, AI_FEATURES, AISettings, DEFAULT_AI_SETTINGS } from '../config/aiConfig';
import { Store } from '@tauri-apps/plugin-store';

let store: Store;
const initStore = async () => {
  if (!store) {
    store = await Store.load('ai-settings.json');
  }
  return store;
};

export const AISettingsPanel: React.FC = () => {
  const [settings, setSettings] = useState<AISettings>(DEFAULT_AI_SETTINGS);
  const [apiKeyVisibility, setApiKeyVisibility] = useState<Record<string, boolean>>({});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const storeInstance = await initStore();
      const saved = await storeInstance.get<AISettings>('ai-settings');
      if (saved) {
        setSettings(saved);
      }
    } catch (error) {
      console.error('Failed to load AI settings:', error);
    }
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const storeInstance = await initStore();
      await storeInstance.set('ai-settings', settings);
      await storeInstance.save();
      // Reinitialize AI factory with new settings
      const { AIProviderFactory } = await import('../ai/AIProviderFactory');
      AIProviderFactory.initialize(settings);
    } catch (error) {
      console.error('Failed to save AI settings:', error);
    }
    setIsSaving(false);
  };

  const updateTier = (tier: 'free' | 'basic' | 'premium') => {
    // Update provider selections based on tier
    const newProviders = { ...settings.providers };
    
    Object.entries(AI_FEATURES).forEach(([featureId, feature]) => {
      const provider = feature.providers[tier];
      if (provider) {
        newProviders[featureId] = provider;
      }
    });

    setSettings({
      ...settings,
      tier,
      providers: newProviders
    });
  };

  const updateApiKey = (provider: string, key: string) => {
    setSettings({
      ...settings,
      apiKeys: {
        ...settings.apiKeys,
        [provider]: key
      }
    });
  };

  const toggleApiKeyVisibility = (provider: string) => {
    setApiKeyVisibility({
      ...apiKeyVisibility,
      [provider]: !apiKeyVisibility[provider]
    });
  };

  const tierColors = {
    free: 'bg-green-500',
    basic: 'bg-blue-500',
    premium: 'bg-purple-500'
  };

  return (
    <div className="space-y-6">
      {/* Tier Selection */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5" />
            AI Processing Tier
          </h3>
          
          <div className="grid grid-cols-3 gap-4">
            {/* Free Tier */}
            <div
              onClick={() => updateTier('free')}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${settings.tier === 'free' 
                  ? 'border-green-500 bg-green-500/10' 
                  : 'border-gray-700 hover:border-gray-600'
                }
              `}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-white">Free</h4>
                {settings.tier === 'free' && <Check className="w-5 h-5 text-green-500" />}
              </div>
              <p className="text-sm text-gray-400 mb-3">Local processing only</p>
              <ul className="space-y-1 text-xs text-gray-500">
                <li>• Basic scene detection</li>
                <li>• Video metadata</li>
                <li>• Simple editing</li>
              </ul>
              <div className="mt-3 text-green-500 font-semibold">$0/month</div>
            </div>

            {/* Basic Tier */}
            <div
              onClick={() => updateTier('basic')}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${settings.tier === 'basic' 
                  ? 'border-blue-500 bg-blue-500/10' 
                  : 'border-gray-700 hover:border-gray-600'
                }
              `}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-white">Basic</h4>
                {settings.tier === 'basic' && <Check className="w-5 h-5 text-blue-500" />}
              </div>
              <p className="text-sm text-gray-400 mb-3">Affordable AI features</p>
              <ul className="space-y-1 text-xs text-gray-500">
                <li>• AI transcription</li>
                <li>• Smart analysis</li>
                <li>• Basic predictions</li>
              </ul>
              <div className="mt-3 text-blue-500 font-semibold">~$5/month</div>
            </div>

            {/* Premium Tier */}
            <div
              onClick={() => updateTier('premium')}
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${settings.tier === 'premium' 
                  ? 'border-purple-500 bg-purple-500/10' 
                  : 'border-gray-700 hover:border-gray-600'
                }
              `}
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-semibold text-white">Premium</h4>
                {settings.tier === 'premium' && <Check className="w-5 h-5 text-purple-500" />}
              </div>
              <p className="text-sm text-gray-400 mb-3">State-of-the-art AI</p>
              <ul className="space-y-1 text-xs text-gray-500">
                <li>• GPT-4 analysis</li>
                <li>• Advanced insights</li>
                <li>• AI thumbnails</li>
              </ul>
              <div className="mt-3 text-purple-500 font-semibold">Usage-based</div>
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Feature Configuration */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Feature Configuration
          </h3>
          
          <div className="space-y-4">
            {Object.entries(AI_FEATURES).map(([featureId, feature]) => {
              const currentProvider = settings.providers[featureId];
              const availableProviders = Object.entries(feature.providers)
                .filter(([tier, provider]) => provider !== 'none')
                .map(([tier, provider]) => ({ tier, provider }));

              return (
                <div key={featureId} className="border border-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-white">{feature.name}</h4>
                    <span className={`text-xs px-2 py-1 rounded ${
                      currentProvider === 'none' ? 'bg-gray-700' :
                      currentProvider === 'local' ? 'bg-green-500/20 text-green-400' :
                      'bg-blue-500/20 text-blue-400'
                    }`}>
                      {currentProvider === 'none' ? 'Disabled' : 
                       AI_PROVIDERS[currentProvider]?.name || currentProvider}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mb-3">{feature.description}</p>
                  
                  {availableProviders.length > 0 && (
                    <div className="flex gap-2">
                      {availableProviders.map(({ tier, provider }) => (
                        <button
                          key={provider}
                          onClick={() => setSettings({
                            ...settings,
                            providers: { ...settings.providers, [featureId]: provider }
                          })}
                          disabled={settings.tier === 'free' && tier !== 'free'}
                          className={`
                            px-3 py-1 text-xs rounded transition-all
                            ${currentProvider === provider 
                              ? `${tierColors[tier as keyof typeof tierColors]} text-white` 
                              : 'bg-gray-800 text-gray-400 hover:bg-gray-700'
                            }
                            ${settings.tier === 'free' && tier !== 'free' 
                              ? 'opacity-50 cursor-not-allowed' 
                              : ''
                            }
                          `}
                        >
                          {AI_PROVIDERS[provider]?.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </GlassCard>

      {/* API Keys */}
      {settings.tier !== 'free' && (
        <GlassCard variant="elevated">
          <div className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Key className="w-5 h-5" />
              API Keys
            </h3>
            
            <div className="space-y-4">
              {Object.values(AI_PROVIDERS)
                .filter(provider => provider.requiresApiKey && provider.tier !== 'free')
                .map(provider => (
                  <div key={provider.id} className="space-y-2">
                    <label className="block text-sm font-medium text-gray-300">
                      {provider.name} API Key
                    </label>
                    <div className="flex gap-2">
                      <input
                        type={apiKeyVisibility[provider.id] ? 'text' : 'password'}
                        value={settings.apiKeys[provider.apiKeyField!] || ''}
                        onChange={(e) => updateApiKey(provider.apiKeyField!, e.target.value)}
                        placeholder={`Enter your ${provider.name} API key`}
                        className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-purple-500"
                      />
                      <button
                        onClick={() => toggleApiKeyVisibility(provider.id)}
                        className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-400 hover:text-white transition-colors"
                      >
                        {apiKeyVisibility[provider.id] ? '🙈' : '👁️'}
                      </button>
                    </div>
                    {provider.pricing && (
                      <p className="text-xs text-gray-500 flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        {provider.pricing}
                      </p>
                    )}
                  </div>
                ))}
            </div>
          </div>
        </GlassCard>
      )}

      {/* Privacy Settings */}
      <GlassCard variant="elevated">
        <div className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Privacy & Preferences
          </h3>
          
          <div className="space-y-3">
            <label className="flex items-center justify-between">
              <span className="text-gray-300">Privacy Mode (Local Only)</span>
              <input
                type="checkbox"
                checked={settings.preferences.privacyMode}
                onChange={(e) => setSettings({
                  ...settings,
                  preferences: { ...settings.preferences, privacyMode: e.target.checked }
                })}
                className="w-4 h-4 text-purple-500"
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span className="text-gray-300">Auto-analyze new files</span>
              <input
                type="checkbox"
                checked={settings.preferences.autoAnalyze}
                onChange={(e) => setSettings({
                  ...settings,
                  preferences: { ...settings.preferences, autoAnalyze: e.target.checked }
                })}
                className="w-4 h-4 text-purple-500"
              />
            </label>
            
            <label className="flex items-center justify-between">
              <span className="text-gray-300">Save transcripts locally</span>
              <input
                type="checkbox"
                checked={settings.preferences.saveTranscripts}
                onChange={(e) => setSettings({
                  ...settings,
                  preferences: { ...settings.preferences, saveTranscripts: e.target.checked }
                })}
                className="w-4 h-4 text-purple-500"
              />
            </label>
          </div>
        </div>
      </GlassCard>

      {/* Save Button */}
      <button
        onClick={saveSettings}
        disabled={isSaving}
        className="w-full py-3 px-4 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-700 text-white font-semibold rounded-lg transition-colors flex items-center justify-center gap-2"
      >
        {isSaving ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            Saving...
          </>
        ) : (
          <>
            <Check className="w-4 h-4" />
            Save Settings
          </>
        )}
      </button>
    </div>
  );
};