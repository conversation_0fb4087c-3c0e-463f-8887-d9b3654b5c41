import React, { useState, useEffect } from 'react';
import { TrendingUp, Download, Upload, Users, Activity, Zap, AlertCircle, CheckCircle } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { colors, typography } from '../design-system';
import { invoke } from '@tauri-apps/api/core';
import HeroMetrics from './HeroMetrics';

interface SystemStatus {
  isActive: boolean;
  runningTasks: number;
  cpu: number;
  memory: number;
  activeDownloads: number;
  activeUploads: number;
}

interface Stats {
  todayDownloads: number;
  todayUploads: number;
  totalViews: number;
  engagement: number;
}

const Dashboard: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    isActive: true,
    runningTasks: 0,
    cpu: 0,
    memory: 0,
    activeDownloads: 0,
    activeUploads: 0,
  });

  const [stats, setStats] = useState<Stats>({
    todayDownloads: 0,
    todayUploads: 0,
    totalViews: 0,
    engagement: 0,
  });

  useEffect(() => {
    // Load initial stats
    loadSystemStatus();
    loadStats();

    // Set up interval for real-time updates
    const interval = setInterval(() => {
      loadSystemStatus();
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const loadSystemStatus = async () => {
    try {
      // Get active downloads count
      const downloads = await invoke<any[]>('get_active_downloads');
      
      // Calculate running tasks (downloads + uploads + background tasks)
      const runningTasks = downloads.length; // Add more task types as needed
      
      setSystemStatus(prev => ({
        ...prev,
        runningTasks,
        activeDownloads: downloads.length,
        isActive: true,
      }));
    } catch (error) {
      console.error('Failed to load system status:', error);
    }
  };

  const loadStats = async () => {
    try {
      // Get today's stats
      const downloads = await invoke<any[]>('get_downloads');
      const today = new Date().toDateString();
      const todayDownloads = downloads.filter(d => 
        new Date(d.created_at).toDateString() === today
      ).length;

      setStats(prev => ({
        ...prev,
        todayDownloads,
      }));
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  // Export system status for Header component
  useEffect(() => {
    const fullStatus = {
      ...systemStatus,
      todayDownloads: stats.todayDownloads,
    };
    window.systemStatus = fullStatus;
    window.dispatchEvent(new CustomEvent('systemStatusUpdate', { detail: fullStatus }));
  }, [systemStatus, stats.todayDownloads]);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const StatCard: React.FC<{
    icon: React.ReactNode;
    label: string;
    value: string | number;
    trend?: number | null;
    color: string;
  }> = ({ icon, label, value, trend, color }) => (
    <GlassCard variant="interactive" className="group">
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm text-gray-400 mb-1">{label}</p>
          <p className="text-2xl font-bold text-white" style={{ fontFamily: typography.fonts.heading }}>
            {value}
          </p>
          {trend !== null && trend !== undefined && (
            <p className={`text-xs mt-1 flex items-center gap-1 ${trend >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              <TrendingUp className="w-3 h-3" />
              {trend >= 0 ? '+' : ''}{trend}%
            </p>
          )}
        </div>
        <div 
          className="p-3 rounded-lg"
          style={{ background: color }}
        >
          {icon}
        </div>
      </div>
    </GlassCard>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Hero Metrics - New Centerpiece Component */}
      <HeroMetrics />

      {/* System Status */}
      <GlassCard variant="elevated" className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className={`w-3 h-3 rounded-full ${systemStatus.isActive ? 'bg-green-500' : 'bg-red-500'} animate-pulse`} />
            <div>
              <h3 className="text-lg font-semibold text-white">System Status</h3>
              <p className="text-sm text-gray-400">
                {systemStatus.isActive ? 'All systems operational' : 'System issues detected'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-6">
            <div className="text-right">
              <p className="text-sm text-gray-400">Active Tasks</p>
              <p className="text-xl font-bold text-white">{systemStatus.runningTasks}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-400">Downloads</p>
              <p className="text-xl font-bold text-primary-400">{systemStatus.activeDownloads}</p>
            </div>
          </div>
        </div>
      </GlassCard>


      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <GlassCard variant="default">
          <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
            Recent Downloads
          </h3>
          <div className="space-y-3">
            {systemStatus.activeDownloads > 0 ? (
              <div className="text-center py-8 text-gray-400">
                <Activity className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">{systemStatus.activeDownloads} downloads in progress</p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Download className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No recent downloads</p>
              </div>
            )}
          </div>
        </GlassCard>

        <GlassCard variant="default">
          <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
            Platform Status
          </h3>
          <div className="space-y-3">
            <div className="text-center py-8 text-gray-500">
              <Zap className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Connect platforms to see performance</p>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Quick Actions */}
      <GlassCard variant="elevated">
        <h3 className="text-lg font-semibold text-white mb-4" style={{ fontFamily: typography.fonts.heading }}>
          Quick Actions
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <AnimatedButton variant="primary" size="md" fullWidth icon={<Download className="w-4 h-4" />}>
            New Download
          </AnimatedButton>
          <AnimatedButton variant="secondary" size="md" fullWidth icon={<Upload className="w-4 h-4" />}>
            Upload Video
          </AnimatedButton>
          <AnimatedButton variant="secondary" size="md" fullWidth icon={<Zap className="w-4 h-4" />}>
            Optimize Media
          </AnimatedButton>
          <AnimatedButton variant="secondary" size="md" fullWidth icon={<Activity className="w-4 h-4" />}>
            View Analytics
          </AnimatedButton>
        </div>
      </GlassCard>
    </div>
  );
};

// Add global type declaration
declare global {
  interface Window {
    systemStatus: SystemStatus;
  }
}

export default Dashboard;