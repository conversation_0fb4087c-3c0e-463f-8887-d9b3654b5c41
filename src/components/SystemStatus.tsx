import React from 'react';

const SystemStatus: React.FC = () => {
  // Sample system status data
  const systemStatus = {
    networkSpeed: 'Fast',
    storageSpace: {
      available: 890,
      unit: 'GB',
      status: 'Free'
    },
    activeDownloads: 2
  };
  
  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Status</h3>
      
      <div className="space-y-4">
        {/* Network Speed */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            <span className="text-sm text-gray-700 dark:text-gray-300">Network Speed</span>
          </div>
          <span className="text-sm font-medium text-green-600 dark:text-green-400">{systemStatus.networkSpeed}</span>
        </div>
        
        {/* Storage Space */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            <span className="text-sm text-gray-700 dark:text-gray-300">Storage Space</span>
          </div>
          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
            {systemStatus.storageSpace.available} {systemStatus.storageSpace.unit} {systemStatus.storageSpace.status}
          </span>
        </div>
        
        {/* Active Downloads */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="inline-block w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
            <span className="text-sm text-gray-700 dark:text-gray-300">Active Downloads</span>
          </div>
          <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
            {systemStatus.activeDownloads} Running
          </span>
        </div>
      </div>
    </div>
  );
};

export default SystemStatus; 