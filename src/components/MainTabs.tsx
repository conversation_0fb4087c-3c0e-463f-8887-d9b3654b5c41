import React from 'react';
import Dashboard from './Dashboard';
import MainContent from './MainContent';
import MediaLibrary from './MediaLibrary/MediaLibrary';
import { UploadManager } from './UploadManager';
import UploadHistory from './UploadHistory';
import Analytics from './Analytics';
import AIStudio from './AIStudio';
import { useNavigation } from '../contexts/NavigationContext';

const MainTabs: React.FC = () => {
  const { activeTab } = useNavigation();

  return (
    <div className="h-full">
        {activeTab === 'dashboard' ? (
          <div className="h-full overflow-y-auto">
            <Dashboard />
          </div>
        ) : activeTab === 'download' ? (
          <div className="h-full overflow-y-auto">
            <MainContent />
          </div>
        ) : activeTab === 'upload' ? (
          <div className="h-full overflow-y-auto">
            <UploadManager />
          </div>
        ) : activeTab === 'analytics' ? (
          <div className="h-full overflow-y-auto">
            <Analytics />
          </div>
        ) : activeTab === 'ai' ? (
          <div className="h-full overflow-y-auto">
            <AIStudio />
          </div>
        ) : (
          <div className="h-full overflow-y-auto">
            <MediaLibrary className="h-full" />
          </div>
        )}
    </div>
  );
};

export default MainTabs;