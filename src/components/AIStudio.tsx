import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ideo, <PERSON><PERSON><PERSON>, Brain, Wand2, <PERSON><PERSON>dingUp, AlertCircle, Settings } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { convertFileSrc } from '@tauri-apps/api/core';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { AIInsightsDashboard, SmartEditPanel } from './ai';
import { multiModalAnalyzer } from '../ai/MultiModalContentAnalyzer';
import { MediaInfo } from '../types/media';
import { ContentAnalysis } from '../ai/types';
import { useDownloadStore } from '../store/downloadStore';
import { AISettingsPanel } from './AISettings';

interface AIStudioState {
  selectedFile: MediaInfo | null;
  analysis: ContentAnalysis | null;
  isAnalyzing: boolean;
  activePanel: 'overview' | 'insights' | 'edit' | 'settings';
  error: string | null;
  showCostEstimate: boolean;
  costEstimate: { estimatedCost: number; breakdown: any[] } | null;
}

const AIStudio: React.FC = () => {
  const [state, setState] = useState<AIStudioState>({
    selectedFile: null,
    analysis: null,
    isAnalyzing: false,
    activePanel: 'overview',
    error: null,
    showCostEstimate: false,
    costEstimate: null
  });
  
  const [mediaFiles, setMediaFiles] = useState<MediaInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const { defaultDownloadPath } = useDownloadStore();

  useEffect(() => {
    loadMediaFiles();
  }, [defaultDownloadPath]);

  const loadMediaFiles = async () => {
    try {
      setLoading(true);
      const files = await invoke<MediaInfo[]>('list_downloaded_files', { 
        directory: defaultDownloadPath 
      });
      // Filter for video and audio files that can be analyzed
      const analyzableFiles = files.filter(file => 
        ['video', 'audio'].includes(file.media_type)
      );
      setMediaFiles(analyzableFiles);
    } catch (err) {
      setState(prev => ({ 
        ...prev, 
        error: err instanceof Error ? err.message : 'Failed to load media files' 
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = async (file: MediaInfo) => {
    setState(prev => ({
      ...prev,
      selectedFile: file,
      analysis: null,
      activePanel: 'overview',
      error: null
    }));
  };

  const handleAnalyze = async () => {
    if (!state.selectedFile) return;

    setState(prev => ({ ...prev, isAnalyzing: true, error: null }));

    try {
      const analysisOptions = {
        enableTranscription: true,
        enableSceneDetection: true,
        enableObjectDetection: true,
        enableSentimentAnalysis: true,
        enablePlatformOptimization: true,
        targetPlatforms: ['youtube', 'tiktok', 'instagram']
      };

      // Get cost estimate first
      const estimate = await multiModalAnalyzer.estimateCost(state.selectedFile.file_path, analysisOptions);
      
      // Show cost estimate if not free
      if (estimate.estimatedCost > 0) {
        setState(prev => ({
          ...prev,
          showCostEstimate: true,
          costEstimate: estimate
        }));
        
        // Wait for user confirmation (in production, show a dialog)
        console.log(`Estimated cost: $${estimate.estimatedCost.toFixed(4)}`);
      }

      // Perform analysis
      const analysis = await multiModalAnalyzer.analyzeContent(state.selectedFile.file_path, analysisOptions);

      setState(prev => ({
        ...prev,
        analysis,
        isAnalyzing: false,
        activePanel: 'insights',
        showCostEstimate: false
      }));
    } catch (error) {
      console.error('Analysis error:', error);
      setState(prev => ({
        ...prev,
        isAnalyzing: false,
        error: error instanceof Error ? error.message : 'Analysis failed'
      }));
    }
  };

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return 'N/A';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="h-full flex flex-col p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-100 flex items-center gap-3">
          <Brain className="w-8 h-8 text-purple-400" />
          AI Studio
        </h1>
        <p className="text-gray-400 mt-2">
          Analyze, optimize, and enhance your content with AI
        </p>
      </div>

      <div className="flex-1 flex gap-6 overflow-hidden">
        {/* File Selection Panel */}
        <div className="w-80 flex flex-col">
          <GlassCard variant="elevated" className="flex-1 overflow-hidden flex flex-col">
            <div className="p-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-gray-200 flex items-center gap-2">
                <FileVideo className="w-5 h-5" />
                Media Library
              </h2>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto"></div>
                  <p className="text-gray-400 mt-4">Loading media files...</p>
                </div>
              ) : mediaFiles.length === 0 ? (
                <div className="text-center py-8">
                  <FileVideo className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-400">No media files found</p>
                  <p className="text-gray-500 text-sm mt-2">
                    Download some videos or audio files to get started
                  </p>
                </div>
              ) : (
                <div className="space-y-2">
                  {mediaFiles.map((file) => (
                    <div
                      key={file.file_path}
                      onClick={() => handleFileSelect(file)}
                      className={`
                        p-3 rounded-lg cursor-pointer transition-all
                        ${state.selectedFile?.file_path === file.file_path
                          ? 'bg-purple-500/20 border border-purple-500/50'
                          : 'bg-gray-800/50 hover:bg-gray-800 border border-transparent'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`
                          p-2 rounded
                          ${state.selectedFile?.file_path === file.file_path
                            ? 'bg-purple-500/30 text-purple-400'
                            : 'bg-gray-700 text-gray-400'
                          }
                        `}>
                          <FileVideo className="w-4 h-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-200 truncate">
                            {file.file_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(file.file_size)} • {formatDuration(file.duration)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </GlassCard>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {state.selectedFile ? (
            <>
              {/* File Info & Actions */}
              <GlassCard variant="elevated" className="mb-4">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-200">
                        {state.selectedFile.file_name}
                      </h3>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                        <span>{formatFileSize(state.selectedFile.file_size)}</span>
                        <span>•</span>
                        <span>{formatDuration(state.selectedFile.duration)}</span>
                        <span>•</span>
                        <span className="capitalize">{state.selectedFile.media_type}</span>
                      </div>
                    </div>
                    
                    {!state.analysis ? (
                      <AnimatedButton
                        onClick={handleAnalyze}
                        disabled={state.isAnalyzing}
                        variant="primary"
                        icon={<Sparkles className="w-4 h-4" />}
                      >
                        {state.isAnalyzing ? 'Analyzing...' : 'Analyze with AI'}
                      </AnimatedButton>
                    ) : (
                      <div className="flex gap-2">
                        <button
                          onClick={() => setState(prev => ({ ...prev, activePanel: 'insights' }))}
                          className={`px-4 py-2 rounded-lg transition-all ${
                            state.activePanel === 'insights'
                              ? 'bg-purple-500/20 text-purple-400 border border-purple-500/50'
                              : 'bg-gray-800/50 text-gray-400 hover:bg-gray-800'
                          }`}
                        >
                          <TrendingUp className="w-4 h-4 inline mr-2" />
                          Insights
                        </button>
                        <button
                          onClick={() => setState(prev => ({ ...prev, activePanel: 'edit' }))}
                          className={`px-4 py-2 rounded-lg transition-all ${
                            state.activePanel === 'edit'
                              ? 'bg-purple-500/20 text-purple-400 border border-purple-500/50'
                              : 'bg-gray-800/50 text-gray-400 hover:bg-gray-800'
                          }`}
                        >
                          <Wand2 className="w-4 h-4 inline mr-2" />
                          Smart Edit
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </GlassCard>

              {/* Error Display */}
              {state.error && (
                <div className="bg-red-500/10 border border-red-500/50 rounded-lg p-4 mb-4">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-red-400">{state.error}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Content Panels */}
              <div className="flex-1 overflow-y-auto">
                {state.activePanel === 'overview' && !state.analysis && !state.isAnalyzing && (
                  <GlassCard variant="elevated" className="h-full">
                    <div className="p-8 text-center">
                      <Brain className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-gray-200 mb-2">
                        Ready to Analyze
                      </h3>
                      <p className="text-gray-400 max-w-md mx-auto">
                        Click "Analyze with AI" to unlock insights, predictions, and smart editing capabilities for this file.
                      </p>
                      <div className="mt-8 grid grid-cols-3 gap-4 max-w-2xl mx-auto">
                        <div className="p-4 bg-gray-800/50 rounded-lg">
                          <Sparkles className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                          <h4 className="text-sm font-medium text-gray-300">Content Analysis</h4>
                          <p className="text-xs text-gray-500 mt-1">
                            Scene detection, transcription, sentiment analysis
                          </p>
                        </div>
                        <div className="p-4 bg-gray-800/50 rounded-lg">
                          <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-2" />
                          <h4 className="text-sm font-medium text-gray-300">Performance Prediction</h4>
                          <p className="text-xs text-gray-500 mt-1">
                            Virality score, engagement metrics, trends
                          </p>
                        </div>
                        <div className="p-4 bg-gray-800/50 rounded-lg">
                          <Wand2 className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                          <h4 className="text-sm font-medium text-gray-300">Smart Editing</h4>
                          <p className="text-xs text-gray-500 mt-1">
                            Auto-cuts, silence removal, highlight generation
                          </p>
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                )}

                {state.isAnalyzing && (
                  <GlassCard variant="elevated" className="h-full">
                    <div className="p-8 text-center">
                      <div className="animate-pulse">
                        <Brain className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-200 mb-2">
                        Analyzing Content
                      </h3>
                      <p className="text-gray-400">
                        AI is processing your file. This may take a few moments...
                      </p>
                      <div className="mt-8 space-y-3 max-w-sm mx-auto">
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                          <span className="text-sm text-gray-400">Extracting content features</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                          <span className="text-sm text-gray-400">Analyzing scenes and audio</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                          <span className="text-sm text-gray-400">Generating insights</span>
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                )}

                {state.analysis && state.activePanel === 'insights' && (
                  <AIInsightsDashboard
                    filePath={state.selectedFile.file_path}
                    analysis={state.analysis}
                  />
                )}

                {state.analysis && state.activePanel === 'edit' && (
                  <SmartEditPanel
                    filePath={state.selectedFile.file_path}
                    analysis={state.analysis}
                    onExport={(exportPath) => {
                      console.log('Exported to:', exportPath);
                      // Could show a success notification here
                    }}
                  />
                )}
              </div>
            </>
          ) : (
            <GlassCard variant="elevated" className="flex-1">
              <div className="h-full flex items-center justify-center">
                <div className="text-center">
                  <Brain className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-300 mb-2">
                    Welcome to AI Studio
                  </h3>
                  <p className="text-gray-500">
                    Select a media file from the library to begin
                  </p>
                </div>
              </div>
            </GlassCard>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIStudio;