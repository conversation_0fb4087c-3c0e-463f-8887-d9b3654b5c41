import React, { useState, useEffect } from 'react';
import { X, Save, RotateCcw, Download, Upload, AlertCircle } from 'lucide-react';
import { 
  useDynamicConfig
} from '../hooks';
import { DynamicConfig } from '../config/schemas';
import { dynamicConfigManager } from '../config/dynamicConfigManager';
import { toast } from 'react-toastify';

interface ConfigurationManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

type ConfigSection = 
  | 'quality' 
  | 'formats' 
  | 'ui' 
  | 'performance' 
  | 'network' 
  | 'storage' 
  | 'features'
  | 'security'
  | 'developer';

const ConfigurationManager: React.FC<ConfigurationManagerProps> = ({ isOpen, onClose }) => {
  const { config, updateConfig, resetConfig, isLoading, error } = useDynamicConfig();
  
  const [activeSection, setActiveSection] = useState<ConfigSection>('quality');
  const [localConfig, setLocalConfig] = useState<DynamicConfig>(config);
  const [hasChanges, setHasChanges] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  useEffect(() => {
    setLocalConfig(config);
    setHasChanges(false);
  }, [config]);

  const handleSave = async () => {
    setSaveError(null);
    try {
      await updateConfig(localConfig);
      toast.success('Configuration saved successfully');
      setHasChanges(false);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to save configuration';
      setSaveError(errorMsg);
      toast.error(errorMsg);
    }
  };

  const handleReset = async () => {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      try {
        await resetConfig();
        toast.success('Configuration reset to defaults');
        setHasChanges(false);
      } catch (err) {
        toast.error('Failed to reset configuration');
      }
    }
  };

  const handleExport = () => {
    const configJson = dynamicConfigManager.exportConfig();
    const blob = new Blob([configJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'flowdownload-config.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Configuration exported successfully');
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        await dynamicConfigManager.importConfig(content);
        toast.success('Configuration imported successfully');
        setHasChanges(false);
      } catch (err) {
        toast.error('Failed to import configuration: Invalid format');
      }
    };
    reader.readAsText(file);
  };

  const updateLocalConfig = (updates: Partial<DynamicConfig>) => {
    setLocalConfig({ ...localConfig, ...updates });
    setHasChanges(true);
  };

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'quality':
        return <QualitySection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'formats':
        return <FormatsSection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'ui':
        return <UISection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'performance':
        return <PerformanceSection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'network':
        return <NetworkSection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'storage':
        return <StorageSection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'features':
        return <FeaturesSection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'security':
        return <SecuritySection config={localConfig} onUpdate={updateLocalConfig} />;
      case 'developer':
        return <DeveloperSection config={localConfig} onUpdate={updateLocalConfig} />;
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute inset-x-4 top-4 bottom-4 md:inset-x-auto md:left-1/2 md:-translate-x-1/2 md:w-[90vw] md:max-w-6xl glass rounded-2xl shadow-2xl flex flex-col animate-fade-in">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Configuration Manager</h2>
          <button
            onClick={onClose}
            className="p-2 glass-light hover:glass-strong rounded-lg transition-all hover:scale-105"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Error Display */}
        {(error || saveError) && (
          <div className="mx-6 mt-4 p-4 glass-light border border-red-400/20 rounded-lg flex items-center animate-slide-up">
            <AlertCircle className="w-5 h-5 text-red-400 mr-2 flex-shrink-0" />
            <span className="text-sm text-red-400">{error || saveError}</span>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 border-r border-white/10 p-4 overflow-y-auto">
            <nav className="space-y-1">
              {[
                { id: 'quality', label: 'Quality Options', icon: '🎬' },
                { id: 'formats', label: 'Format Options', icon: '📁' },
                { id: 'ui', label: 'User Interface', icon: '🎨' },
                { id: 'performance', label: 'Performance', icon: '⚡' },
                { id: 'network', label: 'Network', icon: '🌐' },
                { id: 'storage', label: 'Storage', icon: '💾' },
                { id: 'features', label: 'Features', icon: '✨' },
                { id: 'security', label: 'Security', icon: '🔒' },
                { id: 'developer', label: 'Developer', icon: '👨‍💻' },
              ].map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id as ConfigSection)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all ${
                    activeSection === section.id
                      ? 'glass-strong text-blue-400 glow'
                      : 'text-gray-300 hover:glass-light hover:scale-105'
                  }`}
                >
                  <span className="mr-3 text-lg">{section.icon}</span>
                  {section.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-y-auto p-6">
            {renderSectionContent()}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-white/10">
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExport}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-300 glass-light border border-white/10 rounded-lg hover:glass-strong hover:scale-105 transition-all"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <label className="flex items-center px-4 py-2 text-sm font-medium text-gray-300 glass-light border border-white/10 rounded-lg hover:glass-strong hover:scale-105 transition-all cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              Import
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>
            <button
              onClick={handleReset}
              className="flex items-center px-4 py-2 text-sm font-medium text-red-400 glass-light border border-red-400/30 rounded-lg hover:glass-strong hover:border-red-400/50 hover:scale-105 transition-all"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset All
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <span className="text-sm text-amber-600 dark:text-amber-400 mr-2">
                Unsaved changes
              </span>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-300 glass-light border border-white/10 rounded-lg hover:glass-strong hover:scale-105 transition-all"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges || isLoading}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed glow transition-all hover:scale-105"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Quality Options Section
const QualitySection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Quality Options
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Configure available quality options for downloads
        </p>
      </div>

      <div className="space-y-4">
        {config.qualityOptions.map((quality, index) => (
          <div
            key={quality.id}
            className="p-4 glass-medium rounded-lg border border-glass-border hover:border-primary-500/30 transition-all"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <div className={`mt-1 w-10 h-10 rounded-full glass-light border border-${quality.color}-500/30 flex items-center justify-center`}>
                  <span className={`text-${quality.color}-600 dark:text-${quality.color}-400`}>
                    {quality.icon}
                  </span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {quality.label}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {quality.description}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-500">
                    <span>Value: {quality.value}</span>
                    <span>Size: {quality.estimatedSize}</span>
                    {quality.resolution && <span>Resolution: {quality.resolution}</span>}
                    {quality.bitrate && <span>Bitrate: {quality.bitrate}</span>}
                  </div>
                </div>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={quality.enabled}
                  onChange={(e) => {
                    const updated = [...config.qualityOptions];
                    updated[index] = { ...quality, enabled: e.target.checked };
                    onUpdate({ qualityOptions: updated });
                  }}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Formats Section
const FormatsSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Format Options
        </h3>
        <p className="text-sm text-gray-400">
          Configure available file formats for downloads
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {config.formatOptions.map((format, index) => (
          <div
            key={format.id}
            className="p-4 glass-medium rounded-lg border border-glass-border hover:border-primary-500/30 transition-all"
          >
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-white">
                  {format.label}
                </h4>
                <p className="text-sm text-gray-300 mt-1">
                  {format.description}
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-xs px-2 py-1 glass-strong text-primary-300 rounded border border-primary-400/30">
                    {format.category}
                  </span>
                  <span className="text-xs text-gray-400">
                    {format.fileExtension}
                  </span>
                </div>
              </div>
              
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={format.enabled}
                  onChange={(e) => {
                    const updated = [...config.formatOptions];
                    updated[index] = { ...format, enabled: e.target.checked };
                    onUpdate({ formatOptions: updated });
                  }}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// UI Section
const UISection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          User Interface Settings
        </h3>
        <p className="text-sm text-gray-400">
          Customize the appearance and behavior of the application
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Theme
          </label>
          <select
            value={config.ui.theme.id}
            onChange={(e) => {
              onUpdate({
                ui: {
                  ...config.ui,
                  theme: { ...config.ui.theme, id: e.target.value }
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          >
            <option value="default">Default</option>
            <option value="dark">Dark</option>
            <option value="light">Light</option>
          </select>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Animations
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Enable smooth transitions and animations
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.ui.animations}
              onChange={(e) => {
                onUpdate({
                  ui: { ...config.ui, animations: e.target.checked }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Compact Mode
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Reduce spacing for more content density
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.ui.compactMode}
              onChange={(e) => {
                onUpdate({
                  ui: { ...config.ui, compactMode: e.target.checked }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>
    </div>
  );
};

// Performance Section
const PerformanceSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Performance Settings
        </h3>
        <p className="text-sm text-gray-400">
          Optimize download performance and resource usage
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Max Concurrent Downloads
          </label>
          <input
            type="number"
            min="1"
            max="10"
            value={config.performance.maxConcurrentDownloads}
            onChange={(e) => {
              onUpdate({
                performance: {
                  ...config.performance,
                  maxConcurrentDownloads: parseInt(e.target.value) || 3
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Chunk Size (MB)
          </label>
          <input
            type="number"
            min="0.5"
            max="10"
            step="0.5"
            value={config.performance.chunkSize / (1024 * 1024)}
            onChange={(e) => {
              onUpdate({
                performance: {
                  ...config.performance,
                  chunkSize: parseFloat(e.target.value) * 1024 * 1024
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Progress Update Interval (ms)
          </label>
          <input
            type="number"
            min="100"
            max="2000"
            step="100"
            value={config.performance.progressUpdateInterval}
            onChange={(e) => {
              onUpdate({
                performance: {
                  ...config.performance,
                  progressUpdateInterval: parseInt(e.target.value) || 500
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>
      </div>
    </div>
  );
};

// Network Section
const NetworkSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Network Settings
        </h3>
        <p className="text-sm text-gray-400">
          Configure network behavior and limits
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Connection Timeout (seconds)
          </label>
          <input
            type="number"
            min="5"
            max="120"
            value={config.network.timeout / 1000}
            onChange={(e) => {
              onUpdate({
                network: {
                  ...config.network,
                  timeout: parseInt(e.target.value) * 1000
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Bandwidth Limit (MB/s, 0 = unlimited)
          </label>
          <input
            type="number"
            min="0"
            max="1000"
            value={config.network.bandwidthLimit}
            onChange={(e) => {
              onUpdate({
                network: {
                  ...config.network,
                  bandwidthLimit: parseInt(e.target.value) || 0
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            User Agent
          </label>
          <input
            type="text"
            value={config.network.userAgent}
            onChange={(e) => {
              onUpdate({
                network: {
                  ...config.network,
                  userAgent: e.target.value
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>
      </div>
    </div>
  );
};

// Storage Section
const StorageSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Storage Settings
        </h3>
        <p className="text-sm text-gray-400">
          Configure file storage and organization
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Default Download Path
          </label>
          <input
            type="text"
            value={config.storage.defaultDownloadPath}
            onChange={(e) => {
              onUpdate({
                storage: {
                  ...config.storage,
                  defaultDownloadPath: e.target.value
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
            placeholder="/Users/<USER>/Downloads"
          />
        </div>

        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-300">
            Organization Rules
          </h4>
          
          <div className="flex items-center justify-between">
            <label className="text-sm text-gray-400">
              Organize by file type
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.storage.organizationRules.byType}
                onChange={(e) => {
                  onUpdate({
                    storage: {
                      ...config.storage,
                      organizationRules: {
                        ...config.storage.organizationRules,
                        byType: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm text-gray-400">
              Organize by date
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.storage.organizationRules.byDate}
                onChange={(e) => {
                  onUpdate({
                    storage: {
                      ...config.storage,
                      organizationRules: {
                        ...config.storage.organizationRules,
                        byDate: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm text-gray-400">
              Organize by website
            </label>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.storage.organizationRules.bySite}
                onChange={(e) => {
                  onUpdate({
                    storage: {
                      ...config.storage,
                      organizationRules: {
                        ...config.storage.organizationRules,
                        bySite: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

// Features Section
const FeaturesSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Feature Toggles
        </h3>
        <p className="text-sm text-gray-400">
          Enable or disable application features
        </p>
      </div>

      <div className="space-y-4">
        <div className="p-4 glass-light rounded-lg border border-white/10 hover:glass-medium transition-all">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                Media Editor
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Built-in editor for downloaded media files
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.features.mediaEditor.enabled}
                onChange={(e) => {
                  onUpdate({
                    features: {
                      ...config.features,
                      mediaEditor: {
                        ...config.features.mediaEditor,
                        enabled: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        <div className="p-4 glass-light rounded-lg border border-white/10 hover:glass-medium transition-all">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                Templates
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Download templates and presets
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.features.templates.enabled}
                onChange={(e) => {
                  onUpdate({
                    features: {
                      ...config.features,
                      templates: {
                        ...config.features.templates,
                        enabled: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        <div className="p-4 glass-light rounded-lg border border-white/10 hover:glass-medium transition-all">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white">
                Cloud Sync
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Sync settings and downloads across devices
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={config.features.cloudSync.enabled}
                onChange={(e) => {
                  onUpdate({
                    features: {
                      ...config.features,
                      cloudSync: {
                        ...config.features.cloudSync,
                        enabled: e.target.checked
                      }
                    }
                  });
                }}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

// Security Section
const SecuritySection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Security Settings
        </h3>
        <p className="text-sm text-gray-400">
          Configure security and privacy options
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Max File Size (GB)
          </label>
          <input
            type="number"
            min="1"
            max="100"
            value={config.security.maxFileSize / (1024 * 1024 * 1024)}
            onChange={(e) => {
              onUpdate({
                security: {
                  ...config.security,
                  maxFileSize: parseInt(e.target.value) * 1024 * 1024 * 1024
                }
              });
            }}
            className="block w-full px-3 py-2 border border-white/10 rounded-lg glass-light text-gray-100 focus:glass-strong focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Require HTTPS
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Only allow secure HTTPS connections
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.security.requireHttps}
              onChange={(e) => {
                onUpdate({
                  security: {
                    ...config.security,
                    requireHttps: e.target.checked
                  }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Sanitize Filenames
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Remove special characters from filenames
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.security.sanitizeFilenames}
              onChange={(e) => {
                onUpdate({
                  security: {
                    ...config.security,
                    sanitizeFilenames: e.target.checked
                  }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>
    </div>
  );
};

// Developer Section
const DeveloperSection: React.FC<{
  config: DynamicConfig;
  onUpdate: (updates: Partial<DynamicConfig>) => void;
}> = ({ config, onUpdate }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-white mb-2">
          Developer Settings
        </h3>
        <p className="text-sm text-gray-400">
          Advanced options for development and debugging
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Debug Mode
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Enable detailed logging and debugging features
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.developer.debugMode}
              onChange={(e) => {
                onUpdate({
                  developer: {
                    ...config.developer,
                    debugMode: e.target.checked
                  }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Verbose Logging
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Log all operations and network requests
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.developer.verboseLogging}
              onChange={(e) => {
                onUpdate({
                  developer: {
                    ...config.developer,
                    verboseLogging: e.target.checked
                  }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Show Dev Tools
            </label>
            <p className="text-xs text-gray-400 mt-1">
              Enable browser developer tools
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.developer.showDevTools}
              onChange={(e) => {
                onUpdate({
                  developer: {
                    ...config.developer,
                    showDevTools: e.target.checked
                  }
                });
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {config.developer.debugMode && (
          <div className="mt-4 p-4 glass-light border border-yellow-400/20 rounded-lg animate-slide-up">
            <h4 className="text-sm font-medium text-yellow-200 mb-2">
              Debug Information
            </h4>
            <pre className="text-xs text-yellow-300 overflow-x-auto">
              {JSON.stringify({
                version: '1.0.0',
                platform: navigator.platform,
                userAgent: navigator.userAgent,
                configPath: 'dynamic-config.json',
                activeFeatures: Object.entries(config.features)
                  .filter(([_, f]) => f.enabled)
                  .map(([k]) => k)
              }, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfigurationManager;