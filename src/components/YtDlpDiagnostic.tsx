import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Loader, Terminal } from 'lucide-react';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { colors, typography } from '../design-system';
import { toast } from 'react-toastify';
import { invoke } from '@tauri-apps/api/core';

interface YtDlpStatus {
  available: boolean;
  version?: string;
  path?: string;
  error?: string;
}

const YtDlpDiagnostic: React.FC = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [ytdlpStatus, setYtdlpStatus] = useState<YtDlpStatus | null>(null);

  // Auto-check on component mount
  useEffect(() => {
    checkYtDlpStatus();
  }, []);

  const checkYtDlpStatus = async () => {
    setIsChecking(true);
    try {
      // Check yt-dlp availability
      const available = await invoke<boolean>('check_ytdlp_availability');
      
      if (available) {
        try {
          // Get version and path
          const [version, path] = await Promise.all([
            invoke<string>('get_ytdlp_version').catch(() => 'Unknown'),
            invoke<string>('get_ytdlp_executable_path').catch(() => 'Unknown')
          ]);
          
          setYtdlpStatus({ 
            available: true, 
            version,
            path 
          });
          
          toast.success('yt-dlp is properly installed and ready to use');
        } catch (error) {
          setYtdlpStatus({ 
            available: true, 
            error: 'Could not get version info' 
          });
        }
      } else {
        setYtdlpStatus({ 
          available: false,
          error: 'yt-dlp is not installed on this system'
        });
        toast.warning('yt-dlp is not installed. Please install it to enable downloads.');
      }
    } catch (error) {
      console.error('Failed to check yt-dlp status:', error);
      setYtdlpStatus({ 
        available: false, 
        error: error instanceof Error ? error.message : 'Failed to check status' 
      });
      toast.error('Failed to check yt-dlp status');
    } finally {
      setIsChecking(false);
    }
  };

  const installYtDlp = async () => {
    try {
      toast.info('Opening installation guide...');
      
      // Detect platform and open appropriate guide
      const platform = await invoke<string>('get_platform');
      let url = '';
      
      switch(platform) {
        case 'darwin':
          url = 'https://formulae.brew.sh/formula/yt-dlp';
          break;
        case 'win32':
          url = 'https://github.com/yt-dlp/yt-dlp#installation';
          break;
        default:
          url = 'https://github.com/yt-dlp/yt-dlp#installation';
      }
      
      const { open } = await import('@tauri-apps/plugin-shell');
      await open(url);
    } catch (error) {
      toast.error('Failed to open installation guide');
    }
  };

  const updateYtDlp = async () => {
    setIsChecking(true);
    try {
      toast.info('Updating yt-dlp...');
      await invoke('update_ytdlp');
      toast.success('yt-dlp updated successfully');
      
      // Re-check status
      await checkYtDlpStatus();
    } catch (error) {
      toast.error('Failed to update yt-dlp');
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <GlassCard variant="elevated">
      <div className="p-6">
        <h2 
          className="text-xl font-bold text-white mb-4"
          style={{ fontFamily: typography.fonts.heading }}
        >
          yt-dlp Diagnostic
        </h2>

        {isChecking ? (
          <div className="flex items-center justify-center py-8">
            <Loader className="w-8 h-8 text-primary-400 animate-spin" />
            <span className="ml-3 text-gray-400">Checking yt-dlp status...</span>
          </div>
        ) : ytdlpStatus ? (
          <div className="space-y-4">
            {/* Status Display */}
            <div 
              className={`p-4 rounded-lg border ${
                ytdlpStatus.available 
                  ? 'glass-light border-green-500/50' 
                  : 'glass-light border-red-500/50'
              }`}
            >
              <div className="flex items-start gap-3">
                {ytdlpStatus.available ? (
                  <CheckCircle className="w-6 h-6 text-green-400 flex-shrink-0 mt-1" />
                ) : (
                  <AlertCircle className="w-6 h-6 text-red-400 flex-shrink-0 mt-1" />
                )}
                
                <div className="flex-1">
                  <h3 className={`font-semibold ${ytdlpStatus.available ? 'text-green-400' : 'text-red-400'}`}>
                    {ytdlpStatus.available ? 'yt-dlp Available' : 'yt-dlp Not Found'}
                  </h3>
                  
                  {ytdlpStatus.available && ytdlpStatus.version && (
                    <p className="text-sm text-gray-400 mt-1">
                      Version: <span className="text-white">{ytdlpStatus.version}</span>
                    </p>
                  )}
                  
                  {ytdlpStatus.path && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">Installation path:</p>
                      <code className="text-xs text-gray-300 bg-glass-light px-2 py-1 rounded block mt-1 overflow-x-auto">
                        {ytdlpStatus.path}
                      </code>
                    </div>
                  )}
                  
                  {ytdlpStatus.error && (
                    <p className="text-sm text-red-400 mt-2">{ytdlpStatus.error}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <AnimatedButton
                onClick={checkYtDlpStatus}
                variant="primary"
                size="md"
                icon={<Terminal className="w-4 h-4" />}
                disabled={isChecking}
              >
                Check yt-dlp Status
              </AnimatedButton>
              
              {ytdlpStatus.available ? (
                <AnimatedButton
                  onClick={updateYtDlp}
                  variant="secondary"
                  size="md"
                  disabled={isChecking}
                >
                  Update yt-dlp
                </AnimatedButton>
              ) : (
                <AnimatedButton
                  onClick={installYtDlp}
                  variant="accent"
                  size="md"
                  glow
                >
                  Install yt-dlp
                </AnimatedButton>
              )}
            </div>

            {/* Installation Instructions */}
            {!ytdlpStatus.available && (
              <div className="mt-6 p-4 glass-light rounded-lg border border-glass-border">
                <h4 className="font-medium text-white mb-3">Quick Installation</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-400">macOS:</span>
                    <code className="ml-2 text-primary-400">brew install yt-dlp</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Windows:</span>
                    <code className="ml-2 text-primary-400">winget install yt-dlp</code>
                  </div>
                  <div>
                    <span className="text-gray-400">Linux:</span>
                    <code className="ml-2 text-primary-400">pip install yt-dlp</code>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <AnimatedButton
              onClick={checkYtDlpStatus}
              variant="primary"
              size="md"
              icon={<Terminal className="w-4 h-4" />}
            >
              Check yt-dlp Status
            </AnimatedButton>
          </div>
        )}
      </div>
    </GlassCard>
  );
};

export default YtDlpDiagnostic;