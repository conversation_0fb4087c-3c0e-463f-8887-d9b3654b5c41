import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';
import { useDownloadStore } from '../store/downloadStore';
import { selectDownloadFolder, validateDownloadPath } from '../utils/folderUtils';
import DeviceOptimization from './DeviceOptimization';
import YtDlpDiagnostic from './YtDlpDiagnostic';
import { useQualityOptions, useFormatOptions, useUILabels } from '../hooks';
import GlassCard from './ui/GlassCard';
import AnimatedButton from './ui/AnimatedButton';
import { colors, typography } from '../design-system';

const MainContent: React.FC = () => {
  const { addDownload, defaultDownloadPath } = useDownloadStore();
  const { activeOptions: qualityOptions } = useQualityOptions();
  const { activeOptions: formatOptions, getFormatsForQuality } = useFormatOptions();
  const { labels } = useUILabels();
  
  const [url, setUrl] = useState('');
  const [selectedQuality, setSelectedQuality] = useState(qualityOptions[0]?.value || 'best');
  const [selectedFormat, setSelectedFormat] = useState(formatOptions[0]?.value || 'mp4');
  const [customFilename, setCustomFilename] = useState('');
  const [downloadPath, setDownloadPath] = useState(defaultDownloadPath);
  const [isLoading, setIsLoading] = useState(false);
  const [showQualityDropdown, setShowQualityDropdown] = useState(false);
  const [showFormatDropdown, setShowFormatDropdown] = useState(false);
  const qualityDropdownRef = useRef<HTMLDivElement>(null);
  const formatDropdownRef = useRef<HTMLDivElement>(null);
  
  // Find the selected quality option
  const selectedQualityOption = qualityOptions.find(q => q.value === selectedQuality) || qualityOptions[0];
  const selectedFormatOption = formatOptions.find(f => f.value === selectedFormat) || formatOptions[0];
  
  // Get available formats for selected quality
  const availableFormats = selectedQualityOption ? getFormatsForQuality(selectedQualityOption.id) : formatOptions;
  
  // Update quality and format based on default values when options change
  useEffect(() => {
    if (qualityOptions.length > 0 && !qualityOptions.find(q => q.value === selectedQuality)) {
      setSelectedQuality(qualityOptions[0].value);
    }
  }, [qualityOptions, selectedQuality]);

  useEffect(() => {
    if (availableFormats.length > 0 && !availableFormats.find(f => f.value === selectedFormat)) {
      setSelectedFormat(availableFormats[0].value);
    }
  }, [availableFormats, selectedFormat]);
  
  // Handle device optimization selection
  const handleDeviceSelect = (device: 'desktop' | 'mobile' | 'audio', preset: string) => {
    // Update quality and format based on device type
    switch(device) {
      case 'desktop':
        setSelectedQuality(preset || '1080p');
        setSelectedFormat('mp4');
        toast.info(`Desktop optimization selected: ${preset || '1080p'} MP4`);
        break;
      case 'mobile':
        setSelectedQuality(preset || '720p');
        setSelectedFormat('mp4');
        toast.info(`Mobile optimization selected: ${preset || '720p'} MP4`);
        break;
      case 'audio':
        setSelectedQuality(preset || 'audio-high');
        setSelectedFormat('mp3');
        toast.info(`Audio optimization selected: ${preset || 'High Quality MP3'}`);
        break;
    }
  };
  
  // Listen for device optimization events
  useEffect(() => {
    const handleDeviceOptimize = (event: CustomEvent) => {
      const { device, preset } = event.detail;
      handleDeviceSelect(device, preset);
    };
    
    window.addEventListener('device-optimize' as any, handleDeviceOptimize);
    
    return () => {
      window.removeEventListener('device-optimize' as any, handleDeviceOptimize);
    };
  }, []);
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (qualityDropdownRef.current && !qualityDropdownRef.current.contains(event.target as Node)) {
        setShowQualityDropdown(false);
      }
      if (formatDropdownRef.current && !formatDropdownRef.current.contains(event.target as Node)) {
        setShowFormatDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleBrowse = async () => {
    const selectedPath = await selectDownloadFolder();
    if (selectedPath) {
      setDownloadPath(selectedPath);
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      if (!url) {
        toast.error(labels.invalidUrl);
        setIsLoading(false);
        return;
      }
      
      // Check if it's a valid URL or a search term
      const isValidUrl = url.startsWith('http://') || url.startsWith('https://') || url.includes('://');
      const looksLikeFile = url.endsWith('.mp4') || url.endsWith('.mp3') || url.endsWith('.webm') || 
                           url.endsWith('.mkv') || url.endsWith('.avi') || url.endsWith('.mov');
      
      if (!isValidUrl && !looksLikeFile) {
        // If it's not a URL and doesn't look like a file, treat it as a search term
        console.log('Treating as search term:', url);
      }
      
      // Validate download path
      if (!downloadPath) {
        toast.error(labels.invalidUrl);
        setIsLoading(false);
        return;
      }
      
      const isPathValid = await validateDownloadPath(downloadPath);
      if (!isPathValid) {
        toast.error(labels.permissionDenied);
        setIsLoading(false);
        return;
      }
      
      // Generate filename if not provided
      let filename = customFilename;
      if (!filename) {
        // Check if it's a social media URL or search term
        const isSocialMedia = url.includes('youtube.com') || url.includes('youtu.be') || 
                            url.includes('twitter.com') || url.includes('x.com') ||
                            url.includes('facebook.com') || url.includes('instagram.com') ||
                            (!url.startsWith('http://') && !url.startsWith('https://'));
        
        if (isSocialMedia) {
          try {
            // Import Tauri API properly
            const { invoke } = await import('@tauri-apps/api/core');
            
            // Try to get video info to extract title
            setIsLoading(true);
            toast.info('Fetching video information...');
            const videoInfo = await invoke('get_video_info', { url }) as any;
            
            if (videoInfo && typeof videoInfo === 'object' && 'title' in videoInfo) {
              // Sanitize the title for use as filename
              const title = String(videoInfo.title)
                .replace(/[<>:"/\\|?*]/g, '') // Remove invalid filename characters
                .replace(/\s+/g, ' ') // Normalize spaces
                .trim();
              
              if (title) {
                filename = title;
                toast.success(`Found video: ${title}`);
              }
            }
          } catch (error) {
            console.log('Could not fetch video info, using default filename:', error);
            // If it's a search term rather than URL, use the search term as filename
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
              filename = url.replace(/[<>:"/\\|?*]/g, '').trim();
            }
          }
        }
        
        // Fallback to default filename generation
        if (!filename) {
          try {
            const urlObj = new URL(url);
            const pathSegments = urlObj.pathname.split('/').filter(Boolean);
            if (pathSegments.length > 0) {
              filename = pathSegments[pathSegments.length - 1];
            } else {
              filename = `download_${Date.now()}.${selectedFormat}`;
            }
          } catch (e) {
            filename = `download_${Date.now()}.${selectedFormat}`;
          }
        }
      }
      
      // Ensure filename has correct extension
      if (!filename.endsWith(`.${selectedFormat}`) && !selectedFormat.includes('audio')) {
        filename = `${filename}.${selectedFormat}`;
      } else if (selectedFormat.includes('audio')) {
        // Handle audio formats
        const audioExt = selectedFormat === 'audio-high' || selectedFormat === 'audio-medium' || selectedFormat === 'audio-low' 
          ? 'mp3' 
          : selectedFormat.replace('audio-', '');
        
        if (!filename.endsWith(`.${audioExt}`)) {
          filename = `${filename}.${audioExt}`;
        }
      }
      
      // Add download with quality and format information
      // Pass the value (e.g., "1080p") instead of label for backend processing
      addDownload(url, filename, selectedQuality, downloadPath);
      toast.success(labels.downloadStarted);
      
      // Reset form
      setUrl('');
      setCustomFilename('');
      
    } catch (error) {
      console.error('Error adding download:', error);
      if (error instanceof Error) {
        // Sanitize error message to prevent XSS
        const safeErrorMessage = error.message.replace(/[<>&"']/g, '');
        toast.error(`Error: ${safeErrorMessage}`);
      } else {
        toast.error('An unknown error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Helper function to get color classes based on quality option
  const getColorClasses = (color: string) => {
    // Using CSS variables from theme.css for dynamic color theming
    return {
      bg: `quality-${color}-bg`,
      text: `quality-${color}-text`
    };
  };
  
  const colors = selectedQualityOption ? getColorClasses(selectedQualityOption.color) : getColorClasses('blue');
  
  return (
    <div className="space-y-8 p-6">
      {/* Add New Download Form */}
      <GlassCard variant="elevated" className="animate-fade-in">
        <div className="mb-6 pt-2">
          <h2 
            className="text-xl font-bold text-white mb-2"
            style={{ fontFamily: typography.fonts.heading }}
          >
            {labels.addNewDownload}
          </h2>
          <div className="flex items-center text-sm text-gray-400">
            <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Configure your download settings and start downloading</span>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Source URL */}
          <div className="mb-5">
            <label htmlFor="source-url" className="block text-sm font-medium text-gray-300 mb-2">
              {labels.sourceUrl}
            </label>
            <div className="relative rounded-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                <svg className="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <input
                type="text"
                id="source-url"
                className="block w-full pl-10 pr-12 py-3 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder={labels.sourceUrlPlaceholder}
                value={url}
                onChange={(e) => setUrl(e.target.value)}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-5">
            {/* Quality Selection */}
            <div>
              <label htmlFor="quality" className="block text-sm font-medium text-gray-300 mb-2">
                {labels.qualitySelection}
              </label>
              <div className="relative" ref={qualityDropdownRef}>
                <div 
                  className="flex items-center glass-light border border-glass-border rounded-lg p-3 cursor-pointer hover:bg-glass-medium transition-colors"
                  onClick={() => setShowQualityDropdown(!showQualityDropdown)}
                >
                  {selectedQualityOption && (
                    <>
                      <div className="flex-shrink-0 mr-3">
                        <span className={`inline-flex items-center justify-center h-8 w-8 rounded-full ${colors.bg} ${colors.text}`}>
                          {selectedQualityOption.icon}
                        </span>
                      </div>
                      <div className="flex-grow">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-white">{selectedQualityOption.label}</span>
                          <svg className="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    </>
                  )}
                </div>
                
                {/* Quality Dropdown */}
                {showQualityDropdown && (
                  <div className="absolute z-10 mt-1 w-full glass-strong border border-glass-border rounded-lg shadow-glass-lg max-h-60 overflow-y-auto">
                    {qualityOptions.map((quality) => {
                      const qColors = getColorClasses(quality.color);
                      return (
                        <div 
                          key={quality.value}
                          className="p-3 hover:bg-glass-strong cursor-pointer flex items-center transition-colors"
                          onClick={() => {
                            setSelectedQuality(quality.value);
                            setShowQualityDropdown(false);
                          }}
                        >
                          <span className={`inline-flex items-center justify-center h-6 w-6 rounded-full ${qColors.bg} ${qColors.text} mr-2 text-xs`}>
                            {quality.icon}
                          </span>
                          <div className="flex-grow">
                            <div className="text-sm font-medium text-white">{quality.label}</div>
                            <div className="text-xs text-gray-400 flex justify-between">
                              <span>{quality.description}</span>
                              <span className="text-xs font-medium">{quality.estimatedSize}</span>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                
                {selectedQualityOption && (
                  <div className="mt-2 text-xs text-blue-400">
                    <div className="flex items-center">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span>{selectedQualityOption.description}</span>
                    </div>
                    <div className="ml-4 text-gray-500">Est. size: {selectedQualityOption.estimatedSize}</div>
                  </div>
                )}
              </div>
            </div>
            
            {/* Format Selection */}
            <div>
              <label htmlFor="format" className="block text-sm font-medium text-gray-300 mb-2">
                {labels.formatSelection}
              </label>
              <div className="relative" ref={formatDropdownRef}>
                <div 
                  className="flex items-center glass-light border border-glass-border rounded-lg p-3 cursor-pointer hover:bg-glass-medium transition-colors"
                  onClick={() => setShowFormatDropdown(!showFormatDropdown)}
                >
                  {selectedFormatOption && (
                    <>
                      <div className="flex-shrink-0 mr-3">
                        <span className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                          {selectedFormat.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-grow">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-white">{selectedFormatOption.label}</span>
                          <svg className="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    </>
                  )}
                </div>
                
                {/* Format Dropdown */}
                {showFormatDropdown && (
                  <div className="absolute z-10 mt-1 w-full glass-strong border border-glass-border rounded-lg shadow-glass-lg overflow-hidden">
                    {availableFormats.map((format) => (
                      <div 
                        key={format.value}
                        className="p-3 hover:bg-glass-strong cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedFormat(format.value);
                          setShowFormatDropdown(false);
                        }}
                      >
                        <div className="flex items-center">
                          <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs mr-2">
                            {format.value.toUpperCase()}
                          </span>
                          <div>
                            <div className="text-sm font-medium text-white">{format.label}</div>
                            <div className="text-xs text-gray-400">{format.description}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {selectedFormatOption && (
                  <div className="mt-2 text-xs text-gray-500">
                    {selectedFormatOption.description}
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Custom Filename */}
            <div>
              <label htmlFor="custom-filename" className="block text-sm font-medium text-gray-300 mb-2">
                {labels.customFilename}
              </label>
              <input
                type="text"
                id="custom-filename"
                className="block w-full px-4 py-3 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder={labels.customFilenamePlaceholder}
                value={customFilename}
                onChange={(e) => setCustomFilename(e.target.value)}
              />
              <div className="mt-2 text-xs text-gray-500">
                Leave empty to use original filename
              </div>
            </div>
            
            {/* Download Location */}
            <div>
              <label htmlFor="download-location" className="block text-sm font-medium text-gray-300 mb-2">
                {labels.downloadLocation}
              </label>
              <div className="flex">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 002-2h14a2 2 0 002 2v2" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    id="download-location"
                    className="block w-full pl-10 pr-12 py-3 glass-light border border-glass-border rounded-l-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Select download location"
                    value={downloadPath}
                    onChange={(e) => setDownloadPath(e.target.value)}
                  />
                </div>
                <button
                  type="button"
                  onClick={handleBrowse}
                  className="px-4 py-3 glass-medium border border-glass-border border-l-0 text-gray-300 font-medium rounded-r-lg hover:bg-glass-strong focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  {labels.browse}
                </button>
              </div>
            </div>
          
          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <AnimatedButton
              onClick={() => handleSubmit({ preventDefault: () => {} } as React.FormEvent)}
              disabled={isLoading}
              variant="primary"
              size="lg"
              fullWidth={false}
              loading={isLoading}
              icon={(
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              )}
            >
              {labels.startDownload}
            </AnimatedButton>
            
            <AnimatedButton
              variant="ghost"
              size="lg"
              className="hidden md:flex"
              icon={(
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              )}
            >
              {labels.advanced}
            </AnimatedButton>
          </div>
        </form>
      </GlassCard>
      
      {/* Device Optimization */}
      <DeviceOptimization onSelectDevice={handleDeviceSelect} />
      
      {/* yt-dlp Diagnostic */}
      <YtDlpDiagnostic />
    </div>
  );
};

export default MainContent; 