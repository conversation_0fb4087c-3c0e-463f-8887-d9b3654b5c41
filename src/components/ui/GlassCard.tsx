import React from 'react';
import { colors, utils } from '../../design-system';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'interactive';
  blur?: 'sm' | 'md' | 'lg';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onClick?: () => void;
  hover?: boolean;
  glow?: boolean;
  gradient?: boolean;
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className = '',
  variant = 'default',
  blur = 'md',
  padding = 'lg',
  onClick,
  hover = false,
  glow = false,
  gradient = false,
}) => {
  const blurValues = {
    sm: 8,
    md: 12,
    lg: 20,
  };

  const paddingValues = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  };

  const variantStyles = {
    default: {
      background: colors.glass.light,
      border: `1px solid ${colors.glass.border}`,
    },
    elevated: {
      background: colors.glass.medium,
      border: `1px solid ${colors.glass.border}`,
      boxShadow: colors.glass.shadow,
    },
    interactive: {
      background: colors.glass.medium,
      border: `1px solid ${colors.glass.border}`,
      cursor: 'pointer',
    },
  };

  const baseClasses = `
    rounded-xl
    backdrop-blur-${blur}
    transition-all
    duration-300
    ${paddingValues[padding]}
    ${hover ? 'hover:scale-[1.02] hover:shadow-xl' : ''}
    ${glow ? 'animate-glowPulse' : ''}
    ${onClick ? 'cursor-pointer' : ''}
    ${className}
  `;

  const inlineStyles = {
    ...variantStyles[variant],
    backdropFilter: `blur(${blurValues[blur]}px)`,
    WebkitBackdropFilter: `blur(${blurValues[blur]}px)`,
    ...(gradient && {
      background: `
        linear-gradient(135deg, 
          rgba(139, 92, 246, 0.1) 0%, 
          rgba(59, 130, 246, 0.05) 100%
        ),
        ${variantStyles[variant].background}
      `,
    }),
  };

  const content = (
    <div
      className={baseClasses}
      style={inlineStyles}
      onClick={onClick}
    >
      {children}
    </div>
  );

  return content;
};

export default GlassCard;