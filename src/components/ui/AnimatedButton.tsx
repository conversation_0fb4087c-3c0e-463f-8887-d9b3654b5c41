import React from 'react';
import { colors, typography } from '../../design-system';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'glass';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  className?: string;
  pulse?: boolean;
  glow?: boolean;
}

const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  className = '',
  pulse = false,
  glow = false,
}) => {
  const sizeStyles = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const variantStyles = {
    primary: {
      base: 'text-white shadow-lg',
      gradient: colors.primary.gradient,
      hover: 'hover:shadow-xl hover:scale-105',
      active: 'active:scale-95',
    },
    secondary: {
      base: 'bg-gray-800 text-white border border-gray-700',
      gradient: '',
      hover: 'hover:bg-gray-700 hover:border-gray-600',
      active: 'active:bg-gray-900',
    },
    accent: {
      base: 'text-white shadow-lg',
      gradient: colors.accent.gradient,
      hover: 'hover:shadow-xl hover:scale-105',
      active: 'active:scale-95',
    },
    ghost: {
      base: 'text-gray-300 bg-transparent border border-gray-700',
      gradient: '',
      hover: 'hover:bg-gray-800 hover:text-white',
      active: 'active:bg-gray-900',
    },
    glass: {
      base: 'backdrop-blur-md bg-white/10 text-white border border-white/20',
      gradient: '',
      hover: 'hover:bg-white/20 hover:border-white/30',
      active: 'active:bg-white/5',
    },
  };

  const currentVariant = variantStyles[variant];
  const hasGradient = variant === 'primary' || variant === 'accent';

  const baseClasses = `
    relative
    inline-flex
    items-center
    justify-center
    font-semibold
    rounded-lg
    transition-all
    duration-200
    transform
    ${sizeStyles[size]}
    ${currentVariant.base}
    ${!disabled && currentVariant.hover}
    ${!disabled && currentVariant.active}
    ${fullWidth ? 'w-full' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${pulse && !disabled ? 'animate-pulse' : ''}
    ${className}
  `;

  const style: React.CSSProperties = {
    ...(hasGradient && {
      backgroundImage: currentVariant.gradient,
    }),
    ...(glow && !disabled && {
      boxShadow: `0 0 30px ${variant === 'primary' ? 'rgba(139, 92, 246, 0.5)' : 'rgba(255, 107, 107, 0.5)'}`,
    }),
    fontFamily: typography.fonts.sans,
  };

  const LoadingSpinner = () => (
    <svg 
      className="animate-spin h-4 w-4" 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  return (
    <button
      className={baseClasses}
      style={style}
      onClick={onClick}
      disabled={disabled || loading}
    >
      {/* Hover effect overlay */}
      {hasGradient && (
        <div 
          className="absolute inset-0 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-200"
          style={{ 
            backgroundImage: colors.primary.gradientHover,
            zIndex: -1,
          }}
        />
      )}

      {/* Button content */}
      <div className="flex items-center gap-2">
        {loading ? (
          <LoadingSpinner />
        ) : (
          <>
            {icon && iconPosition === 'left' && <span className="flex-shrink-0">{icon}</span>}
            <span>{children}</span>
            {icon && iconPosition === 'right' && <span className="flex-shrink-0">{icon}</span>}
          </>
        )}
      </div>

      {/* Ripple effect on click */}
      <span className="absolute inset-0 rounded-lg overflow-hidden">
        <span className="absolute inset-0 rounded-lg bg-white opacity-0 hover:opacity-10 transition-opacity" />
      </span>
    </button>
  );
};

export default AnimatedButton;