import React from 'react';
import { colors, typography } from '../../design-system';
import GlassCard from './GlassCard';

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  color?: 'primary' | 'accent' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  onClick?: () => void;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  change,
  icon,
  color = 'primary',
  size = 'md',
  loading = false,
  onClick,
}) => {
  const colorMap = {
    primary: colors.primary.gradient,
    accent: colors.accent.gradient,
    success: colors.success.gradient,
    warning: `linear-gradient(135deg, ${colors.status.warning} 0%, #FCD34D 100%)`,
    error: `linear-gradient(135deg, ${colors.status.error} 0%, #FCA5A5 100%)`,
  };

  const sizeStyles = {
    sm: {
      title: 'text-xs',
      value: 'text-2xl',
      subtitle: 'text-xs',
      icon: 'w-8 h-8',
      padding: 'p-4',
    },
    md: {
      title: 'text-sm',
      value: 'text-3xl',
      subtitle: 'text-sm',
      icon: 'w-10 h-10',
      padding: 'p-6',
    },
    lg: {
      title: 'text-base',
      value: 'text-4xl',
      subtitle: 'text-base',
      icon: 'w-12 h-12',
      padding: 'p-8',
    },
  };

  const currentSize = sizeStyles[size];

  return (
    <GlassCard
      variant="elevated"
      hover={!!onClick}
      onClick={onClick}
      className={`relative overflow-hidden ${currentSize.padding}`}
    >
      {/* Background gradient decoration */}
      <div
        className="absolute inset-0 opacity-10"
        style={{
          background: colorMap[color],
          filter: 'blur(40px)',
        }}
      />

      <div className="relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 
              className={`${currentSize.title} font-medium text-gray-400 mb-1`}
              style={{ fontFamily: typography.fonts.sans }}
            >
              {title}
            </h3>
            
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="h-8 w-24 bg-gray-700 rounded animate-pulse" />
              </div>
            ) : (
              <div className="flex items-baseline gap-3">
                <span 
                  className={`${currentSize.value} font-bold text-white`}
                  style={{ fontFamily: typography.fonts.heading }}
                >
                  {value}
                </span>
                
                {change && (
                  <span 
                    className={`flex items-center text-sm font-medium ${
                      change.type === 'increase' ? 'text-green-400' : 'text-red-400'
                    }`}
                  >
                    {change.type === 'increase' ? (
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                    )}
                    {Math.abs(change.value)}%
                  </span>
                )}
              </div>
            )}
            
            {subtitle && (
              <p 
                className={`${currentSize.subtitle} text-gray-500 mt-1`}
                style={{ fontFamily: typography.fonts.sans }}
              >
                {subtitle}
              </p>
            )}
          </div>

          {icon && (
            <div 
              className={`${currentSize.icon} rounded-lg flex items-center justify-center`}
              style={{
                background: colorMap[color],
              }}
            >
              <div className="text-white opacity-90">
                {icon}
              </div>
            </div>
          )}
        </div>

        {/* Progress indicator at bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-800">
          <div 
            className="h-full transition-all duration-500"
            style={{
              width: change ? `${Math.min(100, Math.abs(change.value))}%` : '0%',
              background: colorMap[color],
            }}
          />
        </div>
      </div>
    </GlassCard>
  );
};

export default MetricCard;