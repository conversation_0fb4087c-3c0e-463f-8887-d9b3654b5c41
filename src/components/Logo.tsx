import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  showText?: boolean;
  variant?: 'default' | 'icon' | 'horizontal';
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  animated = true,
  showText = true,
  variant = 'default'
}) => {
  const sizes = {
    sm: { icon: 24, text: 14 },
    md: { icon: 32, text: 18 },
    lg: { icon: 48, text: 24 },
    xl: { icon: 64, text: 32 }
  };

  const currentSize = sizes[size];

  // Animation variants
  const logoVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.6, -0.05, 0.01, 0.99] as const
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
        ease: [0.42, 0, 0.58, 1] as const
      }
    }
  };

  const pathVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        pathLength: { duration: 1.5, ease: [0.42, 0, 0.58, 1] as const },
        opacity: { duration: 0.3 }
      }
    }
  };

  const glowVariants = {
    animate: {
      opacity: [0.5, 1, 0.5],
      scale: [1, 1.2, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: [0.42, 0, 0.58, 1] as const
      }
    }
  };

  const LogoIcon = () => (
    <motion.svg
      width={currentSize.icon}
      height={currentSize.icon}
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      whileHover="hover"
      variants={logoVariants}
    >
      {/* Background glow effect */}
      <motion.circle
        cx="32"
        cy="32"
        r="30"
        fill="url(#glow)"
        opacity="0.3"
        variants={glowVariants}
        animate={animated ? "animate" : undefined}
      />
      
      {/* Outer ring */}
      <motion.circle
        cx="32"
        cy="32"
        r="28"
        stroke="url(#gradient1)"
        strokeWidth="2"
        fill="none"
        variants={pathVariants}
        initial={animated ? "hidden" : "visible"}
        animate="visible"
      />
      
      {/* Inner hexagon representing connectivity */}
      <motion.path
        d="M32 8 L48 18 L48 38 L32 48 L16 38 L16 18 Z"
        stroke="url(#gradient2)"
        strokeWidth="2"
        fill="none"
        variants={pathVariants}
        initial={animated ? "hidden" : "visible"}
        animate="visible"
        style={{ strokeLinecap: "round", strokeLinejoin: "round" }}
      />
      
      {/* Center icon - play button merged with upload arrow */}
      <motion.g
        initial={animated ? { opacity: 0, scale: 0 } : { opacity: 1, scale: 1 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.5, duration: 0.3 }}
      >
        {/* Play triangle */}
        <path
          d="M26 22 L26 42 L40 32 Z"
          fill="url(#gradient3)"
          opacity="0.8"
        />
        {/* Upload arrow overlay */}
        <path
          d="M32 28 L32 36 M32 28 L28 32 M32 28 L36 32"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </motion.g>
      
      {/* Orbiting dots representing content flow */}
      {[0, 120, 240].map((rotation, index) => (
        <motion.circle
          key={index}
          cx="32"
          cy="12"
          r="2"
          fill={index === 0 ? "#60A5FA" : index === 1 ? "#A78BFA" : "#F472B6"}
          initial={{ rotate: rotation }}
          animate={animated ? {
            rotate: rotation + 360,
            transition: {
              duration: 10,
              repeat: Infinity,
              ease: [0, 0, 1, 1] as const,
              delay: index * 0.3
            }
          } : undefined}
          style={{ originX: "32px", originY: "32px" }}
        />
      ))}
      
      {/* Gradient definitions */}
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#60A5FA" />
          <stop offset="50%" stopColor="#A78BFA" />
          <stop offset="100%" stopColor="#F472B6" />
        </linearGradient>
        
        <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" />
          <stop offset="100%" stopColor="#8B5CF6" />
        </linearGradient>
        
        <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#60A5FA" />
          <stop offset="100%" stopColor="#A78BFA" />
        </linearGradient>
        
        <radialGradient id="glow">
          <stop offset="0%" stopColor="#60A5FA" stopOpacity="0.8" />
          <stop offset="100%" stopColor="#A78BFA" stopOpacity="0" />
        </radialGradient>
      </defs>
    </motion.svg>
  );

  const LogoText = () => (
    <motion.div
      initial={animated ? { opacity: 0, x: -10 } : { opacity: 1 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.3, duration: 0.5 }}
      style={{ fontSize: currentSize.text }}
      className="font-bold text-white select-none"
    >
      <span className="bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent">
        Creator
      </span>
      <span className="text-white">OS</span>
    </motion.div>
  );

  if (variant === 'icon') {
    return <LogoIcon />;
  }

  if (variant === 'horizontal') {
    return (
      <motion.div
        className="flex items-center gap-3"
        initial="hidden"
        animate="visible"
        whileHover="hover"
        variants={logoVariants}
      >
        <LogoIcon />
        {showText && <LogoText />}
      </motion.div>
    );
  }

  // Default variant - stacked
  return (
    <motion.div
      className="flex flex-col items-center gap-2"
      initial="hidden"
      animate="visible"
      whileHover="hover"
      variants={logoVariants}
    >
      <LogoIcon />
      {showText && <LogoText />}
    </motion.div>
  );
};

export default Logo;