import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useDownloadStore } from '../store/downloadStore';
import { selectDownloadFolder, validateDownloadPath } from '../utils/folderUtils';

interface NewDownloadFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewDownloadForm: React.FC<NewDownloadFormProps> = ({ isOpen, onClose }) => {
  const { addDownload, defaultDownloadPath, isInitialized, initializeDefaultPath } = useDownloadStore();
  const [url, setUrl] = useState('');
  const [filename, setFilename] = useState('');
  const [quality, setQuality] = useState('1080p');
  const [downloadPath, setDownloadPath] = useState('');
  const [isYouTubeUrl, setIsYouTubeUrl] = useState(false);
  const [isSocialMediaUrl, setIsSocialMediaUrl] = useState(false);
  const [socialPlatform, setSocialPlatform] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      initializeDefaultPath();
    }
  }, [isInitialized, initializeDefaultPath]);

  useEffect(() => {
    if (defaultDownloadPath && !downloadPath) {
      setDownloadPath(defaultDownloadPath);
    }
  }, [defaultDownloadPath, downloadPath]);

  // Check if URL is a social media URL
  useEffect(() => {
    // Check for YouTube
    const isYouTube = url.includes('youtube.com') || url.includes('youtu.be');
    setIsYouTubeUrl(isYouTube);
    
    // Check for other social media platforms
    let platform = '';
    let isSocial = false;
    
    if (isYouTube) {
      platform = 'YouTube';
      isSocial = true;
    } else if (url.includes('twitter.com') || url.includes('x.com')) {
      platform = 'Twitter/X';
      isSocial = true;
    } else if (url.includes('facebook.com') || url.includes('fb.com') || url.includes('fb.watch')) {
      platform = 'Facebook';
      isSocial = true;
    } else if (url.includes('instagram.com')) {
      platform = 'Instagram';
      isSocial = true;
    } else if (url.includes('tiktok.com')) {
      platform = 'TikTok';
      isSocial = true;
    } else if (url.includes('reddit.com')) {
      platform = 'Reddit';
      isSocial = true;
    } else if (url.includes('vimeo.com')) {
      platform = 'Vimeo';
      isSocial = true;
    }
    
    setIsSocialMediaUrl(isSocial);
    setSocialPlatform(platform);
    
    // Auto-extract filename from YouTube URL if possible
    if (isYouTube && !filename) {
      try {
        // Try to extract video ID and set it as default filename
        let videoId = '';
        if (url.includes('youtube.com/watch?v=')) {
          videoId = new URL(url).searchParams.get('v') || '';
        } else if (url.includes('youtu.be/')) {
          videoId = url.split('youtu.be/')[1]?.split('?')[0] || '';
        }
        
        if (videoId) {
          setFilename(`YouTube-${videoId}.mp4`);
        }
      } catch (e) {
        // Invalid URL, ignore
      }
    }
  }, [url, filename]);

  const handleSelectFolder = async () => {
    const selectedPath = await selectDownloadFolder();
    if (selectedPath) {
      setDownloadPath(selectedPath);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!url) {
        toast.error('Please enter a URL');
        return;
      }

      // Auto-generate filename if not provided
      let finalFilename = filename;
      if (!finalFilename) {
        try {
          const urlObj = new URL(url);
          const pathSegments = urlObj.pathname.split('/').filter(Boolean);
          if (pathSegments.length > 0) {
            finalFilename = pathSegments[pathSegments.length - 1];
          } else {
            finalFilename = `download_${Date.now()}.bin`;
          }
        } catch (e) {
          finalFilename = `download_${Date.now()}.bin`;
        }
      }

      // Validate download path
      if (!downloadPath) {
        toast.error('Please select a download path');
        return;
      }

      const isPathValid = await validateDownloadPath(downloadPath);
      if (!isPathValid) {
        toast.error('Download path is invalid or not accessible');
        return;
      }

      // Show special YouTube notice if needed
      if (isYouTubeUrl) {
        toast.warn('Note: YouTube downloads require yt-dlp which may not be installed.', {
          autoClose: 8000
        });
      }

      // Show warning for social media URLs
      if (isSocialMediaUrl) {
        toast.warn(`Note: ${socialPlatform} downloads require additional tools to be installed.`, {
          autoClose: 8000
        });
      }

      addDownload(url, finalFilename, quality, downloadPath);
      // Sanitize filename for display to prevent XSS
      const safeFilename = finalFilename.replace(/[<>&"']/g, '');
      toast.success(`Added download: ${safeFilename}`);
      
      // Reset form
      setUrl('');
      setFilename('');
      setQuality('1080p');
      onClose();
    } catch (error) {
      console.error('Error adding download:', error);
      if (error instanceof Error) {
        // Sanitize error message to prevent XSS
        const safeErrorMessage = error.message.replace(/[<>&"']/g, '');
        toast.error(`Error: ${safeErrorMessage}`);
      } else {
        toast.error('An unknown error occurred');
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Add New Download</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="url" className="block text-sm font-medium text-gray-700 mb-1">
              URL
            </label>
            <input
              type="text"
              id="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter URL to download"
              required
            />
            {isYouTubeUrl && (
              <div className="mt-1 text-yellow-600 text-sm flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                YouTube URL detected. This requires yt-dlp to be installed.
              </div>
            )}
            {isSocialMediaUrl && (
              <div className="mt-1 text-yellow-600 text-sm flex items-center">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                {socialPlatform} URL detected. This requires additional tools to be installed.
              </div>
            )}
          </div>

          <div className="mb-4">
            <label htmlFor="filename" className="block text-sm font-medium text-gray-700 mb-1">
              Filename (optional)
            </label>
            <input
              type="text"
              id="filename"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Leave empty to auto-detect"
            />
          </div>

          <div className="mb-4">
            <label htmlFor="quality" className="block text-sm font-medium text-gray-700 mb-1">
              Quality
            </label>
            <select
              id="quality"
              value={quality}
              onChange={(e) => setQuality(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="auto">Auto</option>
              <option value="144p">144p</option>
              <option value="240p">240p</option>
              <option value="360p">360p</option>
              <option value="480p">480p</option>
              <option value="720p">720p</option>
              <option value="1080p">1080p</option>
              <option value="1440p">1440p</option>
              <option value="2160p">2160p</option>
            </select>
          </div>

          <div className="mb-6">
            <label htmlFor="downloadPath" className="block text-sm font-medium text-gray-700 mb-1">
              Download Location
            </label>
            <div className="flex">
              <input
                type="text"
                id="downloadPath"
                value={downloadPath}
                onChange={(e) => setDownloadPath(e.target.value)}
                className="flex-1 p-2 border border-gray-300 rounded-l-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Select download folder"
                required
              />
              <button
                type="button"
                onClick={handleSelectFolder}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-r-md hover:bg-gray-300 focus:outline-none"
              >
                Browse
              </button>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none ${isLoading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </div>
              ) : (
                'Add Download'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewDownloadForm; 