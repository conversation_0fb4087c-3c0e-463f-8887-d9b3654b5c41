import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'react-toastify';
import { 
  RefreshCw, Trash2, Play, Pause, CheckCircle, 
  AlertCircle, Clock, Upload, ExternalLink 
} from 'lucide-react';

interface UploadRecord {
  id: string;
  file_name: string;
  file_size: number;
  platform: string;
  status: string;
  progress: number;
  title: string;
  description?: string;
  tags: string[];
  privacy: string;
  platform_url?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  retry_count: number;
  bytes_uploaded: number;
}

export default function UploadHistory() {
  const [uploads, setUploads] = useState<UploadRecord[]>([]);
  const [resumableUploads, setResumableUploads] = useState<UploadRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'resumable'>('all');

  useEffect(() => {
    loadUploads();
  }, []);

  const loadUploads = async () => {
    try {
      setIsLoading(true);
      const [allUploads, resumable] = await Promise.all([
        invoke<UploadRecord[]>('get_upload_history', { limit: 100 }),
        invoke<UploadRecord[]>('get_resumable_uploads')
      ]);
      
      setUploads(allUploads);
      setResumableUploads(resumable);
    } catch (error) {
      console.error('Error loading uploads:', error);
      toast.error('Failed to load upload history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRetry = async (uploadId: string) => {
    try {
      await invoke('retry_upload', { uploadId });
      toast.success('Upload queued for retry');
      loadUploads();
    } catch (error) {
      toast.error(`Failed to retry upload: ${error}`);
    }
  };

  const handleResume = async (uploadId: string) => {
    try {
      await invoke('resume_upload', { uploadId });
      toast.success('Upload resumed');
      loadUploads();
    } catch (error) {
      toast.error(`Failed to resume upload: ${error}`);
    }
  };

  const handlePause = async (uploadId: string) => {
    try {
      await invoke('pause_upload', { uploadId });
      toast.success('Upload paused');
      loadUploads();
    } catch (error) {
      toast.error(`Failed to pause upload: ${error}`);
    }
  };

  const handleDelete = async (uploadId: string) => {
    if (!confirm('Are you sure you want to delete this upload record?')) {
      return;
    }

    try {
      await invoke('delete_upload_record', { uploadId });
      toast.success('Upload record deleted');
      loadUploads();
    } catch (error) {
      toast.error(`Failed to delete upload: ${error}`);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'failed':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'uploading':
      case 'processing':
        return <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />;
      case 'paused':
        return <Pause className="w-5 h-5 text-yellow-600" />;
      case 'queued':
      case 'preparing':
        return <Clock className="w-5 h-5 text-gray-600" />;
      default:
        return <Upload className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20';
      case 'failed':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20';
      case 'uploading':
      case 'processing':
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20';
      case 'paused':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const displayUploads = activeTab === 'all' ? uploads : resumableUploads;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Upload History</h2>
        <button
          onClick={loadUploads}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Tabs */}
      <div className="flex gap-4 mb-6 border-b dark:border-gray-700">
        <button
          onClick={() => setActiveTab('all')}
          className={`pb-2 px-1 font-medium transition-colors ${
            activeTab === 'all'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-gray-800 dark:text-gray-400'
          }`}
        >
          All Uploads ({uploads.length})
        </button>
        <button
          onClick={() => setActiveTab('resumable')}
          className={`pb-2 px-1 font-medium transition-colors ${
            activeTab === 'resumable'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-gray-800 dark:text-gray-400'
          }`}
        >
          Resumable ({resumableUploads.length})
        </button>
      </div>

      {/* Upload List */}
      {isLoading ? (
        <div className="text-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Loading uploads...</p>
        </div>
      ) : displayUploads.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <Upload className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No uploads found</p>
        </div>
      ) : (
        <div className="space-y-4">
          {displayUploads.map((upload) => (
            <div
              key={upload.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow p-4"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-start gap-3">
                  {getStatusIcon(upload.status)}
                  <div>
                    <h3 className="font-semibold">{upload.title}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {upload.file_name} • {formatFileSize(upload.file_size)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(upload.status)}`}>
                    {upload.status.toUpperCase()}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    upload.platform === 'YouTube' ? 'bg-red-100 text-red-800' : 'bg-black text-white'
                  }`}>
                    {upload.platform}
                  </span>
                </div>
              </div>

              {/* Progress Bar */}
              {(upload.status === 'uploading' || upload.status === 'processing') && (
                <div className="mb-3">
                  <div className="flex justify-between text-sm mb-1">
                    <span>Progress</span>
                    <span>{upload.progress.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all"
                      style={{ width: `${upload.progress}%` }}
                    />
                  </div>
                  {upload.bytes_uploaded > 0 && (
                    <p className="text-xs text-gray-500 mt-1">
                      {formatFileSize(upload.bytes_uploaded)} / {formatFileSize(upload.file_size)}
                    </p>
                  )}
                </div>
              )}

              {/* Error Message */}
              {upload.error_message && (
                <div className="mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded text-sm text-red-600">
                  {upload.error_message}
                </div>
              )}

              {/* Metadata */}
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                <p>Created: {formatDate(upload.created_at)}</p>
                {upload.completed_at && <p>Completed: {formatDate(upload.completed_at)}</p>}
                {upload.retry_count > 0 && <p>Retries: {upload.retry_count}</p>}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                {upload.platform_url && (
                  <a
                    href={upload.platform_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                  >
                    <ExternalLink className="w-3 h-3" />
                    View
                  </a>
                )}
                
                {upload.status === 'failed' && (
                  <button
                    onClick={() => handleRetry(upload.id)}
                    className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    <RefreshCw className="w-3 h-3" />
                    Retry
                  </button>
                )}
                
                {upload.status === 'paused' && upload.bytes_uploaded > 0 && (
                  <button
                    onClick={() => handleResume(upload.id)}
                    className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    <Play className="w-3 h-3" />
                    Resume
                  </button>
                )}
                
                {upload.status === 'uploading' && (
                  <button
                    onClick={() => handlePause(upload.id)}
                    className="flex items-center gap-1 px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                  >
                    <Pause className="w-3 h-3" />
                    Pause
                  </button>
                )}
                
                <button
                  onClick={() => handleDelete(upload.id)}
                  className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  <Trash2 className="w-3 h-3" />
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}