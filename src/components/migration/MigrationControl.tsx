import React from 'react';
import { useEnhancedDownloadStore } from '../../store/enhancedDownloadStore';
import { getFeatureFlags, setFeatureFlag } from '../../adapters/FeatureFlags';

export const MigrationControl: React.FC = () => {
  const {
    downloads,
    migrationStatus,
    migrationError,
    useDomainLayer,
    startMigration,
    rollbackMigration,
    clearMigrationError,
    refreshFromDomain
  } = useEnhancedDownloadStore();

  const featureFlags = getFeatureFlags();

  const handleEnableDomainLayer = () => {
    setFeatureFlag('useDomainLayer', true);
    window.location.reload();
  };

  const handleDisableDomainLayer = () => {
    setFeatureFlag('useDomainLayer', false);
    rollbackMigration();
    window.location.reload();
  };

  const handleStartMigration = async () => {
    await startMigration();
  };

  const getMigrationStatusColor = () => {
    switch (migrationStatus) {
      case 'completed': return 'green';
      case 'failed': return 'red';
      case 'in_progress': return 'yellow';
      default: return 'gray';
    }
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg bg-white dark:bg-gray-800 dark:border-gray-700 shadow-sm">
      <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-4">Domain Layer Architecture</h4>
      
      {/* Current Status */}
      <div className="mb-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Domain Layer:</span>
            <span className={`ml-2 px-2 py-1 rounded ${useDomainLayer ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}`}>
              {useDomainLayer ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Migration Status:</span>
            <span className={`ml-2 px-2 py-1 rounded bg-${getMigrationStatusColor()}-100 text-${getMigrationStatusColor()}-800 dark:bg-${getMigrationStatusColor()}-900 dark:text-${getMigrationStatusColor()}-200`}>
              {migrationStatus.replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {migrationError && (
        <div className="mb-4 p-3 bg-red-100 border border-red-200 rounded dark:bg-red-900 dark:border-red-700">
          <div className="flex justify-between items-start">
            <div>
              <h4 className="font-medium text-red-800 dark:text-red-200">Migration Error</h4>
              <p className="text-red-600 dark:text-red-300 text-sm mt-1">{migrationError}</p>
            </div>
            <button
              onClick={clearMigrationError}
              className="text-red-600 dark:text-red-300 hover:text-red-800 dark:hover:text-red-100 text-sm underline"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Migration Actions */}
      <div className="space-y-3">
        {!useDomainLayer && (
          <div>
            <button
              onClick={handleEnableDomainLayer}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
            >
              Enable Domain Layer
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Enable the new domain-driven architecture. This will reload the page.
            </p>
          </div>
        )}

        {useDomainLayer && migrationStatus === 'not_started' && (
          <div>
            <button
              onClick={handleStartMigration}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
              disabled={downloads.length === 0}
            >
              Start Migration ({downloads.length} downloads)
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Migrate existing downloads to the new domain layer.
            </p>
          </div>
        )}

        {useDomainLayer && migrationStatus === 'completed' && (
          <div>
            <button
              onClick={refreshFromDomain}
              className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 dark:bg-blue-400 dark:hover:bg-blue-500 mr-2"
            >
              Refresh from Domain
            </button>
            <button
              onClick={handleDisableDomainLayer}
              className="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 dark:bg-gray-400 dark:hover:bg-gray-500"
            >
              Disable Domain Layer
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Migration completed successfully. You can now use domain-driven features.
            </p>
          </div>
        )}

        {migrationStatus === 'failed' && (
          <div>
            <button
              onClick={handleStartMigration}
              className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 mr-2"
            >
              Retry Migration
            </button>
            <button
              onClick={rollbackMigration}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600"
            >
              Rollback Migration
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Migration failed. You can retry or rollback to the previous state.
            </p>
          </div>
        )}
      </div>

      {/* Development Info */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded dark:bg-blue-900/20 dark:border-blue-700">
          <h5 className="font-medium text-blue-800 dark:text-blue-200 text-sm">Debug Information</h5>
          <div className="text-xs text-blue-600 dark:text-blue-300 mt-1 space-y-1">
            <div>Downloads: {downloads.length}</div>
            <div>Domain Layer: {useDomainLayer ? 'Active' : 'Inactive'}</div>
            <div>Feature Flags: {Object.values(featureFlags).filter(Boolean).length} enabled</div>
          </div>
        </div>
      )}
    </div>
  );
};
