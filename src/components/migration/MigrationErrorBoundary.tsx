import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class MigrationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Migration Error Boundary caught an error:', error, errorInfo);
    
    // Call the optional error handler
    this.props.onError?.(error, errorInfo);
    
    // Could send to error reporting service
    this.reportError(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // For now, just log to console
    // In production, this would send to error reporting service
    console.log('Migration Error Report:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    });
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900 dark:border-red-700">
          <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
            Migration Error
          </h3>
          <p className="text-red-600 dark:text-red-300 mt-2">
            There was an issue with the new domain layer. 
            The application has fallen back to the legacy implementation.
          </p>
          <details className="mt-3">
            <summary className="cursor-pointer text-red-700 dark:text-red-300 font-medium">
              Error Details
            </summary>
            <pre className="mt-2 text-sm bg-red-100 dark:bg-red-800 p-2 rounded overflow-auto">
              {this.state.error?.message}
            </pre>
          </details>
          <button 
            onClick={() => window.location.reload()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600"
          >
            Reload Application
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
