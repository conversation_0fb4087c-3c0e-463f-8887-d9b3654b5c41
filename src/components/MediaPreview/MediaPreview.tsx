import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import VideoPlayer from './VideoPlayer';
import ImageViewer from './ImageViewer';
import AudioPlayer from './AudioPlayer';
import { MediaInfo } from '../../types/media';

interface MediaPreviewProps {
  filePath: string;
  onClose: () => void;
}

const MediaPreview: React.FC<MediaPreviewProps> = ({ filePath, onClose }) => {
  const [mediaInfo, setMediaInfo] = useState<MediaInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMediaInfo();
  }, [filePath]);

  const loadMediaInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const info = await invoke<MediaInfo>('get_media_info', { filePath });
      setMediaInfo(info);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load media info');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const formatDuration = (seconds: number): string => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hrs > 0) {
      return `${hrs}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-slate-800 rounded-lg p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading media...</p>
        </div>
      </div>
    );
  }

  if (error || !mediaInfo) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-slate-800 rounded-lg p-8 max-w-md">
          <h3 className="text-lg font-semibold text-red-500 mb-2">Error</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error || 'Failed to load media'}</p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 flex flex-col z-50">
      {/* Header */}
      <div className="bg-gray-900 text-white p-4 flex items-center justify-between">
        <div className="flex-1">
          <h2 className="text-lg font-semibold truncate">{mediaInfo.file_name}</h2>
          <div className="flex items-center space-x-4 text-sm text-gray-400 mt-1">
            <span>{formatFileSize(mediaInfo.file_size)}</span>
            {mediaInfo.duration && <span>{formatDuration(mediaInfo.duration)}</span>}
            {mediaInfo.width && mediaInfo.height && (
              <span>{mediaInfo.width}x{mediaInfo.height}</span>
            )}
            {mediaInfo.codec && <span>{mediaInfo.codec.toUpperCase()}</span>}
          </div>
        </div>
        <button
          onClick={onClose}
          className="ml-4 p-2 hover:bg-gray-800 rounded-lg transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 flex items-center justify-center p-4 overflow-hidden">
        {mediaInfo.media_type === 'video' && (
          <VideoPlayer filePath={mediaInfo.file_path} mediaInfo={mediaInfo} />
        )}
        {mediaInfo.media_type === 'image' && (
          <ImageViewer filePath={mediaInfo.file_path} mediaInfo={mediaInfo} />
        )}
        {mediaInfo.media_type === 'audio' && (
          <AudioPlayer filePath={mediaInfo.file_path} mediaInfo={mediaInfo} />
        )}
        {mediaInfo.media_type === 'unknown' && (
          <div className="text-white text-center">
            <svg className="w-24 h-24 mx-auto mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-lg">Preview not available for this file type</p>
            <p className="text-sm text-gray-400 mt-2">File: {mediaInfo.file_name}</p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="bg-gray-900 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => invoke('open_folder_and_highlight_file', { 
                folder_path: mediaInfo.file_path.substring(0, mediaInfo.file_path.lastIndexOf('/')),
                filename: mediaInfo.file_name 
              })}
              className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors flex items-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Show in Folder
            </button>
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
          >
            Close Preview
          </button>
        </div>
      </div>
    </div>
  );
};

export default MediaPreview;