import React, { useState, useRef, useEffect } from 'react';
import { convertFileSrc } from '@tauri-apps/api/core';
import { MediaInfo } from '../../types/media';

interface ImageViewerProps {
  filePath: string;
  mediaInfo: MediaInfo;
}

const ImageViewer: React.FC<ImageViewerProps> = ({ filePath, mediaInfo }) => {
  const [zoom, setZoom] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // Convert file path to URL that can be loaded by the webview
  const imageUrl = convertFileSrc(filePath);

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.min(Math.max(0.1, zoom * delta), 5);
    setZoom(newZoom);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const resetView = () => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
  };

  const zoomIn = () => {
    setZoom(Math.min(zoom * 1.2, 5));
  };

  const zoomOut = () => {
    setZoom(Math.max(zoom * 0.8, 0.1));
  };

  const fitToScreen = () => {
    if (!containerRef.current || !imageRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const img = imageRef.current;
    
    const scaleX = container.width / img.naturalWidth;
    const scaleY = container.height / img.naturalHeight;
    const scale = Math.min(scaleX, scaleY, 1);
    
    setZoom(scale);
    setPosition({ x: 0, y: 0 });
  };

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      switch (e.key) {
        case '+':
        case '=':
          zoomIn();
          break;
        case '-':
          zoomOut();
          break;
        case '0':
          resetView();
          break;
        case 'f':
          fitToScreen();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [zoom]);

  return (
    <div className="w-full h-full flex flex-col">
      {/* Toolbar */}
      <div className="bg-gray-800 p-2 flex items-center justify-center space-x-4 rounded-t-lg">
        <button
          onClick={zoomOut}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Zoom Out (-)"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
          </svg>
        </button>
        
        <span className="text-sm text-gray-300 min-w-[60px] text-center">
          {Math.round(zoom * 100)}%
        </span>
        
        <button
          onClick={zoomIn}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Zoom In (+)"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7" />
          </svg>
        </button>
        
        <div className="w-px h-6 bg-gray-600"></div>
        
        <button
          onClick={fitToScreen}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Fit to Screen (F)"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          </svg>
        </button>
        
        <button
          onClick={resetView}
          className="p-2 hover:bg-gray-700 rounded transition-colors"
          title="Reset View (0)"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {/* Image Container */}
      <div
        ref={containerRef}
        className="flex-1 overflow-hidden bg-gray-900 relative cursor-move"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{ cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
      >
        <div
          className="absolute inset-0 flex items-center justify-center"
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${zoom})`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.2s ease-out'
          }}
        >
          <img
            ref={imageRef}
            src={imageUrl}
            alt={mediaInfo.file_name}
            className="max-w-full max-h-full object-contain"
            draggable={false}
            onLoad={fitToScreen}
          />
        </div>
      </div>

      {/* Info Bar */}
      <div className="bg-gray-800 p-2 text-sm text-gray-300 flex items-center justify-between rounded-b-lg">
        <div className="flex items-center space-x-4">
          <span>{mediaInfo.width} × {mediaInfo.height}px</span>
          <span>{mediaInfo.file_name}</span>
        </div>
        <div className="text-xs text-gray-500">
          Use mouse wheel to zoom • Drag to pan • Keyboard: +/- to zoom, F to fit, 0 to reset
        </div>
      </div>
    </div>
  );
};

export default ImageViewer;