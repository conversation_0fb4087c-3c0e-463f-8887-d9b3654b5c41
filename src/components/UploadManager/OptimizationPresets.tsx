import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { X, Zap, Film, Loader } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { colors, typography } from '../../design-system';

interface OptimizationPreset {
  platform: string;
  name: string;
  description: string;
  video: {
    width: number;
    height: number;
    fps: number;
    bitrate: string;
    codec: string;
    aspect_ratio: string;
    max_duration?: number;
  };
  audio: {
    bitrate: string;
    sample_rate: number;
    channels: number;
    codec: string;
  };
  output: {
    format: string;
    max_file_size?: number;
    naming_pattern: string;
  };
}

interface OptimizationResult {
  input_path: string;
  output_path: string;
  platform: string;
  duration: number;
  file_size: number;
  optimization_time: number;
}

interface OptimizationPresetsProps {
  filePaths: string[];
  onClose: () => void;
}

const OptimizationPresets: React.FC<OptimizationPresetsProps> = ({
  filePaths,
  onClose,
}) => {
  const [presets, setPresets] = useState<OptimizationPreset[]>([]);
  const [selectedPresets, setSelectedPresets] = useState<string[]>([]);
  const [outputDir, setOutputDir] = useState('');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [results, setResults] = useState<OptimizationResult[]>([]);
  const [estimatedTime, setEstimatedTime] = useState<number>(0);

  useEffect(() => {
    loadPresets();
  }, []);

  useEffect(() => {
    if (selectedPresets.length > 0 && filePaths.length > 0) {
      estimateTime();
    }
  }, [selectedPresets, filePaths]);

  const loadPresets = async () => {
    try {
      const loadedPresets = await invoke<OptimizationPreset[]>('get_optimization_presets');
      setPresets(loadedPresets);
    } catch (error) {
      console.error('Failed to load optimization presets:', error);
    }
  };

  const estimateTime = async () => {
    try {
      const platforms = selectedPresets.map((preset) => {
        const p = presets.find((p) => `${p.platform}-${p.name}` === preset);
        return p?.platform || '';
      }).filter(Boolean);

      let totalTime = 0;
      for (const filePath of filePaths) {
        const time = await invoke<number>('estimate_optimization_time', {
          filePath,
          platforms,
        });
        totalTime += time;
      }
      setEstimatedTime(totalTime);
    } catch (error) {
      console.error('Failed to estimate optimization time:', error);
    }
  };

  const handleOptimize = async () => {
    if (selectedPresets.length === 0 || filePaths.length === 0 || !outputDir) {
      return;
    }

    setIsOptimizing(true);
    setResults([]);

    try {
      for (const filePath of filePaths) {
        for (const presetId of selectedPresets) {
          const preset = presets.find((p) => `${p.platform}-${p.name}` === presetId);
          if (!preset) continue;

          const result = await invoke<OptimizationResult>('optimize_for_platform', {
            inputPath: filePath,
            platform: preset.platform,
            presetName: preset.name,
            outputDir,
          });

          setResults((prev) => [...prev, result]);
        }
      }
    } catch (error) {
      console.error('Optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  };

  const togglePreset = (presetId: string) => {
    if (selectedPresets.includes(presetId)) {
      setSelectedPresets(selectedPresets.filter((id) => id !== presetId));
    } else {
      setSelectedPresets([...selectedPresets, presetId]);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds.toFixed(0)}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${(seconds % 60).toFixed(0)}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  // Group presets by platform
  const groupedPresets = presets.reduce((acc, preset) => {
    if (!acc[preset.platform]) {
      acc[preset.platform] = [];
    }
    acc[preset.platform].push(preset);
    return acc;
  }, {} as Record<string, OptimizationPreset[]>);

  const inputClass = "w-full px-4 py-3 glass-medium border border-glass-border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all";
  const labelClass = "block text-sm font-semibold text-white mb-2";
  const sectionClass = "space-y-4";
  const selectClass = "w-full px-4 py-3 glass-medium border border-glass-border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-transparent transition-all";

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <GlassCard variant="elevated" className="w-full max-w-4xl max-h-[90vh] overflow-auto animate-fade-in">
        <div className="flex items-center justify-between mb-6">
          <h3 
            className="text-2xl font-bold flex items-center gap-3 text-white"
            style={{ fontFamily: typography.fonts.heading }}
          >
            <div className="p-2 rounded-lg" style={{ background: colors.accent.gradient }}>
              <Zap className="w-6 h-6" />
            </div>
            Optimize Videos for Platforms
          </h3>
          <button
            onClick={onClose}
            className="p-2 glass-light hover:glass-medium rounded-lg transition-all text-gray-300 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* File Count */}
        <div className="mb-4 p-4 glass-light rounded-lg border border-glass-border">
          <p className="text-sm text-white">
            Optimizing <strong className="text-primary-400">{filePaths.length}</strong> file(s)
          </p>
        </div>

        {/* Output Directory */}
        <div className="mb-6">
          <label className={labelClass}>Output Directory</label>
          <div className="flex gap-2">
            <input
              type="text"
              value={outputDir}
              onChange={(e) => setOutputDir(e.target.value)}
              placeholder="/path/to/output"
              className={inputClass}
              style={{ fontFamily: typography.fonts.sans }}
            />
            <AnimatedButton
              onClick={async () => {
                const { open } = await import('@tauri-apps/plugin-dialog');
                const selected = await open({
                  directory: true,
                  multiple: false,
                });
                if (selected) {
                  setOutputDir(selected as string);
                }
              }}
              variant="secondary"
              size="md"
            >
              Browse
            </AnimatedButton>
          </div>
        </div>

        {/* Presets */}
        <div className="space-y-4 mb-6">
          {Object.entries(groupedPresets).map(([platform, platformPresets]) => (
            <div key={platform} className="glass-light border border-glass-border rounded-lg p-4">
              <h4 
                className="font-semibold mb-3 capitalize text-white"
                style={{ fontFamily: typography.fonts.heading }}
              >
                {platform}
              </h4>
              <div className="grid grid-cols-2 gap-3">
                {platformPresets.map((preset) => {
                  const presetId = `${preset.platform}-${preset.name}`;
                  const isSelected = selectedPresets.includes(presetId);
                  return (
                    <div
                      key={presetId}
                      onClick={() => togglePreset(presetId)}
                      className={`preset-card p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        isSelected
                          ? 'glass-strong border-primary-500'
                          : 'glass-light border-glass-border hover:glass-medium'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h5 
                            className="font-medium text-white"
                            style={{ fontFamily: typography.fonts.sans }}
                          >
                            {preset.name}
                          </h5>
                          <p className="text-xs text-gray-300 mt-1">
                            {preset.description}
                          </p>
                          <div className="mt-2 space-y-1">
                            <p className="text-xs text-gray-400">
                              <Film className="w-3 h-3 inline mr-1 text-primary-400" />
                              {preset.video.width}x{preset.video.height} @ {preset.video.fps}fps
                            </p>
                            <p className="text-xs text-gray-400">
                              Bitrate: {preset.video.bitrate}
                            </p>
                            {preset.video.max_duration && (
                              <p className="text-xs text-gray-400">
                                Max duration: {preset.video.max_duration}s
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="relative">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => {}}
                            className="sr-only"
                          />
                          <div 
                            className={`
                              w-5 h-5 rounded border-2 transition-all duration-200
                              ${isSelected 
                                ? 'border-primary-400 bg-primary-500' 
                                : 'border-gray-400 hover:border-gray-300'
                              }
                            `}
                          >
                            {isSelected && (
                              <svg 
                                className="w-full h-full text-white p-0.5" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                              >
                                <path 
                                  strokeLinecap="round" 
                                  strokeLinejoin="round" 
                                  strokeWidth={3} 
                                  d="M5 13l4 4L19 7" 
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Estimated Time */}
        {estimatedTime > 0 && (
          <div className="mb-6 p-4 glass-light rounded-lg border border-glass-border">
            <p className="text-sm text-white">
              Estimated optimization time: <strong className="text-accent-400">{formatDuration(estimatedTime)}</strong>
            </p>
          </div>
        )}

        {/* Results */}
        {results.length > 0 && (
          <div className="mb-6">
            <h4 
              className="font-semibold mb-3 text-white"
              style={{ fontFamily: typography.fonts.heading }}
            >
              Optimization Results
            </h4>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {results.map((result, index) => (
                <div key={index} className="p-3 glass-light rounded-lg border border-glass-border">
                  <p className="text-sm font-medium text-white">{result.output_path.split('/').pop()}</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Size: {formatFileSize(result.file_size)} | Time: {formatDuration(result.optimization_time)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <AnimatedButton
            onClick={handleOptimize}
            disabled={selectedPresets.length === 0 || !outputDir || isOptimizing}
            variant="primary"
            size="lg"
            fullWidth
            loading={isOptimizing}
            icon={<Zap className="w-5 h-5" />}
            glow
          >
            {isOptimizing ? 'Optimizing...' : `Optimize for ${selectedPresets.length} Platform(s)`}
          </AnimatedButton>
          <AnimatedButton
            onClick={onClose}
            variant="secondary"
            size="lg"
          >
            Cancel
          </AnimatedButton>
        </div>
      </GlassCard>
    </div>
  );
};

export default OptimizationPresets;