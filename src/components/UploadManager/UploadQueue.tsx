import React from 'react';
import { Play, X, Clock, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { colors, typography } from '../../design-system';

interface QueuedUpload {
  id: string;
  file_path: string;
  config: {
    platform: string;
    title: string;
    description: string;
  };
  status: 'queued' | 'preparing' | 'uploading' | 'processing' | 'published' | 'failed' | 'cancelled';
  progress?: number;
  message?: string;
}

interface UploadQueueProps {
  queue: QueuedUpload[];
  onStartUpload: (uploadId: string, filePath: string) => void;
  onCancelUpload: (uploadId: string) => void;
  isUploading: boolean;
}

const UploadQueue: React.FC<UploadQueueProps> = ({
  queue,
  onStartUpload,
  onCancelUpload,
  isUploading,
}) => {
  const getStatusIcon = (status: QueuedUpload['status']) => {
    switch (status) {
      case 'queued':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'preparing':
      case 'uploading':
      case 'processing':
        return <Loader className="w-4 h-4 text-blue-400 animate-spin" />;
      case 'published':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: QueuedUpload['status']) => {
    switch (status) {
      case 'queued':
        return 'text-gray-400';
      case 'preparing':
      case 'uploading':
      case 'processing':
        return 'text-blue-400';
      case 'published':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getPlatformStyles = (platform: string) => {
    const styles: { [key: string]: { bg: string; gradient?: string } } = {
      youtube: { bg: '#FF0000' },
      tiktok: { bg: '#000000' },
      instagram: { 
        bg: '#DD2A7B',
        gradient: 'linear-gradient(45deg, #FCD34D 0%, #EF4444 25%, #EC4899 50%, #8B5CF6 100%)'
      },
      twitter: { bg: '#1DA1F2' },
      linkedin: { bg: '#0077B5' },
      facebook: { bg: '#1877F2' },
    };
    return styles[platform] || { bg: '#6B7280' };
  };

  return (
    <div className="upload-queue h-full flex flex-col">
      {queue.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Clock className="w-12 h-12 mx-auto mb-3 text-gray-600" />
            <p className="text-gray-400">No uploads in queue</p>
          </div>
        </div>
      ) : (
        <div className="space-y-3 overflow-y-auto flex-1 pr-2">
          {queue.map((upload) => {
            const platformStyle = getPlatformStyles(upload.config.platform);
            
            return (
              <div
                key={upload.id}
                className="upload-item glass-light rounded-lg p-4 hover:bg-glass-medium transition-all duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span
                        className="px-2.5 py-1 text-xs text-white rounded-md shadow-lg"
                        style={{
                          background: platformStyle.gradient || platformStyle.bg,
                        }}
                      >
                        {upload.config.platform}
                      </span>
                      <div className="flex items-center gap-1.5">
                        {getStatusIcon(upload.status)}
                        <span
                          className={`text-sm capitalize ${getStatusColor(upload.status)}`}
                          style={{ fontFamily: typography.fonts.sans }}
                        >
                          {upload.status}
                        </span>
                      </div>
                    </div>
                    
                    <h4 
                      className="font-medium text-sm mb-1 text-white"
                      style={{ fontFamily: typography.fonts.sans }}
                    >
                      {upload.config.title}
                    </h4>
                    
                    <p className="text-xs text-gray-400 truncate">
                      {upload.file_path.split('/').pop()}
                    </p>
                    
                    {upload.message && (
                      <p className="text-xs text-gray-500 mt-2">{upload.message}</p>
                    )}
                    
                    {upload.progress !== undefined && upload.status === 'uploading' && (
                      <div className="mt-3">
                        <div className="w-full bg-glass-light rounded-full h-2 overflow-hidden">
                          <div
                            className="h-2 rounded-full transition-all duration-300"
                            style={{ 
                              width: `${upload.progress}%`,
                              background: colors.primary.gradient
                            }}
                          />
                        </div>
                        <p className="text-xs text-gray-400 mt-1">
                          {upload.progress.toFixed(0)}%
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {upload.status === 'queued' && (
                      <button
                        onClick={() => onStartUpload(upload.id, upload.file_path)}
                        disabled={isUploading}
                        className="p-2 rounded-lg glass-light hover:bg-glass-medium disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 text-green-400 hover:text-green-300"
                      >
                        <Play className="w-4 h-4" />
                      </button>
                    )}
                    
                    {['queued', 'preparing', 'uploading'].includes(upload.status) && (
                      <button
                        onClick={() => onCancelUpload(upload.id)}
                        className="p-2 rounded-lg glass-light hover:bg-glass-medium transition-all duration-200 text-red-400 hover:text-red-300"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default UploadQueue;