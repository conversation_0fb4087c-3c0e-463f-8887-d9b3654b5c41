import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import UploadManager from './UploadManager';
import { invoke } from '@tauri-apps/api/core';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

vi.mock('@tauri-apps/api/event', () => ({
  listen: vi.fn(() => Promise.resolve(() => {})),
}));

describe('UploadManager Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(invoke).mockResolvedValue([]);
  });

  it('should render upload manager with tabs', () => {
    render(<UploadManager />);
    
    expect(screen.getByText('Upload Manager')).toBeInTheDocument();
    expect(screen.getByText('Optimize Videos')).toBeInTheDocument();
    expect(screen.getByText('Select Platforms')).toBeInTheDocument();
  });

  it('should load upload queue on mount', async () => {
    const mockQueue = [
      ['upload_1', { platform: 'youtube', title: 'Test Video' }],
    ];
    vi.mocked(invoke).mockResolvedValueOnce(mockQueue);

    render(<UploadManager />);

    await waitFor(() => {
      expect(invoke).toHaveBeenCalledWith('get_upload_queue');
    });
  });

  it('should handle platform selection', async () => {
    const user = userEvent.setup();
    render(<UploadManager />);

    const youtubeCard = screen.getByText('YouTube').closest('.platform-card');
    expect(youtubeCard).toBeInTheDocument();
    
    await user.click(youtubeCard!);
    
    // Should show connect account button when selected
    expect(screen.getByText('Connect Account')).toBeInTheDocument();
  });

  it('should queue upload when form is submitted', async () => {
    const user = userEvent.setup();
    vi.mocked(invoke).mockResolvedValueOnce('upload_123');
    
    render(<UploadManager />);

    // Select a platform
    const youtubeCard = screen.getByText('YouTube').closest('.platform-card');
    await user.click(youtubeCard!);

    // Fill in the form
    const titleInput = screen.getByPlaceholderText('Enter video title');
    await user.type(titleInput, 'My Test Video');

    // Queue the upload
    const queueButton = screen.getByRole('button', { name: /queue.*file.*platform/i });
    expect(queueButton).toBeDisabled(); // Should be disabled without files

    // Note: File selection would require mocking the file dialog
    // which is complex in tests, so we're testing the basic flow
  });

  it('should show optimization presets when optimize button is clicked', async () => {
    const user = userEvent.setup();
    render(<UploadManager />);

    const optimizeButton = screen.getByText('Optimize Videos');
    await user.click(optimizeButton);

    expect(screen.getByText('Optimize Videos for Platforms')).toBeInTheDocument();
  });
});