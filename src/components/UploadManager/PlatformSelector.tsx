import React from 'react';
import { Youtube, Instagram, Twitter, Linkedin, Facebook } from 'lucide-react';
import { SiTiktok } from 'react-icons/si';
import { colors, typography } from '../../design-system';

interface Platform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  gradient?: string;
  description: string;
}

const platforms: Platform[] = [
  {
    id: 'youtube',
    name: 'YouTube',
    icon: <Youtube className="w-6 h-6" />,
    color: '#FF0000',
    description: 'Upload videos up to 12 hours long',
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    icon: <SiTiktok className="w-5 h-5" />,
    color: '#000000',
    description: 'Short-form videos up to 10 minutes',
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: <Instagram className="w-6 h-6" />,
    color: '#DD2A7B',
    gradient: 'linear-gradient(45deg, #FCD34D 0%, #EF4444 25%, #EC4899 50%, #8B5CF6 100%)',
    description: 'Reels, posts, and IGTV',
  },
  {
    id: 'twitter',
    name: 'Twitter/X',
    icon: <Twitter className="w-6 h-6" />,
    color: '#1DA1F2',
    description: 'Videos up to 2 minutes 20 seconds',
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: <Linkedin className="w-6 h-6" />,
    color: '#0077B5',
    description: 'Professional content up to 10 minutes',
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: <Facebook className="w-6 h-6" />,
    color: '#1877F2',
    description: 'Videos, reels, and stories',
  },
];

interface PlatformSelectorProps {
  selectedPlatforms: string[];
  onPlatformSelect: (platforms: string[]) => void;
}

const PlatformSelector: React.FC<PlatformSelectorProps> = ({
  selectedPlatforms,
  onPlatformSelect,
}) => {
  const togglePlatform = (platformId: string) => {
    if (selectedPlatforms.includes(platformId)) {
      onPlatformSelect(selectedPlatforms.filter((id) => id !== platformId));
    } else {
      onPlatformSelect([...selectedPlatforms, platformId]);
    }
  };

  const handleAuthenticate = async (platformId: string) => {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const authUrl = await invoke<string>('authenticate_platform', {
        platform: platformId,
      });
      
      // Open authentication URL in default browser
      const { open } = await import('@tauri-apps/plugin-shell');
      await open(authUrl);
    } catch (error) {
      console.error(`Failed to authenticate ${platformId}:`, error);
    }
  };

  return (
    <div className="platform-selector">
      <div className="grid grid-cols-2 gap-4">
        {platforms.map((platform) => {
          const isSelected = selectedPlatforms.includes(platform.id);
          return (
            <div
              key={platform.id}
              onClick={() => togglePlatform(platform.id)}
              className={`
                relative overflow-hidden rounded-xl p-4 cursor-pointer transition-all duration-200
                ${isSelected 
                  ? 'glass-strong border-2' 
                  : 'glass-light border border-glass-border hover:glass-medium'
                }
              `}
              style={{
                borderColor: isSelected ? platform.color + '66' : undefined
              }}
            >
              {/* Background glow effect when selected */}
              {isSelected && (
                <div 
                  className="absolute inset-0 opacity-10"
                  style={{
                    background: platform.gradient || platform.color,
                    filter: 'blur(30px)',
                  }}
                />
              )}

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <div
                      className="p-2.5 rounded-lg text-white shadow-lg"
                      style={{
                        background: platform.gradient || platform.color,
                      }}
                    >
                      {platform.icon}
                    </div>
                    <div>
                      <h4 
                        className="font-medium text-white"
                        style={{ fontFamily: typography.fonts.sans }}
                      >
                        {platform.name}
                      </h4>
                      <p 
                        className="text-xs text-gray-400"
                        style={{ fontFamily: typography.fonts.sans }}
                      >
                        {platform.description}
                      </p>
                    </div>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => {}}
                      className="sr-only"
                    />
                    <div 
                      className={`
                        w-5 h-5 rounded border-2 transition-all duration-200
                        ${isSelected 
                          ? 'border-white' 
                          : 'border-gray-500 hover:border-gray-400'
                        }
                      `}
                      style={{
                        background: isSelected ? platform.gradient || platform.color : 'transparent',
                      }}
                    >
                      {isSelected && (
                        <svg 
                          className="w-full h-full text-white p-0.5" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={3} 
                            d="M5 13l4 4L19 7" 
                          />
                        </svg>
                      )}
                    </div>
                  </div>
                </div>
                {isSelected && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAuthenticate(platform.id);
                    }}
                    className="text-xs hover:underline transition-colors mt-2"
                    style={{ color: platform.color }}
                  >
                    Connect Account
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PlatformSelector;