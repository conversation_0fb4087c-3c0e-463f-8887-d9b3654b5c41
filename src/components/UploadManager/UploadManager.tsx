import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { Upload } from 'lucide-react';
import PlatformSelector from './PlatformSelector';
import UploadQueue from './UploadQueue';
import UploadForm from './UploadForm';
import OptimizationPresets from './OptimizationPresets';
import GlassCard from '../ui/GlassCard';
import AnimatedButton from '../ui/AnimatedButton';
import { colors, typography } from '../../design-system';

interface UploadConfig {
  platform: string;
  title: string;
  description: string;
  tags: string[];
  privacy: string;
  scheduled_time?: string;
  thumbnail_path?: string;
  category?: string;
  playlist_id?: string;
}

interface UploadProgress {
  upload_id: string;
  platform: string;
  progress: number;
  status: string;
  message: string;
}

interface QueuedUpload {
  id: string;
  file_path: string;
  config: UploadConfig;
  status: 'queued' | 'preparing' | 'uploading' | 'processing' | 'published' | 'failed' | 'cancelled';
  progress?: number;
  message?: string;
}

const UploadManager: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [uploadQueue, setUploadQueue] = useState<QueuedUpload[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showOptimization, setShowOptimization] = useState(false);

  useEffect(() => {
    // Load upload queue on mount
    loadUploadQueue();

    // Listen for upload progress events
    const unlistenProgress = listen<UploadProgress>('upload-progress', (event) => {
      updateUploadProgress(event.payload);
    });

    return () => {
      unlistenProgress.then((fn) => fn());
    };
  }, []);

  const loadUploadQueue = async () => {
    try {
      const queue = await invoke<[string, UploadConfig][]>('get_upload_queue');
      const formattedQueue: QueuedUpload[] = queue.map(([id, config]) => ({
        id,
        file_path: '', // We'll need to track this separately
        config,
        status: 'queued',
      }));
      setUploadQueue(formattedQueue);
    } catch (error) {
      console.error('Failed to load upload queue:', error);
    }
  };

  const updateUploadProgress = (progress: UploadProgress) => {
    setUploadQueue((prev) =>
      prev.map((upload) =>
        upload.id === progress.upload_id
          ? {
              ...upload,
              status: progress.status as QueuedUpload['status'],
              progress: progress.progress,
              message: progress.message,
            }
          : upload
      )
    );
  };

  const handleFileSelect = (filePaths: string[]) => {
    setSelectedFiles(filePaths);
  };

  const handlePlatformSelect = (platforms: string[]) => {
    setSelectedPlatforms(platforms);
  };

  const handleQueueUpload = async (filePath: string, config: UploadConfig) => {
    try {
      const uploadId = await invoke<string>('queue_upload', {
        filePath,
        config,
      });

      const newUpload: QueuedUpload = {
        id: uploadId,
        file_path: filePath,
        config,
        status: 'queued',
      };

      setUploadQueue((prev) => [...prev, newUpload]);
    } catch (error) {
      console.error('Failed to queue upload:', error);
    }
  };

  const handleStartUpload = async (uploadId: string, filePath: string) => {
    try {
      setIsUploading(true);
      await invoke('start_upload', {
        uploadId,
        filePath,
      });
    } catch (error) {
      console.error('Failed to start upload:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancelUpload = async (uploadId: string) => {
    try {
      await invoke('cancel_upload', { uploadId });
      setUploadQueue((prev) => prev.filter((upload) => upload.id !== uploadId));
    } catch (error) {
      console.error('Failed to cancel upload:', error);
    }
  };

  const handleOptimizeVideos = () => {
    setShowOptimization(true);
  };


  return (
    <div className="upload-manager p-6">
      <GlassCard variant="elevated" className="mb-6 animate-fade-in">
        <div className="flex items-center justify-between">
          <h2 
            className="text-2xl font-bold flex items-center gap-3 text-white"
            style={{ fontFamily: typography.fonts.heading }}
          >
            <div className="p-2 rounded-lg" style={{ background: colors.primary.gradient }}>
              <Upload className="w-6 h-6" />
            </div>
            Upload Manager
          </h2>
          <AnimatedButton
            onClick={handleOptimizeVideos}
            variant="primary"
            size="md"
          >
            Optimize Videos
          </AnimatedButton>
        </div>
      </GlassCard>

      {showOptimization ? (
        <OptimizationPresets
          filePaths={selectedFiles}
          onClose={() => setShowOptimization(false)}
        />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            <GlassCard variant="default">
              <h3 
                className="text-lg font-semibold text-white mb-4"
                style={{ fontFamily: typography.fonts.heading }}
              >
                Select Platforms
              </h3>
              <PlatformSelector
                selectedPlatforms={selectedPlatforms}
                onPlatformSelect={handlePlatformSelect}
              />
            </GlassCard>
            
            <GlassCard variant="default">
              <h3 
                className="text-lg font-semibold text-white mb-4"
                style={{ fontFamily: typography.fonts.heading }}
              >
                Upload Details
              </h3>
              <UploadForm
                selectedFiles={selectedFiles}
                selectedPlatforms={selectedPlatforms}
                onFileSelect={handleFileSelect}
                onQueueUpload={handleQueueUpload}
              />
            </GlassCard>
          </div>

          <div>
            <GlassCard variant="default" className="h-full">
              <h3 
                className="text-lg font-semibold text-white mb-4"
                style={{ fontFamily: typography.fonts.heading }}
              >
                Upload Queue
              </h3>
              <UploadQueue
                queue={uploadQueue}
                onStartUpload={handleStartUpload}
                onCancelUpload={handleCancelUpload}
                isUploading={isUploading}
              />
            </GlassCard>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadManager;