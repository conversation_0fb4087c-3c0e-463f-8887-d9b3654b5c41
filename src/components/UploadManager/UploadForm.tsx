import React, { useState } from 'react';
import { FileVideo, Plus, X, Image } from 'lucide-react';
import { open } from '@tauri-apps/plugin-dialog';
import AnimatedButton from '../ui/AnimatedButton';
import { colors, typography } from '../../design-system';

interface UploadFormData {
  title: string;
  description: string;
  tags: string[];
  privacy: 'public' | 'private' | 'unlisted' | 'friendsonly';
  scheduled_time?: string;
  thumbnail_path?: string;
  category?: string;
}

interface UploadFormProps {
  selectedFiles: string[];
  selectedPlatforms: string[];
  onFileSelect: (files: string[]) => void;
  onQueueUpload: (filePath: string, config: any) => void;
}

const privacyOptions = [
  { value: 'public', label: 'Public' },
  { value: 'private', label: 'Private' },
  { value: 'unlisted', label: 'Unlisted' },
  { value: 'friendsonly', label: 'Friends Only' },
];

const categories = [
  'Gaming',
  'Music',
  'Education',
  'Entertainment',
  'Science & Technology',
  'News & Politics',
  'Sports',
  'Travel & Events',
  'People & Blogs',
  'Comedy',
  'Film & Animation',
  'How-to & Style',
  'Pets & Animals',
  'Nonprofits & Activism',
];

const UploadForm: React.FC<UploadFormProps> = ({
  selectedFiles,
  selectedPlatforms,
  onFileSelect,
  onQueueUpload,
}) => {
  const [formData, setFormData] = useState<UploadFormData>({
    title: '',
    description: '',
    tags: [],
    privacy: 'public',
    category: 'Entertainment',
  });
  const [tagInput, setTagInput] = useState('');

  const handleSelectFiles = async () => {
    try {
      const selected = await open({
        multiple: true,
        filters: [
          {
            name: 'Video',
            extensions: ['mp4', 'mov', 'avi', 'mkv', 'webm'],
          },
        ],
      });

      if (selected) {
        const files = Array.isArray(selected) ? selected : [selected];
        onFileSelect(files);
      }
    } catch (error) {
      console.error('Failed to select files:', error);
    }
  };

  const handleSelectThumbnail = async () => {
    try {
      const selected = await open({
        multiple: false,
        filters: [
          {
            name: 'Image',
            extensions: ['jpg', 'jpeg', 'png', 'webp'],
          },
        ],
      });

      if (selected) {
        setFormData({ ...formData, thumbnail_path: selected as string });
      }
    } catch (error) {
      console.error('Failed to select thumbnail:', error);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tagInput.trim()],
      });
      setTagInput('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter((t) => t !== tag),
    });
  };

  const handleQueueAll = () => {
    if (selectedFiles.length === 0 || selectedPlatforms.length === 0) {
      return;
    }

    selectedFiles.forEach((filePath) => {
      selectedPlatforms.forEach((platform) => {
        onQueueUpload(filePath, {
          platform,
          ...formData,
        });
      });
    });

    // Reset form
    setFormData({
      title: '',
      description: '',
      tags: [],
      privacy: 'public',
      category: 'Entertainment',
    });
  };

  const inputClass = "w-full px-4 py-3 glass-light border border-glass-border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent";
  const labelClass = "block text-sm font-medium text-gray-300 mb-2";

  return (
    <div className="upload-form space-y-6">
      {/* File Selection */}
      <div className="file-selection">
        <label className={labelClass}>Video Files</label>
        {selectedFiles.length === 0 ? (
          <button
            onClick={handleSelectFiles}
            className="w-full glass-light border-2 border-dashed border-glass-border rounded-lg p-8 text-center hover:bg-glass-medium transition-all duration-200"
          >
            <FileVideo className="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p className="text-gray-400">
              Click to select video files
            </p>
          </button>
        ) : (
          <div className="space-y-2">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 glass-light rounded-lg"
              >
                <span className="text-sm truncate text-white">{file.split('/').pop()}</span>
                <button
                  onClick={() =>
                    onFileSelect(selectedFiles.filter((_, i) => i !== index))
                  }
                  className="text-red-400 hover:text-red-300 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
            <button
              onClick={handleSelectFiles}
              className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
            >
              Add more files
            </button>
          </div>
        )}
      </div>

      {/* Title */}
      <div>
        <label className={labelClass}>Title</label>
        <input
          type="text"
          value={formData.title}
          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
          className={inputClass}
          placeholder="Enter video title"
          style={{ fontFamily: typography.fonts.sans }}
        />
      </div>

      {/* Description */}
      <div>
        <label className={labelClass}>Description</label>
        <textarea
          value={formData.description}
          onChange={(e) =>
            setFormData({ ...formData, description: e.target.value })
          }
          className={inputClass}
          rows={4}
          placeholder="Enter video description"
          style={{ fontFamily: typography.fonts.sans }}
        />
      </div>

      {/* Tags */}
      <div>
        <label className={labelClass}>Tags</label>
        <div className="flex gap-2 mb-2">
          <input
            type="text"
            value={tagInput}
            onChange={(e) => setTagInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
            className={inputClass}
            placeholder="Add a tag"
            style={{ fontFamily: typography.fonts.sans }}
          />
          <AnimatedButton
            onClick={handleAddTag}
            variant="primary"
            size="md"
            icon={<Plus className="w-4 h-4" />}
          >
            Add
          </AnimatedButton>
        </div>
        <div className="flex flex-wrap gap-2">
          {formData.tags.map((tag, index) => (
            <span
              key={index}
              className="px-3 py-1 glass-light rounded-full text-sm flex items-center gap-1 text-white"
            >
              {tag}
              <button
                onClick={() => handleRemoveTag(tag)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          ))}
        </div>
      </div>

      {/* Category */}
      <div>
        <label className={labelClass}>Category</label>
        <select
          value={formData.category}
          onChange={(e) =>
            setFormData({ ...formData, category: e.target.value })
          }
          className={inputClass}
          style={{ fontFamily: typography.fonts.sans }}
        >
          {categories.map((category) => (
            <option key={category} value={category} className="bg-dark-bg-secondary">
              {category}
            </option>
          ))}
        </select>
      </div>

      {/* Privacy */}
      <div>
        <label className={labelClass}>Privacy</label>
        <select
          value={formData.privacy}
          onChange={(e) =>
            setFormData({ ...formData, privacy: e.target.value as any })
          }
          className={inputClass}
          style={{ fontFamily: typography.fonts.sans }}
        >
          {privacyOptions.map((option) => (
            <option key={option.value} value={option.value} className="bg-dark-bg-secondary">
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Thumbnail */}
      <div>
        <label className={labelClass}>Thumbnail</label>
        <AnimatedButton
          onClick={handleSelectThumbnail}
          variant="secondary"
          size="md"
          icon={<Image className="w-4 h-4" />}
        >
          {formData.thumbnail_path ? 'Change Thumbnail' : 'Select Thumbnail'}
        </AnimatedButton>
        {formData.thumbnail_path && (
          <p className="text-sm text-gray-400 mt-2">
            {formData.thumbnail_path.split('/').pop()}
          </p>
        )}
      </div>

      {/* Schedule */}
      <div>
        <label className={labelClass}>
          Schedule Upload (Optional)
        </label>
        <input
          type="datetime-local"
          value={formData.scheduled_time || ''}
          onChange={(e) =>
            setFormData({ ...formData, scheduled_time: e.target.value })
          }
          className={inputClass}
          style={{ fontFamily: typography.fonts.sans }}
        />
      </div>

      {/* Queue Button */}
      <AnimatedButton
        onClick={handleQueueAll}
        disabled={
          selectedFiles.length === 0 ||
          selectedPlatforms.length === 0 ||
          !formData.title
        }
        variant="primary"
        size="lg"
        fullWidth
        glow
      >
        Queue {selectedFiles.length} file(s) to {selectedPlatforms.length}{' '}
        platform(s)
      </AnimatedButton>
    </div>
  );
};

export default UploadForm;