// React hook for dynamic configuration management
import { useState, useEffect, useCallback } from 'react';
import { DynamicConfig } from '../config/schemas';
import { 
  dynamicConfigManager, 
  getDynamicConfig, 
  updateDynamicConfig,
  subscribeToConfigChanges 
} from '../config/dynamicConfigManager';

interface UseDynamicConfigReturn {
  config: DynamicConfig;
  updateConfig: (updates: Partial<DynamicConfig>) => Promise<void>;
  resetConfig: (sections?: (keyof DynamicConfig)[]) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  // Convenience methods
  qualityOptions: DynamicConfig['qualityOptions'];
  formatOptions: DynamicConfig['formatOptions'];
  uiLabels: DynamicConfig['ui']['labels'];
  theme: DynamicConfig['ui']['theme'];
  isFeatureEnabled: (feature: keyof DynamicConfig['features']) => boolean;
}

export function useDynamicConfig(): UseDynamicConfigReturn {
  const [config, setConfig] = useState<DynamicConfig>(getDynamicConfig());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Subscribe to configuration changes
    const unsubscribe = subscribeToConfigChanges((newConfig) => {
      setConfig(newConfig);
    });

    // Load initial config
    dynamicConfigManager.initialize().then(() => {
      setConfig(getDynamicConfig());
    }).catch((err) => {
      setError(err.message);
    });

    return unsubscribe;
  }, []);

  const updateConfigWrapper = useCallback(async (updates: Partial<DynamicConfig>) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await updateDynamicConfig(updates);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update configuration');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const resetConfig = useCallback(async (sections?: (keyof DynamicConfig)[]) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await dynamicConfigManager.resetToDefaults(sections);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset configuration');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const isFeatureEnabled = useCallback((feature: keyof DynamicConfig['features']) => {
    return dynamicConfigManager.isFeatureEnabled(feature);
  }, [config]);

  return {
    config,
    updateConfig: updateConfigWrapper,
    resetConfig,
    isLoading,
    error,
    // Convenience accessors
    qualityOptions: config.qualityOptions,
    formatOptions: config.formatOptions,
    uiLabels: config.ui.labels,
    theme: config.ui.theme,
    isFeatureEnabled
  };
}

// Hook for specific configuration sections
export function useConfigSection<K extends keyof DynamicConfig>(
  section: K
): [DynamicConfig[K], (updates: Partial<DynamicConfig[K]>) => Promise<void>] {
  const { config, updateConfig } = useDynamicConfig();
  
  const sectionConfig = config[section];
  
  const updateSection = useCallback(async (updates: Partial<DynamicConfig[K]>) => {
    await updateConfig({
      [section]: { ...sectionConfig, ...updates }
    } as Partial<DynamicConfig>);
  }, [section, sectionConfig, updateConfig]);

  return [sectionConfig, updateSection];
}

// Hook for quality options
export function useQualityOptions() {
  const { config, updateConfig } = useDynamicConfig();
  
  const activeOptions = dynamicConfigManager.getActiveQualityOptions();
  
  const toggleOption = useCallback(async (qualityId: string, enabled: boolean) => {
    await dynamicConfigManager.toggleQualityOption(qualityId, enabled);
  }, []);
  
  const addOption = useCallback(async (option: DynamicConfig['qualityOptions'][0]) => {
    const newOptions = [...config.qualityOptions, option];
    await updateConfig({ qualityOptions: newOptions });
  }, [config.qualityOptions, updateConfig]);
  
  const removeOption = useCallback(async (qualityId: string) => {
    const newOptions = config.qualityOptions.filter(q => q.id !== qualityId);
    await updateConfig({ qualityOptions: newOptions });
  }, [config.qualityOptions, updateConfig]);
  
  const updateOption = useCallback(async (
    qualityId: string, 
    updates: Partial<DynamicConfig['qualityOptions'][0]>
  ) => {
    const newOptions = config.qualityOptions.map(q => 
      q.id === qualityId ? { ...q, ...updates } : q
    );
    await updateConfig({ qualityOptions: newOptions });
  }, [config.qualityOptions, updateConfig]);

  return {
    allOptions: config.qualityOptions,
    activeOptions,
    toggleOption,
    addOption,
    removeOption,
    updateOption
  };
}

// Hook for format options
export function useFormatOptions() {
  const { config } = useDynamicConfig();
  
  const activeOptions = dynamicConfigManager.getActiveFormatOptions();
  
  const toggleOption = useCallback(async (formatId: string, enabled: boolean) => {
    await dynamicConfigManager.toggleFormatOption(formatId, enabled);
  }, []);
  
  const getFormatsForQuality = useCallback((qualityId: string) => {
    return dynamicConfigManager.getFormatsForQuality(qualityId);
  }, []);

  return {
    allOptions: config.formatOptions,
    activeOptions,
    toggleOption,
    getFormatsForQuality
  };
}

// Hook for UI labels (for i18n)
export function useUILabels() {
  const { uiLabels } = useDynamicConfig();
  
  const updateLabels = useCallback(async (labels: Partial<DynamicConfig['ui']['labels']>) => {
    await dynamicConfigManager.updateUILabels(labels);
  }, []);
  
  // Helper function to format labels with parameters
  const formatLabel = useCallback((key: keyof typeof uiLabels, ...args: unknown[]): string => {
    let label = uiLabels[key];
    
    // Simple parameter replacement
    args.forEach((arg, index) => {
      label = label.replace(`{${index}}`, String(arg));
    });
    
    return label;
  }, [uiLabels]);

  return {
    labels: uiLabels,
    updateLabels,
    formatLabel,
    t: formatLabel // Alias for easier migration from i18n
  };
}

// Hook for theme configuration
export function useTheme() {
  const { theme, config, updateConfig } = useDynamicConfig();
  
  const updateTheme = useCallback(async (updates: Partial<DynamicConfig['ui']['theme']>) => {
    const newTheme = { ...theme, ...updates };
    const newUi = { ...config.ui, theme: newTheme };
    await updateConfig({ ui: newUi });
  }, [theme, config.ui, updateConfig]);
  
  const getQualityColor = useCallback((
    color: string, 
    mode: 'light' | 'dark' = 'light'
  ) => {
    return theme.qualityColors[color]?.[mode] || theme.qualityColors.blue[mode];
  }, [theme]);

  return {
    theme,
    updateTheme,
    getQualityColor,
    colors: theme.colors,
    isDark: config.ui.theme.id.includes('dark')
  };
}

// Hook for feature flags
export function useFeatureFlags() {
  const { config, isFeatureEnabled } = useDynamicConfig();
  
  return {
    mediaEditor: isFeatureEnabled('mediaEditor'),
    templates: isFeatureEnabled('templates'),
    collaboration: isFeatureEnabled('collaboration'),
    cloudSync: isFeatureEnabled('cloudSync'),
    features: config.features
  };
}