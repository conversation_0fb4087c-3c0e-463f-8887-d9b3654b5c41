import { DownloadRepository } from '../../domain/repositories';
import { DownloadDomainService } from '../../domain/services/DownloadDomainService';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export interface DownloadProgressEvent {
  downloadId: string;
  bytesDownloaded: number;
  totalBytes: number;
  speedBytesPerSecond: number;
}

export interface DownloadCompletedEvent {
  downloadId: string;
  filePath: string;
}

export interface DownloadFailedEvent {
  downloadId: string;
  error: string;
}

export type DownloadEvent = 
  | { type: 'progress'; data: DownloadProgressEvent }
  | { type: 'completed'; data: DownloadCompletedEvent }
  | { type: 'failed'; data: DownloadFailedEvent };

export interface EventEmitter {
  emit(event: DownloadEvent): void;
}

export interface DownloadEngine {
  startDownload(id: string, url: string, filePath: string): Promise<void>;
  pauseDownload(id: string): Promise<void>;
  resumeDownload(id: string): Promise<void>;
  cancelDownload(id: string): Promise<void>;
}

export class DownloadApplicationService {
  constructor(
    public readonly repository: DownloadRepository,
    private readonly eventEmitter: EventEmitter,
    private readonly downloadEngine: DownloadEngine
  ) {}

  async createDownload(
    url: string, 
    filename: string, 
    quality: string, 
    downloadPath: string
  ): Promise<string> {
    // Validate individual parameters first
    if (!url) {
      throw new Error('URL cannot be empty');
    }
    if (!filename) {
      throw new Error('Filename cannot be empty');
    }
    if (!quality) {
      throw new Error('Quality cannot be empty');
    }
    if (!downloadPath) {
      throw new Error('Download path cannot be empty');
    }

    // Validate request using domain service
    const fullPath = `${downloadPath}/${filename}`;
    DownloadDomainService.validateDownloadRequest(url, fullPath, quality);

    // Check if we can start another download
    const allDownloads = await this.repository.findAll();
    if (!DownloadDomainService.canDownloadsConcurrently(allDownloads)) {
      throw new Error('Maximum number of concurrent downloads reached');
    }

    // Create domain entity
    const download = Download.create(url, fullPath, quality);
    
    // Save to repository
    await this.repository.save(download);

    // Start download process
    this.startDownloadProcess(download);

    return download.getId().toString();
  }

  async pauseDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    download.pause();
    await this.repository.save(download);
    await this.downloadEngine.pauseDownload(downloadId);
  }

  async resumeDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    // Check concurrent download limit
    const allDownloads = await this.repository.findAll();
    if (!DownloadDomainService.canDownloadsConcurrently(allDownloads)) {
      throw new Error('Maximum number of concurrent downloads reached');
    }

    // Let startDownloadProcess handle the state transition
    await this.startDownloadProcess(download);
  }

  async cancelDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    await this.downloadEngine.cancelDownload(downloadId);
    await this.repository.delete(id);
  }

  async getAllDownloads(): Promise<Download[]> {
    return this.repository.findAll();
  }

  async getDownloadsByStatus(status: DownloadStatus): Promise<Download[]> {
    return this.repository.findByStatus(status);
  }

  async clearCompletedDownloads(): Promise<void> {
    await this.repository.deleteByStatus(DownloadStatus.COMPLETED);
  }

  // Event handlers for download engine events
  async handleDownloadProgress(event: DownloadProgressEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.updateProgress(
      event.bytesDownloaded, 
      event.totalBytes, 
      event.speedBytesPerSecond
    );
    
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'progress', data: event });
  }

  async handleDownloadCompleted(event: DownloadCompletedEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.complete();
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'completed', data: event });
  }

  async handleDownloadFailed(event: DownloadFailedEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.fail(event.error);
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'failed', data: event });
  }

  private async startDownloadProcess(download: Download): Promise<void> {
    try {
      // Only call start() if the download is not already downloading
      if (download.getStatus() !== DownloadStatus.DOWNLOADING) {
        download.start();
      }
      await this.repository.save(download);
      
      await this.downloadEngine.startDownload(
        download.getId().toString(),
        download.getUrl().getValue(),
        download.getFilePath().getValue()
      );
    } catch (error) {
      download.fail(error instanceof Error ? error.message : 'Unknown error');
      await this.repository.save(download);
    }
  }
}
