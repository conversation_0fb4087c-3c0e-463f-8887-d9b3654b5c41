import { DownloadApplicationService, EventEmitter } from './DownloadApplicationService';
import { createDownloadRepository } from '../../infrastructure/repositories';
import { createDownloadEngine } from '../../infrastructure/DownloadEngine';

// Simple event emitter implementation
class SimpleEventEmitter implements EventEmitter {
  private listeners: Array<(event: any) => void> = [];

  emit(event: any): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in event listener:', error);
      }
    });
  }

  addListener(listener: (event: any) => void): void {
    this.listeners.push(listener);
  }

  removeListener(listener: (event: any) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
}

// Factory to create application service with all dependencies
export function createDownloadApplicationService(): DownloadApplicationService {
  const repository = createDownloadRepository();
  const downloadEngine = createDownloadEngine();
  const eventEmitter = new SimpleEventEmitter();

  return new DownloadApplicationService(
    repository,
    eventEmitter,
    downloadEngine
  );
}

// Export the event emitter for use in other parts of the app
export const globalEventEmitter = new SimpleEventEmitter();

// Create singleton application service
export const downloadApplicationService = createDownloadApplicationService();
