// CreatorOS Design System - Animations
// Smooth, professional animations for micro-interactions

export const animations = {
  // Transition durations
  duration: {
    instant: '0ms',
    fast: '150ms',
    base: '200ms',
    slow: '300ms',
    slower: '400ms',
    slowest: '500ms',
  },

  // Easing functions
  easing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    elastic: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    smooth: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
  },

  // Keyframe animations
  keyframes: {
    // Fade animations
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    fadeOut: {
      from: { opacity: 1 },
      to: { opacity: 0 },
    },

    // Scale animations
    scaleIn: {
      from: { transform: 'scale(0.95)', opacity: 0 },
      to: { transform: 'scale(1)', opacity: 1 },
    },
    scaleOut: {
      from: { transform: 'scale(1)', opacity: 1 },
      to: { transform: 'scale(0.95)', opacity: 0 },
    },

    // Slide animations
    slideInUp: {
      from: { transform: 'translateY(20px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    slideInDown: {
      from: { transform: 'translateY(-20px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 },
    },
    slideInLeft: {
      from: { transform: 'translateX(-20px)', opacity: 0 },
      to: { transform: 'translateX(0)', opacity: 1 },
    },
    slideInRight: {
      from: { transform: 'translateX(20px)', opacity: 0 },
      to: { transform: 'translateX(0)', opacity: 1 },
    },

    // Shimmer effect for loading states
    shimmer: {
      '0%': { backgroundPosition: '-200% center' },
      '100%': { backgroundPosition: '200% center' },
    },

    // Pulse for attention
    pulse: {
      '0%, 100%': { opacity: 1 },
      '50%': { opacity: 0.5 },
    },

    // Spin for loading
    spin: {
      from: { transform: 'rotate(0deg)' },
      to: { transform: 'rotate(360deg)' },
    },

    // Bounce for notifications
    bounce: {
      '0%, 100%': { transform: 'translateY(0)' },
      '50%': { transform: 'translateY(-10px)' },
    },

    // Glow pulse for AI elements
    glowPulse: {
      '0%, 100%': { 
        boxShadow: '0 0 20px rgba(139, 92, 246, 0.5)',
        borderColor: 'rgba(139, 92, 246, 0.5)',
      },
      '50%': { 
        boxShadow: '0 0 40px rgba(139, 92, 246, 0.8)',
        borderColor: 'rgba(139, 92, 246, 0.8)',
      },
    },

    // Progress bar animation
    progress: {
      from: { transform: 'translateX(-100%)' },
      to: { transform: 'translateX(0)' },
    },

    // Wave animation for backgrounds
    wave: {
      '0%': { transform: 'translateX(0) translateY(0) rotate(0deg)' },
      '33%': { transform: 'translateX(30px) translateY(-30px) rotate(120deg)' },
      '66%': { transform: 'translateX(-20px) translateY(20px) rotate(240deg)' },
      '100%': { transform: 'translateX(0) translateY(0) rotate(360deg)' },
    },
  },

  // Transition presets
  transitions: {
    // Basic transitions
    opacity: 'opacity 200ms ease-in-out',
    transform: 'transform 200ms ease-in-out',
    all: 'all 200ms ease-in-out',
    
    // Common combinations
    fade: 'opacity 200ms ease-in-out',
    scale: 'transform 200ms ease-out',
    slide: 'transform 300ms cubic-bezier(0.25, 0.1, 0.25, 1)',
    
    // Complex transitions
    smooth: 'all 300ms cubic-bezier(0.25, 0.1, 0.25, 1)',
    bounce: 'all 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    elastic: 'all 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // CSS animation classes
  css: `
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    @keyframes fadeOut { from { opacity: 1; } to { opacity: 0; } }
    @keyframes scaleIn { from { transform: scale(0.95); opacity: 0; } to { transform: scale(1); opacity: 1; } }
    @keyframes slideInUp { from { transform: translateY(20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
    @keyframes slideInDown { from { transform: translateY(-20px); opacity: 0; } to { transform: translateY(0); opacity: 1; } }
    @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
    @keyframes shimmer { 0% { background-position: -200% center; } 100% { background-position: 200% center; } }
    @keyframes glowPulse { 
      0%, 100% { 
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
        border-color: rgba(139, 92, 246, 0.5);
      }
      50% { 
        box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
        border-color: rgba(139, 92, 246, 0.8);
      }
    }

    .animate-fadeIn { animation: fadeIn 200ms ease-in-out; }
    .animate-fadeOut { animation: fadeOut 200ms ease-in-out; }
    .animate-scaleIn { animation: scaleIn 200ms ease-out; }
    .animate-slideInUp { animation: slideInUp 300ms ease-out; }
    .animate-slideInDown { animation: slideInDown 300ms ease-out; }
    .animate-spin { animation: spin 1s linear infinite; }
    .animate-pulse { animation: pulse 2s ease-in-out infinite; }
    .animate-shimmer { animation: shimmer 2s linear infinite; }
    .animate-glowPulse { animation: glowPulse 2s ease-in-out infinite; }
  `,
};

// Framer Motion variants for complex animations
export const motionVariants = {
  // Container variants for staggered children
  container: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  },

  // Item variants for list animations
  item: {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  },

  // Card hover effects
  card: {
    rest: {
      scale: 1,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    },
    hover: {
      scale: 1.02,
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10,
      },
    },
    tap: {
      scale: 0.98,
    },
  },

  // Button effects
  button: {
    rest: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 },
  },

  // Modal/overlay animations
  overlay: {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.3 },
    },
  },

  modal: {
    hidden: { 
      opacity: 0,
      scale: 0.95,
      y: 20,
    },
    visible: { 
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20,
      },
    },
  },

  // Drawer animations
  drawer: {
    left: {
      hidden: { x: '-100%' },
      visible: { x: 0 },
    },
    right: {
      hidden: { x: '100%' },
      visible: { x: 0 },
    },
  },
};