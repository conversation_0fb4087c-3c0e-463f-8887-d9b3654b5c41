// CreatorOS Design System - Typography
// Modern typography system using Inter and Space Grotesk

export const typography = {
  // Font families
  fonts: {
    sans: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    heading: '"Space Grotesk", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    mono: '"JetBrains Mono", "SF Mono", Monaco, Consolas, "Courier New", monospace',
  },

  // Font sizes - using rem for accessibility
  sizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
    '7xl': '4.5rem',  // 72px
    '8xl': '6rem',    // 96px
  },

  // Line heights
  lineHeights: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },

  // Font weights
  weights: {
    thin: 100,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },

  // Letter spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },

  // Text styles - pre-composed for consistency
  styles: {
    // Display styles - for hero sections
    display: {
      xl: {
        fontSize: '4.5rem',
        lineHeight: '1',
        letterSpacing: '-0.02em',
        fontWeight: 800,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      lg: {
        fontSize: '3.75rem',
        lineHeight: '1',
        letterSpacing: '-0.02em',
        fontWeight: 800,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      md: {
        fontSize: '3rem',
        lineHeight: '1.2',
        letterSpacing: '-0.02em',
        fontWeight: 700,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      sm: {
        fontSize: '2.25rem',
        lineHeight: '1.25',
        letterSpacing: '-0.02em',
        fontWeight: 700,
        fontFamily: '"Space Grotesk", sans-serif',
      },
    },

    // Heading styles
    heading: {
      h1: {
        fontSize: '2.25rem',
        lineHeight: '1.25',
        letterSpacing: '-0.02em',
        fontWeight: 700,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      h2: {
        fontSize: '1.875rem',
        lineHeight: '1.3',
        letterSpacing: '-0.01em',
        fontWeight: 600,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      h3: {
        fontSize: '1.5rem',
        lineHeight: '1.375',
        letterSpacing: '0',
        fontWeight: 600,
        fontFamily: '"Space Grotesk", sans-serif',
      },
      h4: {
        fontSize: '1.25rem',
        lineHeight: '1.4',
        letterSpacing: '0',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
      h5: {
        fontSize: '1.125rem',
        lineHeight: '1.5',
        letterSpacing: '0',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
      h6: {
        fontSize: '1rem',
        lineHeight: '1.5',
        letterSpacing: '0',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
    },

    // Body text styles
    body: {
      lg: {
        fontSize: '1.125rem',
        lineHeight: '1.75',
        letterSpacing: '0',
        fontWeight: 400,
        fontFamily: 'Inter, sans-serif',
      },
      base: {
        fontSize: '1rem',
        lineHeight: '1.625',
        letterSpacing: '0',
        fontWeight: 400,
        fontFamily: 'Inter, sans-serif',
      },
      sm: {
        fontSize: '0.875rem',
        lineHeight: '1.5',
        letterSpacing: '0',
        fontWeight: 400,
        fontFamily: 'Inter, sans-serif',
      },
      xs: {
        fontSize: '0.75rem',
        lineHeight: '1.5',
        letterSpacing: '0',
        fontWeight: 400,
        fontFamily: 'Inter, sans-serif',
      },
    },

    // Label styles
    label: {
      lg: {
        fontSize: '0.875rem',
        lineHeight: '1.25',
        letterSpacing: '0.025em',
        fontWeight: 500,
        fontFamily: 'Inter, sans-serif',
        textTransform: 'none',
      },
      base: {
        fontSize: '0.75rem',
        lineHeight: '1',
        letterSpacing: '0.05em',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
        textTransform: 'uppercase',
      },
      sm: {
        fontSize: '0.6875rem',
        lineHeight: '1',
        letterSpacing: '0.05em',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
        textTransform: 'uppercase',
      },
    },

    // Special styles
    quote: {
      fontSize: '1.25rem',
      lineHeight: '1.75',
      letterSpacing: '0',
      fontWeight: 400,
      fontFamily: 'Inter, sans-serif',
      fontStyle: 'italic',
    },
    
    code: {
      fontSize: '0.875rem',
      lineHeight: '1.5',
      letterSpacing: '0',
      fontWeight: 400,
      fontFamily: '"JetBrains Mono", monospace',
    },

    button: {
      base: {
        fontSize: '0.875rem',
        lineHeight: '1.25',
        letterSpacing: '0.025em',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
      lg: {
        fontSize: '1rem',
        lineHeight: '1.5',
        letterSpacing: '0',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
      sm: {
        fontSize: '0.75rem',
        lineHeight: '1',
        letterSpacing: '0.025em',
        fontWeight: 600,
        fontFamily: 'Inter, sans-serif',
      },
    },
  },

  // Text decoration
  decoration: {
    none: 'none',
    underline: 'underline',
    lineThrough: 'line-through',
  },

  // Text transform
  transform: {
    none: 'none',
    uppercase: 'uppercase',
    lowercase: 'lowercase',
    capitalize: 'capitalize',
  },
};