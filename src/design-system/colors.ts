// CreatorOS Design System - Colors
// Premium color palette with gradients for a modern, professional look

export const colors = {
  // Primary gradient - Deep purple to electric blue
  primary: {
    gradient: 'linear-gradient(135deg, #6B46C1 0%, #3B82F6 100%)',
    gradientHover: 'linear-gradient(135deg, #5A3AA8 0%, #2563EB 100%)',
    solid: '#5563D1',
    50: '#EEF2FF',
    100: '#E0E7FF',
    200: '#C7D2FE',
    300: '#A5B4FC',
    400: '#818CF8',
    500: '#6366F1',
    600: '#4F46E5',
    700: '#4338CA',
    800: '#3730A3',
    900: '#312E81',
  },

  // Accent - Vibrant coral for CTAs
  accent: {
    gradient: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%)',
    solid: '#FF6B6B',
    hover: '#FF5252',
    50: '#FFF1F1',
    100: '#FFE1E1',
    200: '#FFC7C7',
    300: '#FFA0A0',
    400: '#FF6B6B',
    500: '#FF3838',
    600: '#EF1C1C',
    700: '#C41414',
    800: '#A11414',
    900: '#841818',
  },

  // Success - Emerald
  success: {
    gradient: 'linear-gradient(135deg, #10B981 0%, #34D399 100%)',
    solid: '#10B981',
    50: '#ECFDF5',
    100: '#D1FAE5',
    200: '#A7F3D0',
    300: '#6EE7B7',
    400: '#34D399',
    500: '#10B981',
    600: '#059669',
    700: '#047857',
    800: '#065F46',
    900: '#064E3B',
  },

  // Dark mode backgrounds with purple undertones
  dark: {
    bg: {
      primary: '#0F0F14',     // Main background
      secondary: '#1A1A23',   // Card background
      tertiary: '#252533',    // Elevated surfaces
      hover: '#2A2A3A',       // Hover states
    },
    border: {
      subtle: 'rgba(139, 92, 246, 0.1)',
      default: 'rgba(139, 92, 246, 0.2)',
      strong: 'rgba(139, 92, 246, 0.3)',
    }
  },

  // Light mode (keeping for future)
  light: {
    bg: {
      primary: '#FFFFFF',
      secondary: '#F9FAFB',
      tertiary: '#F3F4F6',
      hover: '#E5E7EB',
    },
    border: {
      subtle: 'rgba(107, 70, 193, 0.1)',
      default: 'rgba(107, 70, 193, 0.2)',
      strong: 'rgba(107, 70, 193, 0.3)',
    }
  },

  // Glass morphism effects
  glass: {
    light: 'rgba(255, 255, 255, 0.05)',
    medium: 'rgba(255, 255, 255, 0.1)',
    strong: 'rgba(255, 255, 255, 0.15)',
    border: 'rgba(255, 255, 255, 0.1)',
    shadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
  },

  // Status colors
  status: {
    info: '#3B82F6',
    warning: '#F59E0B',
    error: '#EF4444',
    neutral: '#6B7280',
  },

  // Platform colors
  platforms: {
    youtube: '#FF0000',
    tiktok: '#000000',
    instagram: {
      gradient: 'linear-gradient(45deg, #F58529, #DD2A7B, #8134AF, #515BD4)',
      solid: '#DD2A7B',
    },
    twitter: '#1DA1F2',
    linkedin: '#0077B5',
    facebook: '#1877F2',
  },

  // Text colors
  text: {
    primary: '#FFFFFF',
    secondary: 'rgba(255, 255, 255, 0.8)',
    tertiary: 'rgba(255, 255, 255, 0.6)',
    muted: 'rgba(255, 255, 255, 0.4)',
  }
};

// Gradient presets for various UI elements
export const gradients = {
  // Hero gradients
  hero: {
    primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    secondary: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    success: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  },

  // Mesh gradients for backgrounds
  mesh: {
    purple: `
      radial-gradient(at 0% 0%, hsla(253,16%,7%,1) 0, transparent 50%),
      radial-gradient(at 50% 0%, hsla(225,39%,30%,1) 0, transparent 50%),
      radial-gradient(at 100% 0%, hsla(339,49%,30%,1) 0, transparent 50%)
    `,
    blue: `
      radial-gradient(at 0% 100%, hsla(253,16%,7%,1) 0, transparent 50%),
      radial-gradient(at 50% 100%, hsla(225,39%,20%,1) 0, transparent 50%),
      radial-gradient(at 100% 100%, hsla(229,49%,20%,1) 0, transparent 50%)
    `,
  },

  // Animated gradients
  animated: {
    shimmer: 'linear-gradient(105deg, transparent 40%, rgba(255,255,255,0.2) 50%, transparent 60%)',
    glow: 'linear-gradient(105deg, transparent 40%, rgba(139,92,246,0.3) 50%, transparent 60%)',
  }
};

// Shadows for depth
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  glow: '0 0 50px rgba(139, 92, 246, 0.3)',
  glowStrong: '0 0 100px rgba(139, 92, 246, 0.5)',
};