/* Force dark theme overrides - load this last */

/* CRITICAL: Set dark background before anything else renders */
html {
  background-color: #0F0F14 !important;
  color: #f8fafc !important;
}

body {
  background-color: #0F0F14 !important;
  color: #f8fafc !important;
  margin: 0 !important;
  padding: 0 !important;
}

#root {
  background-color: #0F0F14 !important;
  color: #f8fafc !important;
  min-height: 100vh !important;
}

/* Override any element with bg-white or bg-gray classes */
[class*="bg-white"],
[class*="bg-gray-100"],
[class*="bg-gray-50"] {
  background-color: var(--dark-bg-secondary, #1A1A23) !important;
}

/* Main app container */
.min-h-screen {
  background-color: #0F0F14 !important;
}

/* Ensure all direct children of root use dark background */
#root > div {
  background-color: #0F0F14 !important;
}

/* Override Tailwind's default body styles */
body {
  background-color: #0F0F14 !important;
  color: #f8fafc !important;
}