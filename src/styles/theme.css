/* CreatorOS Premium Theme CSS Variables */

/* Import premium fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@400;500;600;700&display=swap');

:root {
  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-heading: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', Monaco, Consolas, 'Courier New', monospace;

  /* Premium Color System */
  --color-primary-50: #EEF2FF;
  --color-primary-100: #E0E7FF;
  --color-primary-200: #C7D2FE;
  --color-primary-300: #A5B4FC;
  --color-primary-400: #818CF8;
  --color-primary-500: #6366F1;
  --color-primary-600: #4F46E5;
  --color-primary-700: #4338CA;
  --color-primary-800: #3730A3;
  --color-primary-900: #312E81;

  /* Gradient System */
  --gradient-primary: linear-gradient(135deg, #6B46C1 0%, #3B82F6 100%);
  --gradient-primary-hover: linear-gradient(135deg, #5A3AA8 0%, #2563EB 100%);
  --gradient-accent: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
  --gradient-success: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  --gradient-warning: linear-gradient(135deg, #F59E0B 0%, #FCD34D 100%);
  --gradient-error: linear-gradient(135deg, #EF4444 0%, #FCA5A5 100%);

  /* Dark Background System */
  --dark-bg-primary: #0F0F14;
  --dark-bg-secondary: #1A1A23;
  --dark-bg-tertiary: #252533;
  --dark-bg-hover: #2A2A3A;
  --dark-bg-active: #30303F;

  /* Glass Morphism */
  --glass-light: rgba(255, 255, 255, 0.05);
  --glass-medium: rgba(255, 255, 255, 0.1);
  --glass-strong: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-shadow-sm: 0 4px 16px 0 rgba(31, 38, 135, 0.25);
  --glass-shadow-lg: 0 16px 48px 0 rgba(31, 38, 135, 0.45);

  /* Border System */
  --border-subtle: rgba(139, 92, 246, 0.1);
  --border-default: rgba(139, 92, 246, 0.2);
  --border-strong: rgba(139, 92, 246, 0.3);

  /* Glow Effects */
  --glow-primary: 0 0 20px rgba(139, 92, 246, 0.3);
  --glow-primary-strong: 0 0 40px rgba(139, 92, 246, 0.5);
  --glow-accent: 0 0 20px rgba(255, 107, 107, 0.3);
  --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);

  /* Platform Colors */
  --platform-youtube: #FF0000;
  --platform-tiktok: #000000;
  --platform-instagram: #DD2A7B;
  --platform-twitter: #1DA1F2;
  --platform-linkedin: #0077B5;
  --platform-facebook: #1877F2;

  /* Animations */
  --transition-smooth: cubic-bezier(0.25, 0.1, 0.25, 1);
  --transition-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Global Styles */
body {
  font-family: var(--font-sans);
  background-color: var(--dark-bg-primary);
  color: #f8fafc;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Background Gradient Mesh */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: 
    radial-gradient(at 0% 0%, hsla(253,16%,7%,1) 0, transparent 50%),
    radial-gradient(at 50% 0%, hsla(225,39%,30%,1) 0, transparent 50%),
    radial-gradient(at 100% 0%, hsla(339,49%,30%,1) 0, transparent 50%);
  opacity: 0.4;
}

/* Glass Effect Classes */
.glass {
  background: var(--glass-medium);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--glass-border);
}

.glass-light {
  background: var(--glass-light);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: var(--glass-strong);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Text Gradient Utility */
.text-gradient {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: inline-block;
}

.text-gradient-accent {
  background: var(--gradient-accent);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Glow Effects */
.glow {
  box-shadow: var(--glow-primary);
}

.glow-strong {
  box-shadow: var(--glow-primary-strong);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

/* Animation Classes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% { background-position: -200% center; }
  100% { background-position: 200% center; }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
    border-color: rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
    border-color: rgba(139, 92, 246, 0.8);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(105deg, transparent 40%, rgba(255,255,255,0.2) 50%, transparent 60%);
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

.animate-glow-pulse {
  animation: glowPulse 2s ease-in-out infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--glass-medium);
  border-radius: 4px;
  border: 1px solid var(--glass-border);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--glass-strong);
}

/* Selection */
::selection {
  background: rgba(139, 92, 246, 0.3);
  color: white;
}

/* Focus Styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Headings with Premium Font */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  letter-spacing: -0.02em;
}

/* Smooth Transitions */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-timing-function: var(--transition-smooth);
  transition-duration: 200ms;
}

/* Platform Brand Colors as Utilities */
.platform-youtube { color: var(--platform-youtube); }
.platform-tiktok { color: var(--platform-tiktok); }
.platform-instagram { color: var(--platform-instagram); }
.platform-twitter { color: var(--platform-twitter); }
.platform-linkedin { color: var(--platform-linkedin); }
.platform-facebook { color: var(--platform-facebook); }

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(90deg, var(--dark-bg-secondary) 25%, var(--dark-bg-tertiary) 50%, var(--dark-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}