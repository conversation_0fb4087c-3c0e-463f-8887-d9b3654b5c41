// This utility helps diagnose and fix download folder issues

import { invoke } from '@tauri-apps/api/core';
import { getConfig } from '../config/configManager';
import { 
  getDefaultDownloadsPath, 
  ensureDirectoryExists, 
  testDirectoryAccess,
  validateDownloadPath,
  getSuggestedDownloadPaths,
  openFolderInExplorer
} from './folderUtils';

export interface DownloadFolderDiagnostic {
  status: 'success' | 'warning' | 'error';
  path: string;
  message: string;
  suggestions?: string[];
  canWrite: boolean;
  exists: boolean;
}

/**
 * Comprehensive download folder diagnostic
 */
export const diagnoseDownloadFolder = async (): Promise<DownloadFolderDiagnostic> => {
  console.log('🔍 Starting download folder diagnostic...');
  
  try {
    // Step 1: Get the configured download path
    const config = getConfig();
    let currentPath = config.paths.defaultDownloadDir;
    
    console.log('📁 Configured download path:', currentPath);
    
    // Step 2: If no configured path, get default
    if (!currentPath || currentPath.trim() === '') {
      console.log('⚠️ No configured path, getting default...');
      currentPath = await getDefaultDownloadsPath();
      console.log('📁 Default download path:', currentPath);
    }
    
    // Step 3: Test if the path exists and is accessible
    const exists = await testPathExists(currentPath);
    const canWrite = exists ? await testDirectoryAccess(currentPath) : false;
    
    console.log(`📊 Path analysis: exists=${exists}, canWrite=${canWrite}`);
    
    // Step 4: If path doesn't work, try to fix it
    if (!exists || !canWrite) {
      console.log('🔧 Attempting to fix download folder...');
      const fixResult = await fixDownloadFolder(currentPath);
      
      if (fixResult.success) {
        return {
          status: 'success',
          path: fixResult.path,
          message: `Download folder fixed: ${fixResult.path}`,
          canWrite: true,
          exists: true
        };
      } else {
        // Get suggestions for alternative paths
        const suggestions = await getSuggestedDownloadPaths();
        
        return {
          status: 'error',
          path: currentPath,
          message: `Cannot access download folder: ${fixResult.error}`,
          suggestions,
          canWrite: false,
          exists
        };
      }
    }
    
    // Step 5: Path is working
    return {
      status: 'success',
      path: currentPath,
      message: 'Download folder is accessible and ready',
      canWrite: true,
      exists: true
    };
    
  } catch (error) {
    console.error('❌ Download folder diagnostic failed:', error);
    
    // Emergency fallback
    const emergencyPath = await getEmergencyDownloadPath();
    
    return {
      status: 'error',
      path: emergencyPath,
      message: `Diagnostic failed: ${error}. Using emergency path.`,
      suggestions: [emergencyPath],
      canWrite: false,
      exists: false
    };
  }
};

/**
 * Attempt to fix download folder issues
 */
export const fixDownloadFolder = async (path: string): Promise<{ success: boolean; path: string; error?: string }> => {
  console.log('🔧 Attempting to fix download folder:', path);
  
  try {
    // Try to create the directory if it doesn't exist
    const created = await ensureDirectoryExists(path);
    
    if (created) {
      console.log('✅ Successfully created/verified directory:', path);
      return { success: true, path };
    }
    
    // If that didn't work, try alternative paths
    console.log('⚠️ Primary path failed, trying alternatives...');
    
    const alternatives = await getSuggestedDownloadPaths();
    
    for (const altPath of alternatives) {
      console.log('🔄 Trying alternative path:', altPath);
      
      try {
        const altCreated = await ensureDirectoryExists(altPath);
        if (altCreated) {
          console.log('✅ Alternative path works:', altPath);
          return { success: true, path: altPath };
        }
      } catch (altError) {
        console.log('❌ Alternative path failed:', altPath, altError);
      }
    }
    
    // Last resort: create in current directory
    const lastResort = './downloads';
    console.log('🆘 Trying last resort path:', lastResort);
    
    const lastResortCreated = await ensureDirectoryExists(lastResort);
    if (lastResortCreated) {
      return { success: true, path: lastResort };
    }
    
    return { 
      success: false, 
      path, 
      error: 'All download folder options failed' 
    };
    
  } catch (error) {
    console.error('❌ Fix download folder failed:', error);
    return { 
      success: false, 
      path, 
      error: `Fix failed: ${error}` 
    };
  }
};

/**
 * Test if a path exists (cross-platform)
 */
export const testPathExists = async (path: string): Promise<boolean> => {
  if (typeof window === 'undefined' || !window.__TAURI__) {
    // Web environment - assume it exists
    return true;
  }
  
  try {
    const result = await invoke<boolean>('path_exists', { path });
    console.log(`📂 Path exists check: ${path} = ${result}`);
    return result;
  } catch (error) {
    console.error('❌ Error checking if path exists:', error);
    return false;
  }
};

/**
 * Get an emergency download path when everything else fails
 */
export const getEmergencyDownloadPath = async (): Promise<string> => {
  const emergencyPaths = [
    './downloads',
    './FlowDownload',
    '~/Downloads',
    '~/Desktop/Downloads'
  ];
  
  for (const path of emergencyPaths) {
    try {
      const canCreate = await ensureDirectoryExists(path);
      if (canCreate) {
        console.log('🆘 Emergency path works:', path);
        return path;
      }
    } catch (error) {
      console.log('❌ Emergency path failed:', path, error);
    }
  }
  
  // Ultimate fallback
  return './downloads';
};

/**
 * Interactive download folder setup
 */
export const setupDownloadFolder = async (): Promise<string> => {
  console.log('🚀 Starting interactive download folder setup...');
  
  // Run diagnostic first
  const diagnostic = await diagnoseDownloadFolder();
  
  if (diagnostic.status === 'success') {
    console.log('✅ Download folder is already working:', diagnostic.path);
    return diagnostic.path;
  }
  
  console.log('⚠️ Download folder needs setup:', diagnostic.message);
  
  // Try to fix automatically
  if (diagnostic.suggestions && diagnostic.suggestions.length > 0) {
    for (const suggestion of diagnostic.suggestions) {
      console.log('🔄 Trying suggested path:', suggestion);
      
      try {
        const isValid = await validateDownloadPath(suggestion);
        if (isValid) {
          console.log('✅ Suggested path works:', suggestion);
          return suggestion;
        }
      } catch (error) {
        console.log('❌ Suggested path failed:', suggestion, error);
      }
    }
  }
  
  // If all else fails, use emergency path
  const emergencyPath = await getEmergencyDownloadPath();
  console.log('🆘 Using emergency download path:', emergencyPath);
  return emergencyPath;
};

/**
 * Test download folder functionality end-to-end
 */
export const testDownloadFunctionality = async (): Promise<{
  success: boolean;
  path: string;
  details: string[];
}> => {
  const details: string[] = [];
  
  try {
    details.push('🔍 Starting download functionality test...');
    
    // Step 1: Setup download folder
    const downloadPath = await setupDownloadFolder();
    details.push(`📁 Download path: ${downloadPath}`);
    
    // Step 2: Test write access
    const canWrite = await testDirectoryAccess(downloadPath);
    details.push(`✍️ Write access: ${canWrite ? 'YES' : 'NO'}`);
    
    if (!canWrite) {
      details.push('❌ Cannot write to download folder');
      return { success: false, path: downloadPath, details };
    }
    
    // Step 3: Test folder opening
    try {
      const canOpen = await openFolderInExplorer(downloadPath);
      details.push(`📂 Can open folder: ${canOpen ? 'YES' : 'NO'}`);
    } catch (error) {
      details.push(`⚠️ Folder opening test failed: ${error}`);
    }
    
    details.push('✅ Download functionality test completed successfully');
    return { success: true, path: downloadPath, details };
    
  } catch (error) {
    details.push(`❌ Test failed: ${error}`);
    return { success: false, path: '', details };
  }
};

/**
 * Export diagnostic information for debugging
 */
export const exportDiagnosticInfo = async (): Promise<string> => {
  const info = {
    timestamp: new Date().toISOString(),
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    isTauri: !!window.__TAURI__,
    config: getConfig(),
    diagnostic: await diagnoseDownloadFolder(),
    test: await testDownloadFunctionality()
  };
  
  return JSON.stringify(info, null, 2);
};
