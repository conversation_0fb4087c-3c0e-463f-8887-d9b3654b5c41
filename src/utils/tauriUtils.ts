/**
 * Utility functions for safe Tauri API handling
 */

export interface TauriAPIs {
  invoke: any;
  open: any;
}

/**
 * Comprehensive Tauri environment detection with enhanced validation
 */
export const isInTauriEnvironment = (): boolean => {
  try {
    // Check if we're in a browser environment first
    if (typeof window === 'undefined') {
      return false;
    }
    
    // Multiple checks to ensure we're in Tauri
    const hasTauriObject = window.__TAURI__ !== undefined;
    const hasTauriInvokeApi = (window as any).__TAURI_INVOKE__ !== undefined;
    const hasTauriMetadata = window.__TAURI_METADATA__ !== undefined;
    const hasTauriInternals = (window as any).__TAURI_INTERNALS__ !== undefined;
    
    // Check for Tauri-specific user agent patterns
    const userAgent = window.navigator.userAgent || '';
    const hasTauriUserAgent = userAgent.includes('Tauri') || userAgent.includes('tauri');
    
    // At least one primary indicator must be present
    const isInTauri = hasTauriObject || hasTauriInvokeApi || hasTauriInternals;
    
    console.log('🔍 Enhanced Tauri environment check:', {
      hasTauriObject,
      hasTauriInvokeApi,
      hasTauriMetadata,
      hasTauriInternals,
      hasTauriUserAgent,
      isInTauri,
      windowDefined: typeof window !== 'undefined',
      userAgent: userAgent.substring(0, 100) // Truncate for logging
    });
    
    return isInTauri;
  } catch (error) {
    console.warn('Error during Tauri environment detection:', error);
    return false;
  }
};

/**
 * Safely import and validate Tauri APIs with proper error handling and retries
 */
export const importTauriAPIs = async (retryCount: number = 2): Promise<TauriAPIs> => {
  console.log('🚀 Attempting to import Tauri APIs... (retry count:', retryCount, ')');
  
  if (!isInTauriEnvironment()) {
    throw new Error('Not running in Tauri environment');
  }

  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      console.log(`📦 Importing Tauri modules... (attempt ${attempt + 1}/${retryCount + 1})`);
      
      // Add a small delay for retries to allow for module loading
      if (attempt > 0) {
        await new Promise(resolve => setTimeout(resolve, 100 * attempt));
      }
      
      // Try importing modules sequentially for better error handling
      let shellModule: any;
      let coreModule: any;
      
      try {
        console.log('📦 Importing shell module...');
        shellModule = await import('@tauri-apps/plugin-shell');
        console.log('✅ Shell module imported successfully');
      } catch (shellError) {
        console.error('❌ Failed to import shell module:', shellError);
        throw new Error(`Shell module not available: ${shellError instanceof Error ? shellError.message : 'Unknown error'}`);
      }
      
      try {
        console.log('📦 Importing core module...');
        coreModule = await import('@tauri-apps/api/core');
        console.log('✅ Core module imported successfully');
      } catch (coreError) {
        console.error('❌ Failed to import core module:', coreError);
        throw new Error(`Core module not available: ${coreError instanceof Error ? coreError.message : 'Unknown error'}`);
      }

      // Validate that the APIs are available
      if (!shellModule || typeof shellModule.open !== 'function') {
        console.error('❌ Shell.open API not available or not a function');
        throw new Error('Shell.open API not available');
      }

      if (!coreModule || typeof coreModule.invoke !== 'function') {
        console.error('❌ Core.invoke API not available or not a function');
        throw new Error('Core.invoke API not available');
      }

      console.log('✅ All Tauri APIs successfully imported and validated');
      return {
        invoke: coreModule.invoke,
        open: shellModule.open
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Attempt ${attempt + 1} failed:`, errorMessage);
      
      // If this was the last attempt, throw the error
      if (attempt === retryCount) {
        throw new Error(`Desktop features not available after ${retryCount + 1} attempts: ${errorMessage}`);
      }
      
      // Otherwise, continue to next attempt
      console.log(`⏳ Retrying import in ${100 * (attempt + 1)}ms...`);
    }
  }
  
  // This should never be reached, but TypeScript requires it
  throw new Error('Unexpected error during Tauri API import');
};

/**
 * Execute a function that requires Tauri APIs with proper error handling
 */
export const withTauriAPIs = async <T>(
  callback: (apis: TauriAPIs) => Promise<T> | T,
  fallback?: () => T | Promise<T>
): Promise<T> => {
  try {
    const apis = await importTauriAPIs();
    return await callback(apis);
  } catch (error) {
    console.error('Tauri API error:', error);
    if (fallback) {
      return await fallback();
    }
    throw error;
  }
};

/**
 * Check if a directory exists and is accessible, with automatic creation if needed
 */
export const ensureFolderAccessible = async (path: string, invoke: any): Promise<boolean> => {
  try {
    // First check if it's accessible
    const isAccessible = await invoke('test_directory_access', { path });
    if (isAccessible) {
      return true;
    }

    // Try to create it if not accessible
    console.log('📁 Folder not accessible, trying to create:', path);
    const created = await invoke('ensure_directory_exists', { path });
    
    if (created) {
      console.log('✅ Folder created successfully');
      return true;
    } else {
      console.error('❌ Could not create folder:', path);
      return false;
    }
  } catch (error) {
    console.warn('Could not verify folder accessibility:', error);
    return false;
  }
};

/**
 * Get downloads folder path with enhanced fallback strategy
 */
export const getDownloadsPath = async (invoke: any): Promise<string> => {
  // Try enhanced command first
  try {
    const path = await invoke('get_downloads_folder');
    console.log('✅ Got enhanced downloads path:', path);
    return path;
  } catch (enhancedError) {
    console.warn('Enhanced downloads folder command failed, trying fallback:', enhancedError);
    
    // Try fallback command
    try {
      const path = await invoke('get_download_dir');
      console.log('✅ Got fallback downloads path:', path);
      return path;
    } catch (fallbackError) {
      console.error('Both downloads folder commands failed:', fallbackError);
      throw new Error('Unable to determine downloads folder location');
    }
  }
};