import { describe, it, expect, beforeEach, beforeAll, vi } from 'vitest';

describe('folderUtils', () => {
  let formatPathForDisplay: (path: string, maxLength?: number) => string;

  beforeAll(async () => {
    // Import the actual function without mocking
    const actual = await vi.importActual('./folderUtils') as Record<string, unknown>;
    formatPathForDisplay = actual.formatPathForDisplay as (path: string, maxLength?: number) => string;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('formatPathForDisplay', () => {
    it('should format long paths with ellipsis', () => {
      const longPath = '/Users/<USER>/Documents/Very/Long/Path/To/Downloads/Folder';
      const result = formatPathForDisplay(longPath, 20);
      
      expect(result.length).toBeLessThanOrEqual(20);
      expect(result).toContain('...');
    });

    it('should return short paths unchanged', () => {
      const shortPath = '/Users/<USER>';
      const result = formatPathForDisplay(shortPath, 50);
      
      expect(result).toBe(shortPath);
    });

    it('should handle empty path', () => {
      const result = formatPathForDisplay('', 20);
      expect(result).toBe('');
    });

    it('should use default max length', () => {
      const path = '/a/very/long/path/that/should/be/truncated/because/it/exceeds/default/length';
      const result = formatPathForDisplay(path);
      
      expect(result.length).toBeLessThanOrEqual(50); // Default max length
    });
  });
});
