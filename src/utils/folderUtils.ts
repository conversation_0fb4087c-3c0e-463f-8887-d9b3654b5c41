// Enhanced folder utilities with comprehensive error handling
// Note: No direct imports of Tauri APIs to avoid import errors in web environment

import { isInTauriEnvironment, withTauriAPIs } from './tauriUtils';

// Check if we're in a Tauri environment (use centralized detection)
export const isInTauri = (): boolean => {
  return isInTauriEnvironment();
};

/**
 * Get the default downloads directory with comprehensive fallback strategy
 */
export const getDefaultDownloadsPath = async (): Promise<string> => {
  if (!isInTauri()) {
    // Web/development fallback
    const userAgent = window.navigator.userAgent.toLowerCase();
    if (userAgent.includes('mac')) {
      return '~/Downloads';
    } else if (userAgent.includes('win')) {
      return 'Downloads';
    } else {
      return '~/Downloads';
    }
  }

  try {
    // Use the robust Tauri API wrapper
    return await withTauriAPIs(
      async (apis) => {
        // Use the new comprehensive downloads folder command
        const downloadsPath = await apis.invoke('get_downloads_folder') as string;
        console.log('✅ Downloads folder resolved:', downloadsPath);
        return downloadsPath;
      },
      async () => {
        // Fallback: try the old method
        return await withTauriAPIs(
          async (apis) => {
            const fallbackPath = await apis.invoke('get_download_dir') as string;
            console.log('✅ Using fallback downloads path:', fallbackPath);
            return fallbackPath;
          },
          async () => {
            // Last resort: use a reasonable default
            const defaultPath = await getLastResortPath();
            console.log('⚠️ Using last resort path:', defaultPath);
            return defaultPath;
          }
        );
      }
    );
  } catch (error) {
    console.error('❌ All download folder attempts failed:', error);
    const defaultPath = await getLastResortPath();
    console.log('⚠️ Using emergency fallback path:', defaultPath);
    return defaultPath;
  }
};

/**
 * Ensure a directory exists and is writable
 */
export const ensureDirectoryExists = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    console.log('Web environment: assuming directory exists');
    return true;
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    const result = await invoke<boolean>('ensure_directory_exists', { path });
    if (result) {
      console.log('✅ Directory ensured:', path);
    } else {
      console.warn('⚠️ Directory not accessible:', path);
    }
    return result;
  } catch (error) {
    console.error('❌ Failed to ensure directory exists:', error);
    return false;
  }
};

/**
 * Test if a directory is accessible and writable
 */
export const testDirectoryAccess = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    return true;
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    const result = await invoke<boolean>('test_directory_access', { path });
    console.log(result ? '✅ Directory accessible:' : '❌ Directory not accessible:', path);
    return result;
  } catch (error) {
    console.error('❌ Error testing directory access:', error);
    return false;
  }
};

/**
 * Open a folder in the system file explorer
 */
export const openFolderInExplorer = async (path: string): Promise<boolean> => {
  if (!isInTauri()) {
    console.warn('Cannot open folder in web environment');
    return false;
  }

  try {
    // First check if the folder exists and is accessible
    const isAccessible = await testDirectoryAccess(path);
    if (!isAccessible) {
      // Try to ensure the directory exists
      const created = await ensureDirectoryExists(path);
      if (!created) {
        console.error('❌ Folder is not accessible and could not be created:', path);
        throw new Error(`Folder is not accessible and could not be created: ${path}`);
      }
    }
    
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    await invoke('open_folder_in_explorer', { path });
    console.log('✅ Opened folder in explorer:', path);
    return true;
  } catch (error) {
    console.error('❌ Failed to open folder:', error);
    throw error; // Re-throw to let caller handle the error message
  }
};

/**
 * Resolve a path (handle ~ expansion, relative paths, etc.)
 */
export const resolvePath = async (path: string): Promise<string> => {
  if (!isInTauri()) {
    // Simple web fallback
    return path.replace(/^~\//, '/Users/');
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    const resolvedPath = await invoke<string>('resolve_path', { path });
    console.log('✅ Path resolved:', path, '->', resolvedPath);
    return resolvedPath;
  } catch (error) {
    console.error('❌ Failed to resolve path:', error);
    return path;
  }
};

/**
 * Get a reasonable last resort path when all else fails
 */
const getLastResortPath = async (): Promise<string> => {
  try {
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    // Try to get home directory and create Downloads there
    const home = await invoke<string>('get_home_dir');
    const downloadsPath = `${home}/Downloads`;
    
    // Try to ensure it exists
    await ensureDirectoryExists(downloadsPath);
    return downloadsPath;
  } catch (error) {
    console.error('❌ Even last resort failed:', error);
    
    // Ultimate fallback - use current directory
    return './downloads';
  }
};

/**
 * Format a path for display (shorten long paths)
 */
export const formatPathForDisplay = (path: string, maxLength: number = 50): string => {
  if (path.length <= maxLength) {
    return path;
  }
  
  // Calculate optimal lengths for start and end, reserving 3 chars for "..."
  const availableLength = maxLength - 3; // Reserve 3 for "..."
  const startLength = Math.floor(availableLength / 2);
  const endLength = availableLength - startLength;
  
  // Show start and end of path
  const start = path.substring(0, startLength);
  const end = path.substring(path.length - endLength);
  return `${start}...${end}`;
};

/**
 * Validate that a path is safe and reasonable for downloads
 */
export const validateDownloadPath = async (path: string): Promise<boolean> => {
  if (!path || path.trim().length === 0) {
    return false;
  }

  // Check for obviously bad paths
  const badPaths = ['/System', '/Windows', '/usr', '/bin', '/sbin'];
  const normalizedPath = path.toLowerCase();
  
  for (const badPath of badPaths) {
    if (normalizedPath.startsWith(badPath.toLowerCase())) {
      console.warn('⚠️ Rejecting system path:', path);
      return false;
    }
  }

  // Test if the path is accessible
  try {
    const resolvedPath = await resolvePath(path);
    return await testDirectoryAccess(resolvedPath);
  } catch (error) {
    console.error('❌ Path validation failed:', error);
    return false;
  }
};

/**
 * Get suggested download paths for the user to choose from
 */
export const getSuggestedDownloadPaths = async (): Promise<string[]> => {
  const suggestions: string[] = [];
  
  if (!isInTauri()) {
    return ['Downloads', '~/Downloads'];
  }

  try {
    const { invoke } = await import('@tauri-apps/api/core');
    if (!invoke) {
      throw new Error('Tauri invoke function not available');
    }
    
    // Primary suggestion: system downloads folder
    try {
      const downloadsPath = await invoke<string>('get_downloads_folder');
      suggestions.push(downloadsPath);
    } catch (e) {
      console.log('Primary downloads folder not available');
    }

    // Secondary suggestions
    try {
      const home = await invoke<string>('get_home_dir');
      suggestions.push(`${home}/Downloads`);
      suggestions.push(`${home}/Desktop/FlowDownload`);
      suggestions.push(`${home}/Documents/FlowDownload`);
    } catch (e) {
      console.log('Home directory not available');
    }

    // Remove duplicates and test accessibility
    const uniqueSuggestions = [...new Set(suggestions)];
    const accessibleSuggestions: string[] = [];

    for (const suggestion of uniqueSuggestions) {
      try {
        if (await testDirectoryAccess(suggestion)) {
          accessibleSuggestions.push(suggestion);
        }
      } catch (e) {
        console.log('Suggestion not accessible:', suggestion);
      }
    }

    return accessibleSuggestions.length > 0 ? accessibleSuggestions : ['./downloads'];
  } catch (error) {
    console.error('❌ Failed to get suggested paths:', error);
    return ['./downloads'];
  }
};

/**
 * Select a download folder using the system file dialog
 */
export const selectDownloadFolder = async (): Promise<string | null> => {
  if (!isInTauri()) {
    console.warn('Folder selection not available in web environment');
    return null;
  }

  try {
    const { open } = await import('@tauri-apps/plugin-dialog');
    const result = await open({
      directory: true,
      multiple: false,
      title: 'Select Download Folder'
    });

    if (result && typeof result === 'string') {
      // Test if the selected folder is accessible
      const isAccessible = await testDirectoryAccess(result);
      if (isAccessible) {
        console.log('✅ Selected accessible folder:', result);
        return result;
      } else {
        console.warn('⚠️ Selected folder is not accessible:', result);
        return null;
      }
    }

    return null;
  } catch (error) {
    console.error('❌ Failed to select folder:', error);
    return null;
  }
};

// Legacy function names for backward compatibility
export const createDirectory = ensureDirectoryExists;
export const checkDirectoryAccess = testDirectoryAccess;