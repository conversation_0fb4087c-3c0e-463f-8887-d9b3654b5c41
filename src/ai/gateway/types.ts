/**
 * Types for Unified AI Gateway
 */

export type AICapability = 
  | 'image_generation'
  | 'text_to_video'
  | 'image_to_video'
  | 'video_generation'
  | 'realtime_transcription'
  | 'voice_synthesis'
  | 'live_translation'
  | 'custom_models'
  | 'style_transfer'
  | 'thumbnail_creation'
  | 'image_enhancement'
  | 'motion_generation'
  | 'character_animation'
  | 'concept_visualization'
  | 'scene_generation'
  | 'realtime_processing';

export type GenerationType = 
  | 'text_to_image'
  | 'text_to_video'
  | 'image_to_video'
  | 'image_to_image'
  | 'thumbnail'
  | 'realtime_transcription'
  | 'voice_synthesis'
  | 'custom_model';

export interface GenerationOptions {
  type: GenerationType;
  quality?: 'low' | 'medium' | 'high';
  style?: string;
  width?: number;
  height?: number;
  duration?: number; // For video
  fps?: number; // For video
  voice?: string; // For voice synthesis
  language?: string; // For translation
  model?: string; // Specific model override
  seed?: number; // For reproducibility
  realtime?: boolean;
  streaming?: boolean;
  format?: 'png' | 'jpg' | 'webp' | 'mp4' | 'webm' | 'wav' | 'mp3';
  negativePrompt?: string; // For image generation
  guidanceScale?: number; // For diffusion models
  numInferenceSteps?: number; // For diffusion models
  temperature?: number; // For text models
  maxTokens?: number; // For text models
}

export interface GenerationResult {
  success: boolean;
  data?: any; // The generated content (buffer, URL, or stream)
  url?: string; // Direct URL to result
  localPath?: string; // Local file path
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    format?: string;
    model?: string;
    seed?: number;
    status?: string;
    fps?: number;
    language?: string;
    voice?: string;
    partial?: boolean;
    provider?: string;
    tokensUsed?: number;
  };
  cost?: number; // Cost in USD
  tokensUsed?: number;
  processingTime?: number; // in milliseconds
  error?: string;
}

export interface AIProvider {
  name: string;
  
  /**
   * Initialize the provider with credentials
   */
  initialize(config: any): Promise<void>;
  
  /**
   * Generate content based on prompt and options
   */
  generate(prompt: string, options: GenerationOptions): Promise<GenerationResult>;
  
  /**
   * Test connection to the provider
   */
  testConnection(): Promise<void>;
  
  /**
   * Get provider speed rating (0-10)
   */
  getSpeed(): number;
  
  /**
   * Get provider quality rating (0-10)
   */
  getQuality(): number;
  
  /**
   * Get approximate cost per request
   */
  getCost(): number;
  
  /**
   * Check if provider supports a specific capability
   */
  supportsCapability(capability: AICapability): boolean;
  
  /**
   * Get available models
   */
  getAvailableModels(): Promise<ModelInfo[]>;
  
  /**
   * Stream generation results
   */
  generateStream?(
    prompt: string, 
    options: GenerationOptions,
    onProgress: (partial: Partial<GenerationResult>) => void
  ): Promise<GenerationResult>;
}

export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  type: GenerationType[];
  maxResolution?: { width: number; height: number };
  maxDuration?: number; // For video models
  costPerRequest?: number;
  speed?: 'slow' | 'medium' | 'fast';
  quality?: 'low' | 'medium' | 'high';
}

export interface AIProviderCredentials {
  openai?: {
    apiKey: string;
    realtimeKey?: string;
    organization?: string;
  };
  replicate?: {
    apiToken: string;
  };
  flux?: {
    apiKey: string;
  };
  bytedance?: {
    accessKey: string;
    secretKey: string;
  };
  google?: {
    projectId: string;
    apiKey: string;
  };
}

export interface UsageStats {
  requests: number;
  tokens: number;
  cost: number;
  errors?: number;
  averageLatency?: number;
}

export interface ProviderConfig {
  enabled: boolean;
  priority: number;
  maxRequestsPerMinute?: number;
  maxCostPerMonth?: number;
  preferredModels?: string[];
}