/**
 * Unified AI Gateway - Central hub for all AI operations
 * Routes requests to the optimal provider based on task type and availability
 */

import { AIProvider, GenerationOptions, GenerationResult, AICapability } from './types';
import { OpenAIRealtimeProvider } from '../providers/OpenAIRealtimeProvider';
import { FluxProvider } from '../providers/FluxProvider';
import { ReplicateProvider } from '../providers/ReplicateProvider';
import { ByteDanceProvider } from '../providers/ByteDanceProvider';
import { GoogleImagenProvider } from '../providers/GoogleImagenProvider';
import { AIProviderFactory } from '../AIProviderFactory';
import { Store } from '@tauri-apps/plugin-store';

// Provider capability mapping
const PROVIDER_CAPABILITIES: Record<string, AICapability[]> = {
  openai_realtime: ['realtime_transcription', 'voice_synthesis', 'live_translation'],
  flux: ['image_generation', 'thumbnail_creation', 'style_transfer'],
  replicate: ['custom_models', 'video_generation', 'image_enhancement'],
  bytedance: ['image_to_video', 'motion_generation', 'character_animation'],
  google_imagen: ['text_to_video', 'concept_visualization', 'scene_generation']
};

export class UnifiedAIGateway {
  private providers: Map<string, AIProvider> = new Map();
  private providerStatus: Map<string, boolean> = new Map();
  private store: Store | null = null;
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    // Initialize store for API key management
    this.store = await Store.load('ai-gateway-config.json');

    // Load API credentials
    const credentials = await this.loadCredentials();
    
    // For testing: Check environment variables as fallback
    const envCredentials: any = {};
    if (process.env.VITE_REPLICATE_API_TOKEN || import.meta.env?.VITE_REPLICATE_API_TOKEN) {
      envCredentials.replicate = { 
        apiToken: process.env.VITE_REPLICATE_API_TOKEN || import.meta.env?.VITE_REPLICATE_API_TOKEN 
      };
    }
    
    const finalCredentials = { ...envCredentials, ...credentials };
    
    console.log('AI Gateway: Loading credentials...', {
      hasCredentials: Object.keys(finalCredentials).length > 0,
      providers: Object.keys(finalCredentials)
    });

    // Initialize providers based on available credentials
    if (finalCredentials.openai?.realtimeKey) {
      this.providers.set('openai_realtime', new OpenAIRealtimeProvider(finalCredentials.openai));
      this.providerStatus.set('openai_realtime', true);
    }

    if (finalCredentials.flux?.apiKey) {
      this.providers.set('flux', new FluxProvider(finalCredentials.flux));
      this.providerStatus.set('flux', true);
    }

    if (finalCredentials.replicate?.apiToken) {
      this.providers.set('replicate', new ReplicateProvider(finalCredentials.replicate));
      this.providerStatus.set('replicate', true);
    }

    if (finalCredentials.bytedance?.accessKey) {
      this.providers.set('bytedance', new ByteDanceProvider(finalCredentials.bytedance));
      this.providerStatus.set('bytedance', true);
    }

    if (finalCredentials.google?.apiKey) {
      this.providers.set('google_imagen', new GoogleImagenProvider(finalCredentials.google));
      this.providerStatus.set('google_imagen', true);
    }

    // Also initialize existing providers from AIProviderFactory
    try {
      const existingProvider = AIProviderFactory.getProvider('groq');
      if (existingProvider) {
        // Wrap the existing provider to match our interface
        const wrappedProvider = this.wrapLegacyProvider(existingProvider);
        this.providers.set('legacy', wrappedProvider);
        this.providerStatus.set('legacy', true);
      }
    } catch (error) {
      console.warn('Failed to initialize legacy provider:', error);
    }

    this.initialized = true;
    
    console.log('AI Gateway initialized:', {
      availableProviders: Array.from(this.providers.keys()),
      providerStatus: Array.from(this.providerStatus.entries())
    });
  }

  /**
   * Generate content using the optimal provider
   */
  async generateContent(
    prompt: string, 
    options: GenerationOptions
  ): Promise<GenerationResult> {
    await this.initialize();

    const provider = await this.selectOptimalProvider(options);
    if (!provider) {
      // Provide specific guidance based on the requested type
      let errorMessage = 'No AI provider available. ';
      
      if (options.type === 'text_to_video') {
        errorMessage += 'Text-to-video requires a Google Imagen or Replicate API key. Please go to Settings → AI Provider Settings to configure.';
      } else if (options.type === 'image_to_video') {
        errorMessage += 'Image-to-video requires a ByteDance API key. Please go to Settings → AI Provider Settings to configure.';
      } else if (options.type === 'text_to_image' || options.type === 'thumbnail') {
        errorMessage += 'Image generation requires a Flux or Replicate API key. Please go to Settings → AI Provider Settings to configure.';
      } else {
        errorMessage += 'Please configure API keys in Settings → AI Provider Settings.';
      }
      
      throw new Error(errorMessage);
    }

    try {
      const result = await provider.generate(prompt, options);
      
      // Track usage for analytics
      await this.trackUsage(provider.name, options.type, result);
      
      return result;
    } catch (error) {
      // Try fallback provider if available
      const fallbackProvider = await this.getFallbackProvider(provider.name, options);
      if (fallbackProvider) {
        console.warn(`Primary provider failed, falling back to ${fallbackProvider.name}`);
        return fallbackProvider.generate(prompt, options);
      }
      throw error;
    }
  }

  /**
   * Select the optimal provider based on task requirements
   */
  private async selectOptimalProvider(
    options: GenerationOptions
  ): Promise<AIProvider | null> {
    const requiredCapabilities = this.getRequiredCapabilities(options);
    
    // Find providers that support all required capabilities
    const capableProviders: AIProvider[] = [];
    
    for (const [providerName, capabilities] of Object.entries(PROVIDER_CAPABILITIES)) {
      if (requiredCapabilities.every(cap => capabilities.includes(cap))) {
        const provider = this.providers.get(providerName);
        if (provider && this.providerStatus.get(providerName)) {
          capableProviders.push(provider);
        }
      }
    }

    if (capableProviders.length === 0) {
      return null;
    }

    // Sort by priority: cost, speed, quality
    return this.rankProviders(capableProviders, options)[0];
  }

  /**
   * Get required capabilities based on generation options
   */
  private getRequiredCapabilities(options: GenerationOptions): AICapability[] {
    const capabilities: AICapability[] = [];

    switch (options.type) {
      case 'text_to_image':
      case 'thumbnail':
        capabilities.push('image_generation');
        break;
      case 'text_to_video':
        capabilities.push('text_to_video');
        break;
      case 'image_to_video':
        capabilities.push('image_to_video');
        break;
      case 'realtime_transcription':
        capabilities.push('realtime_transcription');
        break;
      case 'voice_synthesis':
        capabilities.push('voice_synthesis');
        break;
      case 'custom_model':
        capabilities.push('custom_models');
        break;
    }

    if (options.realtime) {
      capabilities.push('realtime_processing');
    }

    return capabilities;
  }

  /**
   * Rank providers based on various factors
   */
  private rankProviders(
    providers: AIProvider[], 
    options: GenerationOptions
  ): AIProvider[] {
    return providers.sort((a, b) => {
      // Priority factors
      let scoreA = 0;
      let scoreB = 0;

      // Speed priority for realtime tasks
      if (options.realtime) {
        scoreA += a.getSpeed() * 3;
        scoreB += b.getSpeed() * 3;
      }

      // Quality priority for final renders
      if (options.quality === 'high') {
        scoreA += a.getQuality() * 2;
        scoreB += b.getQuality() * 2;
      }

      // Cost consideration
      scoreA -= a.getCost() * 0.5;
      scoreB -= b.getCost() * 0.5;

      return scoreB - scoreA;
    });
  }

  /**
   * Get fallback provider if primary fails
   */
  private async getFallbackProvider(
    failedProvider: string,
    options: GenerationOptions
  ): Promise<AIProvider | null> {
    const providers = await this.selectOptimalProvider(options);
    return providers?.name !== failedProvider ? providers : null;
  }

  /**
   * Wrap legacy provider to match AIProvider interface
   */
  private wrapLegacyProvider(legacyProvider: any): AIProvider {
    return {
      name: 'legacy',
      initialize: async () => {
        // Legacy provider doesn't need initialization
      },
      generate: async (prompt: string, options: GenerationOptions) => {
        try {
          const response = await legacyProvider.generateText({
            prompt,
            maxTokens: options.maxTokens || 1000,
            temperature: options.temperature || 0.7
          });
          
          return {
            success: true,
            data: new TextEncoder().encode(response.text),
            metadata: {
              provider: 'legacy',
              model: response.model,
              tokensUsed: response.usage?.totalTokens
            },
            cost: response.usage?.totalTokens ? response.usage.totalTokens * 0.00001 : 0
          };
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Legacy provider error'
          };
        }
      },
      testConnection: async () => {
        try {
          await legacyProvider.generateText({ prompt: 'test', maxTokens: 1 });
        } catch (error) {
          throw new Error('Legacy provider connection failed');
        }
      },
      getSpeed: () => 5,
      getQuality: () => 7,
      getCost: () => 3,
      supportsCapability: (_capability: AICapability) => {
        return false; // Legacy provider doesn't support new generation capabilities
      },
      getAvailableModels: async () => {
        return [{
          id: 'legacy-model',
          name: 'Legacy Text Model',
          type: [] // Legacy provider doesn't support new generation types
        }];
      }
    };
  }

  /**
   * Load API credentials from secure storage
   */
  private async loadCredentials(): Promise<any> {
    if (!this.store) return {};
    
    const stored = await this.store.get('api_credentials');
    return stored || {};
  }

  /**
   * Save API credentials securely
   */
  async saveCredentials(provider: string, credentials: any): Promise<void> {
    if (!this.store) return;

    const current = await this.loadCredentials();
    current[provider] = credentials;
    
    await this.store.set('api_credentials', current);
    await this.store.save();

    // Reinitialize to load new provider
    this.initialized = false;
    await this.initialize();
  }

  /**
   * Track usage for analytics and billing
   */
  private async trackUsage(
    provider: string, 
    _type: string, 
    result: GenerationResult
  ): Promise<void> {
    if (!this.store) return;

    const usage: Record<string, Record<string, any>> = await this.store.get('usage_stats') || {};
    const month = new Date().toISOString().substring(0, 7);
    
    if (!usage[month]) {
      usage[month] = {};
    }
    
    if (!usage[month][provider]) {
      usage[month][provider] = {
        requests: 0,
        tokens: 0,
        cost: 0
      };
    }

    usage[month][provider].requests++;
    usage[month][provider].tokens += result.tokensUsed || 0;
    usage[month][provider].cost += result.cost || 0;

    await this.store.set('usage_stats', usage);
    await this.store.save();
  }

  /**
   * Get available providers and their status
   */
  getProviderStatus(): Map<string, boolean> {
    return new Map(this.providerStatus);
  }

  /**
   * Test provider connectivity
   */
  async testProvider(providerName: string): Promise<boolean> {
    const provider = this.providers.get(providerName);
    if (!provider) return false;

    try {
      await provider.testConnection();
      this.providerStatus.set(providerName, true);
      return true;
    } catch (error) {
      this.providerStatus.set(providerName, false);
      return false;
    }
  }

  /**
   * Get usage statistics
   */
  async getUsageStats(month?: string): Promise<any> {
    if (!this.store) return {};

    const usage: Record<string, any> = await this.store.get('usage_stats') || {};
    if (month) {
      return usage[month] || {};
    }
    return usage;
  }

  /**
   * Get user-friendly error message
   */
  public getErrorMessage(error: any): string {
    if (error instanceof Error) {
      // Check for common API errors
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        return 'Invalid API key. Please check your provider settings.';
      }
      if (error.message.includes('429') || error.message.includes('rate limit')) {
        return 'Rate limit exceeded. Please try again later.';
      }
      if (error.message.includes('402') || error.message.includes('payment')) {
        return 'Payment required. Please check your provider account.';
      }
      if (error.message.includes('network') || error.message.includes('fetch')) {
        return 'Network error. Please check your internet connection.';
      }
      return error.message;
    }
    return 'An unexpected error occurred';
  }
}

// Singleton instance
export const unifiedAIGateway = new UnifiedAIGateway();