/**
 * Centralized configuration for AI providers
 * This file contains all the hardcoded values that were previously scattered across provider files
 */

export interface ProviderConfig {
  baseUrl: string;
  models: Record<string, ModelConfig>;
  defaults: Record<string, any>;
  costs: Record<string, number>;
  quality: number;
  speed: number;
  capabilities: string[];
}

export interface ModelConfig {
  id: string;
  name: string;
  endpoint?: string;
  defaults?: Record<string, any>;
  cost?: number;
}

export const providerConfigs: Record<string, ProviderConfig> = {
  flux: {
    baseUrl: process.env.FLUX_API_URL || 'https://api.bfl.ml',
    models: {
      schnell: {
        id: 'flux-schnell',
        name: 'Flux Schnell (Fast)',
        endpoint: '/v1/flux-schnell',
        defaults: { steps: 4 },
        cost: 0.003
      },
      dev: {
        id: 'flux-dev',
        name: 'Flux Dev (Balanced)',
        endpoint: '/v1/flux-dev',
        defaults: { steps: 50 },
        cost: 0.025
      },
      pro: {
        id: 'flux-pro-1.1',
        name: 'Flux Pro (Quality)',
        endpoint: '/v1/flux-pro-1.1',
        defaults: { steps: 50 },
        cost: 0.05
      }
    },
    defaults: {
      width: 1024,
      height: 1024,
      guidanceScale: 3.5,
      safetyTolerance: 2,
      promptUpsampling: true
    },
    costs: {
      base: 0.003,
      upscaleMultiplier: 1.5
    },
    quality: 7,
    speed: 10,
    capabilities: ['image_generation', 'thumbnail_creation', 'style_transfer']
  },

  replicate: {
    baseUrl: process.env.REPLICATE_API_URL || 'https://api.replicate.com',
    models: {
      sdxl: {
        id: 'stability-ai/sdxl:39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b',
        name: 'Stable Diffusion XL',
        cost: 0.0023
      },
      animateDiff: {
        id: 'lucataco/animate-diff:1531004ee4c98894ab11f8a4ce6206099e732c1da15121987a8eef54828f0663',
        name: 'Animate Diff',
        cost: 0.025
      },
      zeroscope: {
        id: 'anotherjesse/zeroscope-v2-xl:9f747673945c62801b13b84701c783929c0ee784e4748ec062204894dda1a351',
        name: 'Zeroscope V2 XL',
        cost: 0.05
      },
      realEsrgan: {
        id: 'nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc3a73abf41610695738c1d7b',
        name: 'Real-ESRGAN',
        cost: 0.001
      }
    },
    defaults: {
      width: 1024,
      height: 576,
      fps: 24,
      duration: 5,
      scale: 4,
      faceEnhance: true
    },
    costs: {
      base: 0.001
    },
    quality: 6,
    speed: 8,
    capabilities: ['custom_models', 'video_generation', 'image_enhancement']
  },

  googleImagen: {
    baseUrl: process.env.GOOGLE_API_URL || 'https://generativelanguage.googleapis.com/v1beta',
    models: {
      imagenVideo: {
        id: 'imagen-video-v2',
        name: 'Imagen Video V2',
        endpoint: '/models/imagen-video:generateVideo',
        cost: 0.08
      },
      imagen3: {
        id: 'imagen-3',
        name: 'Imagen 3',
        endpoint: '/models/imagen-3:predict',
        cost: 0.02
      }
    },
    defaults: {
      duration: 5,
      fps: 24,
      resolution: '1280x768',
      temperature: 0.8,
      guidanceScale: 7.5,
      inferenceSteps: 50
    },
    costs: {
      video720p: 0.08,
      video1080p: 0.12,
      image: 0.02
    },
    quality: 9,
    speed: 4,
    capabilities: ['text_to_video', 'concept_visualization', 'scene_generation']
  },

  openaiRealtime: {
    baseUrl: process.env.OPENAI_API_URL || 'https://api.openai.com/v1',
    models: {
      whisper: {
        id: 'whisper-1',
        name: 'Whisper',
        endpoint: '/audio/transcriptions',
        cost: 0.006 // per minute
      },
      tts: {
        id: 'tts-1-hd',
        name: 'Text-to-Speech HD',
        endpoint: '/audio/speech',
        cost: 0.00003 // per character
      },
      realtime: {
        id: 'gpt-4o-realtime-preview-2024-10-01',
        name: 'GPT-4 Realtime',
        endpoint: 'wss://api.openai.com/v1/realtime',
        cost: 0.06 // per minute
      }
    },
    defaults: {
      voice: 'alloy',
      format: 'mp3',
      websocketTimeout: 30000
    },
    costs: {
      transcription: 0.006,
      tts: 0.00003,
      realtime: 0.06
    },
    quality: 9,
    speed: 9,
    capabilities: ['realtime_transcription', 'voice_synthesis', 'live_translation']
  },

  bytedance: {
    baseUrl: process.env.BYTEDANCE_API_URL || 'https://api.bytedance.com/v1',
    models: {
      magicAnimate: {
        id: 'magicAnimate',
        name: 'Magic Animate',
        cost: 0.1
      },
      animateAnyone: {
        id: 'animateAnyone',
        name: 'Animate Anyone',
        cost: 0.08
      },
      i2vgen: {
        id: 'i2vgen-xl',
        name: 'I2VGen-XL',
        cost: 0.12
      }
    },
    defaults: {
      duration: 5,
      fps: 24,
      width: 512,
      height: 512,
      motionStrength: 1.0,
      pollingInterval: 5000,
      maxPollingAttempts: 60
    },
    costs: {
      basePerSecond: 0.02,
      hdMultiplier: 1.5
    },
    quality: 8,
    speed: 5,
    capabilities: ['image_to_video', 'motion_generation', 'character_animation']
  }
};

// Helper function to get provider config
export function getProviderConfig(provider: string): ProviderConfig | undefined {
  return providerConfigs[provider];
}

// Helper function to get model config
export function getModelConfig(provider: string, modelKey: string): ModelConfig | undefined {
  const config = providerConfigs[provider];
  return config?.models[modelKey];
}

// Helper function to get cost for a specific operation
export function calculateCost(
  provider: string, 
  model: string, 
  options: { duration?: number; resolution?: string; words?: number }
): number {
  const config = getProviderConfig(provider);
  const modelConfig = config?.models[model];
  
  if (!config || !modelConfig) return 0;
  
  let baseCost = modelConfig.cost || 0;
  
  // Apply modifiers based on options
  if (provider === 'bytedance' && options.resolution && parseInt(options.resolution) > 512) {
    baseCost *= config.costs.hdMultiplier || 1;
  }
  
  if (options.duration && (provider === 'bytedance' || provider === 'googleImagen')) {
    baseCost *= options.duration;
  }
  
  if (options.words && provider === 'openaiRealtime') {
    baseCost = options.words * (config.costs.tts || 0.00003);
  }
  
  return baseCost;
}