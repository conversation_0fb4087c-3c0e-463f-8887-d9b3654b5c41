import { invoke } from '@tauri-apps/api/core';
import {
  ContentAnalysis,
  ViralityPrediction,
  EngagementMetrics,
  PlatformOptimization
} from './types';

interface HistoricalPerformance {
  contentId: string;
  platform: string;
  uploadDate: Date;
  metrics: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
    watchTime: number;
    clickThroughRate: number;
  };
  contentFeatures: {
    duration: number;
    hasSubtitles: boolean;
    thumbnailQuality: number;
    titleLength: number;
    descriptionLength: number;
    tags: string[];
  };
}

interface TrendingTopic {
  topic: string;
  platform: string;
  trendScore: number;
  growthRate: number;
  relatedHashtags: string[];
  peakTimes: string[];
  competitionLevel: 'low' | 'medium' | 'high';
}

interface CreatorProfile {
  platforms: {
    [platform: string]: {
      followers: number;
      avgEngagementRate: number;
      contentFrequency: number;
      bestPerformingCategories: string[];
      audienceDemographics?: {
        ageGroups: { [range: string]: number };
        locations: { [country: string]: number };
        interests: string[];
      };
    };
  };
  contentHistory: HistoricalPerformance[];
  growthTrajectory: {
    date: Date;
    totalFollowers: number;
    totalViews: number;
  }[];
}

export class PredictionEngine {
  private static instance: PredictionEngine;
  private creatorProfile: CreatorProfile | null = null;
  private trendingTopics: Map<string, TrendingTopic[]> = new Map();
  private predictionCache: Map<string, any> = new Map();

  private constructor() {
    this.initializeEngine();
  }

  static getInstance(): PredictionEngine {
    if (!PredictionEngine.instance) {
      PredictionEngine.instance = new PredictionEngine();
    }
    return PredictionEngine.instance;
  }

  private async initializeEngine() {
    // Load creator profile
    await this.loadCreatorProfile();
    // Load trending topics
    await this.refreshTrendingTopics();
  }

  /**
   * Predict content virality based on analysis and historical data
   */
  async predictVirality(
    analysis: ContentAnalysis,
    platform?: string
  ): Promise<ViralityPrediction> {
    const cacheKey = `${analysis.id}_${platform || 'all'}`;
    
    if (this.predictionCache.has(cacheKey)) {
      return this.predictionCache.get(cacheKey);
    }

    try {
      const features = this.extractPredictiveFeatures(analysis);
      const historicalContext = this.getHistoricalContext(platform);
      const trendAlignment = await this.calculateTrendAlignment(analysis);

      const prediction = await invoke<ViralityPrediction>('predict_virality', {
        features,
        historicalData: historicalContext,
        trendScore: trendAlignment,
        platform: platform || 'youtube'
      });

      // Enhance with local insights
      const enhancedPrediction = this.enhancePrediction(prediction, analysis);
      
      this.predictionCache.set(cacheKey, enhancedPrediction);
      return enhancedPrediction;
    } catch (error) {
      console.error('Virality prediction failed:', error);
      return this.generateFallbackPrediction(analysis);
    }
  }

  /**
   * Predict engagement metrics for content
   */
  async predictEngagement(
    analysis: ContentAnalysis,
    platform: string,
    postingTime?: Date
  ): Promise<EngagementMetrics> {
    const creatorStats = this.creatorProfile?.platforms[platform];
    
    if (!creatorStats) {
      throw new Error(`No profile data for platform: ${platform}`);
    }

    try {
      const prediction = await invoke<EngagementMetrics>('predict_engagement', {
        contentFeatures: this.extractPredictiveFeatures(analysis),
        creatorStats: {
          followers: creatorStats.followers,
          avgEngagementRate: creatorStats.avgEngagementRate,
          platform
        },
        postingTime: postingTime?.toISOString(),
        contentType: analysis.fileType
      });

      // Apply platform-specific adjustments
      return this.adjustEngagementForPlatform(prediction, platform, analysis);
    } catch (error) {
      console.error('Engagement prediction failed:', error);
      return this.estimateEngagement(creatorStats, analysis);
    }
  }

  /**
   * Get optimal posting times for content
   */
  async getOptimalPostingTimes(
    platform: string,
    contentType: string,
    timezone: string = 'UTC'
  ): Promise<Date[]> {
    try {
      const times = await invoke<string[]>('get_optimal_posting_times', {
        platform,
        contentType,
        timezone,
        audienceActivity: this.creatorProfile?.platforms[platform]?.audienceDemographics
      });

      return times.map(time => new Date(time));
    } catch (error) {
      console.error('Failed to get optimal posting times:', error);
      // Return default times based on general best practices
      return this.getDefaultPostingTimes(platform);
    }
  }

  /**
   * Analyze content-trend alignment
   */
  async analyzeTrendAlignment(
    analysis: ContentAnalysis
  ): Promise<{
    alignmentScore: number;
    matchingTrends: TrendingTopic[];
    suggestions: string[];
  }> {
    const trends = await this.getTrendingTopics();
    const matchingTrends: TrendingTopic[] = [];
    
    // Check topic alignment
    if (analysis.topics) {
      for (const topic of analysis.topics) {
        const matching = trends.filter(trend => 
          this.isTopicRelated(topic.topic, trend.topic) ||
          topic.keywords.some(keyword => 
            trend.relatedHashtags.includes(keyword.toLowerCase())
          )
        );
        matchingTrends.push(...matching);
      }
    }

    // Calculate alignment score
    const alignmentScore = this.calculateAlignmentScore(matchingTrends, analysis);

    // Generate suggestions
    const suggestions = this.generateTrendSuggestions(trends, analysis, matchingTrends);

    return {
      alignmentScore,
      matchingTrends: matchingTrends.slice(0, 5),
      suggestions
    };
  }

  /**
   * Get competitor insights
   */
  async getCompetitorInsights(
    category: string,
    platform: string
  ): Promise<{
    topPerformers: Array<{
      creatorName: string;
      avgViews: number;
      avgEngagement: number;
      contentFrequency: number;
      successFactors: string[];
    }>;
    contentGaps: string[];
    opportunities: string[];
  }> {
    try {
      const insights = await invoke<any>('get_competitor_insights', {
        category,
        platform,
        region: 'global'
      });

      return {
        topPerformers: insights.topPerformers || [],
        contentGaps: this.identifyContentGaps(insights, category),
        opportunities: this.identifyOpportunities(insights, this.creatorProfile)
      };
    } catch (error) {
      console.error('Failed to get competitor insights:', error);
      return {
        topPerformers: [],
        contentGaps: [],
        opportunities: []
      };
    }
  }

  /**
   * Generate content ideas based on trends and performance
   */
  async generateContentIdeas(
    platform: string,
    category?: string
  ): Promise<Array<{
    title: string;
    description: string;
    predictedScore: number;
    trendAlignment: number;
    difficulty: 'easy' | 'medium' | 'hard';
    resources: string[];
  }>> {
    const trends = await this.getTrendingTopics(platform);
    const profile = this.creatorProfile?.platforms[platform];
    
    if (!profile) {
      return [];
    }

    try {
      const ideas = await invoke<any[]>('generate_content_ideas', {
        trends: trends.map(t => ({
          topic: t.topic,
          score: t.trendScore
        })),
        creatorStrengths: profile.bestPerformingCategories,
        category,
        recentContent: this.getRecentContentTopics()
      });

      return ideas.map(idea => ({
        ...idea,
        predictedScore: this.estimateIdeaScore(idea, trends, profile)
      }));
    } catch (error) {
      console.error('Failed to generate content ideas:', error);
      return this.generateFallbackIdeas(trends, profile);
    }
  }

  /**
   * Update creator profile with new performance data
   */
  async updateCreatorProfile(
    platform: string,
    metrics: Partial<HistoricalPerformance>
  ): Promise<void> {
    if (!this.creatorProfile) {
      await this.loadCreatorProfile();
    }

    try {
      await invoke('update_creator_profile', {
        platform,
        metrics
      });

      // Update local cache
      if (this.creatorProfile && metrics) {
        const perf: HistoricalPerformance = {
          contentId: metrics.contentId || '',
          platform,
          uploadDate: new Date(),
          metrics: metrics.metrics || {
            views: 0,
            likes: 0,
            comments: 0,
            shares: 0,
            watchTime: 0,
            clickThroughRate: 0
          },
          contentFeatures: metrics.contentFeatures || {
            duration: 0,
            hasSubtitles: false,
            thumbnailQuality: 0,
            titleLength: 0,
            descriptionLength: 0,
            tags: []
          }
        };
        
        this.creatorProfile.contentHistory.push(perf);
      }

      // Clear prediction cache as profile has changed
      this.predictionCache.clear();
    } catch (error) {
      console.error('Failed to update creator profile:', error);
    }
  }

  // Private helper methods

  private async loadCreatorProfile(): Promise<void> {
    try {
      this.creatorProfile = await invoke<CreatorProfile>('get_creator_profile');
    } catch (error) {
      console.error('Failed to load creator profile:', error);
      this.creatorProfile = this.createDefaultProfile();
    }
  }

  private async refreshTrendingTopics(): Promise<void> {
    try {
      const platforms = ['youtube', 'tiktok', 'instagram', 'twitter'];
      
      for (const platform of platforms) {
        const topics = await invoke<TrendingTopic[]>('get_trending_topics', {
          platform,
          limit: 50
        });
        this.trendingTopics.set(platform, topics);
      }
    } catch (error) {
      console.error('Failed to refresh trending topics:', error);
    }
  }

  private async getTrendingTopics(platform?: string): Promise<TrendingTopic[]> {
    if (platform) {
      return this.trendingTopics.get(platform) || [];
    }
    
    // Return all trending topics
    const allTopics: TrendingTopic[] = [];
    this.trendingTopics.forEach(topics => {
      if (topics && Array.isArray(topics)) {
        allTopics.push(...topics);
      }
    });
    return allTopics.sort((a, b) => b.trendScore - a.trendScore);
  }

  private extractPredictiveFeatures(analysis: ContentAnalysis): any {
    return {
      // Content features
      duration: analysis.duration || 0,
      fileType: analysis.fileType,
      
      // Quality features
      hasHDVideo: analysis.videoQuality ? 
        parseInt(analysis.videoQuality.resolution.split('x')[1]) >= 720 : false,
      fps: analysis.videoQuality?.fps || 30,
      hasGoodAudio: !analysis.audioQuality?.issues || 
        analysis.audioQuality.issues.length === 0,
      
      // Content understanding
      sceneCount: analysis.scenes?.length || 0,
      avgSceneDuration: analysis.scenes && analysis.duration ? 
        analysis.duration / analysis.scenes.length : 0,
      hasFaces: analysis.faces && analysis.faces.length > 0,
      
      // Engagement features
      sentiment: analysis.sentiment?.overall || 'neutral',
      sentimentScore: analysis.sentiment?.score || 0,
      topicCount: analysis.topics?.length || 0,
      
      // Technical features
      hasTranscript: !!analysis.transcript,
      wordCount: analysis.transcript ? 
        analysis.transcript.reduce((sum, seg) => sum + seg.text.split(' ').length, 0) : 0,
    };
  }

  private getHistoricalContext(platform?: string): any {
    if (!this.creatorProfile) return null;

    const relevantHistory = platform 
      ? this.creatorProfile.contentHistory.filter(h => h.platform === platform)
      : this.creatorProfile.contentHistory;

    return {
      avgViews: this.calculateAverage(relevantHistory.map(h => h.metrics.views)),
      avgEngagement: this.calculateAverage(
        relevantHistory.map(h => 
          h.metrics.likes + h.metrics.comments + h.metrics.shares
        )
      ),
      bestPerformingDuration: this.findOptimalDuration(relevantHistory),
      successPatterns: this.identifySuccessPatterns(relevantHistory)
    };
  }

  private async calculateTrendAlignment(analysis: ContentAnalysis): Promise<number> {
    const result = await this.analyzeTrendAlignment(analysis);
    return result.alignmentScore;
  }

  private enhancePrediction(
    prediction: ViralityPrediction,
    analysis: ContentAnalysis
  ): ViralityPrediction {
    const enhancedFactors = [...prediction.factors];

    // Add platform-specific factors
    if (analysis.platformSuggestions) {
      analysis.platformSuggestions.forEach(suggestion => {
        if (suggestion.score > 80) {
          enhancedFactors.push({
            factor: `High compatibility with ${suggestion.platform}`,
            impact: 'positive',
            weight: 0.8
          });
        }
      });
    }

    // Add quality factors
    if (analysis.videoQuality?.issues && analysis.videoQuality.issues.length > 0) {
      enhancedFactors.push({
        factor: 'Video quality issues detected',
        impact: 'negative',
        weight: 0.6
      });
    }

    return {
      ...prediction,
      factors: enhancedFactors
    };
  }

  private generateFallbackPrediction(analysis: ContentAnalysis): ViralityPrediction {
    // Simple heuristic-based prediction
    let score = 50; // Base score
    const factors: any[] = [];

    // Duration factor
    if (analysis.duration) {
      if (analysis.duration >= 60 && analysis.duration <= 600) {
        score += 10;
        factors.push({
          factor: 'Optimal video length',
          impact: 'positive',
          weight: 1.0
        });
      }
    }

    // Quality factor
    if (analysis.videoQuality && !analysis.videoQuality.issues) {
      score += 15;
      factors.push({
        factor: 'High video quality',
        impact: 'positive',
        weight: 1.5
      });
    }

    // Sentiment factor
    if (analysis.sentiment?.overall === 'positive') {
      score += 10;
      factors.push({
        factor: 'Positive sentiment',
        impact: 'positive',
        weight: 1.0
      });
    }

    return {
      score: Math.min(score, 100),
      confidence: 0.5,
      factors
    };
  }

  private adjustEngagementForPlatform(
    prediction: EngagementMetrics,
    platform: string,
    analysis: ContentAnalysis
  ): EngagementMetrics {
    // Platform-specific multipliers
    const multipliers: { [key: string]: any } = {
      youtube: { views: 1.0, likes: 0.03, comments: 0.01, shares: 0.005 },
      tiktok: { views: 1.5, likes: 0.1, comments: 0.02, shares: 0.03 },
      instagram: { views: 0.8, likes: 0.08, comments: 0.015, shares: 0.02 },
      twitter: { views: 0.5, likes: 0.05, comments: 0.02, shares: 0.04 }
    };

    const mult = multipliers[platform] || multipliers.youtube;

    return {
      predictedViews: Math.round(prediction.predictedViews * mult.views),
      predictedLikes: Math.round(prediction.predictedViews * mult.likes),
      predictedComments: Math.round(prediction.predictedViews * mult.comments),
      predictedShares: Math.round(prediction.predictedViews * mult.shares),
      engagementRate: (mult.likes + mult.comments + mult.shares) * 100,
      watchTime: prediction.watchTime
    };
  }

  private estimateEngagement(
    creatorStats: any,
    analysis: ContentAnalysis
  ): EngagementMetrics {
    const baseViews = creatorStats.followers * 0.1; // 10% reach
    const engagementRate = creatorStats.avgEngagementRate || 0.05;

    return {
      predictedViews: Math.round(baseViews),
      predictedLikes: Math.round(baseViews * engagementRate * 0.6),
      predictedComments: Math.round(baseViews * engagementRate * 0.3),
      predictedShares: Math.round(baseViews * engagementRate * 0.1),
      engagementRate: engagementRate * 100,
      watchTime: (analysis.duration || 0) * 0.5 // 50% average watch time
    };
  }

  private getDefaultPostingTimes(platform: string): Date[] {
    const now = new Date();
    const times: Date[] = [];

    // Platform-specific best times (in UTC)
    const bestHours: { [key: string]: number[] } = {
      youtube: [14, 15, 16, 20, 21], // 2-4 PM, 8-9 PM
      tiktok: [6, 10, 19, 20], // 6 AM, 10 AM, 7-8 PM
      instagram: [11, 13, 17, 19], // 11 AM, 1 PM, 5 PM, 7 PM
      twitter: [9, 10, 19, 20] // 9-10 AM, 7-8 PM
    };

    const hours = bestHours[platform] || bestHours.youtube;
    
    // Generate times for next 7 days
    for (let day = 0; day < 7; day++) {
      for (const hour of hours) {
        const time = new Date(now);
        time.setDate(time.getDate() + day);
        time.setHours(hour, 0, 0, 0);
        times.push(time);
      }
    }

    return times.slice(0, 10); // Return top 10 times
  }

  private calculateAlignmentScore(
    matchingTrends: TrendingTopic[],
    analysis: ContentAnalysis
  ): number {
    if (matchingTrends.length === 0) return 0;

    const avgTrendScore = matchingTrends.reduce((sum, t) => sum + t.trendScore, 0) / 
      matchingTrends.length;
    
    const topicRelevance = analysis.topics ? 
      Math.min(matchingTrends.length / analysis.topics.length, 1) : 0;

    return Math.round((avgTrendScore * 0.7 + topicRelevance * 30) * 100) / 100;
  }

  private isTopicRelated(topic1: string, topic2: string): boolean {
    // Simple similarity check - in production, use NLP
    const t1 = topic1.toLowerCase();
    const t2 = topic2.toLowerCase();
    
    return t1.includes(t2) || t2.includes(t1) || 
      this.calculateSimilarity(t1, t2) > 0.7;
  }

  private calculateSimilarity(str1: string, str2: string): number {
    // Simple Jaccard similarity
    const set1 = new Set(str1.split(' '));
    const set2 = new Set(str2.split(' '));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  private generateTrendSuggestions(
    trends: TrendingTopic[],
    analysis: ContentAnalysis,
    matchingTrends: TrendingTopic[]
  ): string[] {
    const suggestions: string[] = [];

    // Find high-potential trends not covered
    const uncoveredTrends = trends.filter(trend => 
      !matchingTrends.includes(trend) && trend.trendScore > 70
    ).slice(0, 3);

    uncoveredTrends.forEach(trend => {
      suggestions.push(`Consider creating content about "${trend.topic}" - trending with ${trend.trendScore}% score`);
    });

    // Suggest hashtags
    const allHashtags = new Set<string>();
    matchingTrends.forEach(trend => {
      trend.relatedHashtags.forEach(tag => allHashtags.add(tag));
    });

    if (allHashtags.size > 0) {
      suggestions.push(`Use trending hashtags: ${Array.from(allHashtags).slice(0, 5).join(', ')}`);
    }

    return suggestions;
  }

  private identifyContentGaps(insights: any, category: string): string[] {
    // Identify content types that competitors are creating but you're not
    return [
      'Tutorial-style content in this category',
      'Behind-the-scenes content',
      'Collaboration opportunities'
    ];
  }

  private identifyOpportunities(insights: any, profile: any): string[] {
    return [
      'Low competition time slots identified',
      'Underserved audience segments',
      'Emerging format opportunities'
    ];
  }

  private getRecentContentTopics(): string[] {
    if (!this.creatorProfile) return [];
    
    return this.creatorProfile.contentHistory
      .slice(-10)
      .map(content => content.contentFeatures.tags)
      .flat()
      .filter((tag, index, self) => self.indexOf(tag) === index);
  }

  private estimateIdeaScore(idea: any, trends: TrendingTopic[], profile: any): number {
    let score = 50;

    // Check trend alignment
    const relatedTrend = trends.find(t => 
      this.isTopicRelated(idea.title, t.topic)
    );
    
    if (relatedTrend) {
      score += relatedTrend.trendScore * 0.3;
    }

    // Check category strength
    if (profile.bestPerformingCategories.includes(idea.category)) {
      score += 20;
    }

    return Math.min(score, 100);
  }

  private generateFallbackIdeas(trends: TrendingTopic[], profile: any): any[] {
    return trends.slice(0, 5).map(trend => ({
      title: `${trend.topic} - What You Need to Know`,
      description: `Create content about ${trend.topic} while it's trending`,
      predictedScore: trend.trendScore,
      trendAlignment: trend.trendScore,
      difficulty: trend.competitionLevel === 'high' ? 'hard' : 
                  trend.competitionLevel === 'medium' ? 'medium' : 'easy',
      resources: ['Research trending examples', 'Create unique angle']
    }));
  }

  private createDefaultProfile(): CreatorProfile {
    return {
      platforms: {},
      contentHistory: [],
      growthTrajectory: []
    };
  }

  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  private findOptimalDuration(history: HistoricalPerformance[]): number {
    if (history.length === 0) return 300; // Default 5 minutes

    // Find duration of best performing content
    const sorted = [...history].sort((a, b) => 
      b.metrics.views - a.metrics.views
    );
    
    return sorted[0]?.contentFeatures.duration || 300;
  }

  private identifySuccessPatterns(history: HistoricalPerformance[]): string[] {
    const patterns: string[] = [];

    // Analyze top 20% of content
    const sorted = [...history].sort((a, b) => b.metrics.views - a.metrics.views);
    const topContent = sorted.slice(0, Math.ceil(sorted.length * 0.2));

    if (topContent.length === 0) return patterns;

    // Check for common features
    const avgDuration = this.calculateAverage(topContent.map(c => c.contentFeatures.duration));
    patterns.push(`Optimal duration: ${Math.round(avgDuration / 60)} minutes`);

    const withSubtitles = topContent.filter(c => c.contentFeatures.hasSubtitles).length;
    if (withSubtitles > topContent.length * 0.7) {
      patterns.push('Subtitles significantly boost performance');
    }

    return patterns;
  }
}

// Export singleton instance
export const predictionEngine = PredictionEngine.getInstance();