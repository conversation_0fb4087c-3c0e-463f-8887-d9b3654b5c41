import { AISettings, AI_FEATURES, AI_PROVIDERS, DEFAULT_AI_SETTINGS } from '../config/aiConfig';
import { invoke } from '@tauri-apps/api/core';

export interface AIProviderInterface {
  transcribe(filePath: string, options?: any): Promise<any>;
  analyzeContent(filePath: string, options?: any): Promise<any>;
  detectScenes(filePath: string, options?: any): Promise<any>;
  predictVirality(analysis: any): Promise<any>;
  generateThumbnail(filePath: string, options?: any): Promise<any>;
}

// Local provider using FFmpeg and basic algorithms
class LocalAIProvider implements AIProviderInterface {
  async transcribe(_filePath: string): Promise<any> {
    throw new Error('Transcription not available in free tier');
  }

  async analyzeContent(filePath: string): Promise<any> {
    // Use FFmpeg for basic analysis
    return invoke('analyze_content_local', { filePath });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Use FFmpeg scene detection
    return invoke('detect_scenes_local', { filePath });
  }

  async predictVirality(_analysis: any): Promise<any> {
    throw new Error('Virality prediction not available in free tier');
  }

  async generateThumbnail(filePath: string): Promise<any> {
    // Extract frame using FFmpeg
    return invoke('extract_thumbnail_local', { filePath });
  }
}

// Groq provider for LLM-based features
class GroqAIProvider implements AIProviderInterface {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribe(_filePath: string): Promise<any> {
    throw new Error('Groq does not support transcription');
  }

  async analyzeContent(filePath: string): Promise<any> {
    // First get basic metadata with FFmpeg
    const metadata = await invoke('get_media_metadata', { filePath });
    
    // Then use Groq for intelligent analysis
    return invoke('analyze_content_groq', { 
      filePath, 
      metadata,
      apiKey: this.apiKey 
    });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Use local FFmpeg for scene detection
    return invoke('detect_scenes_local', { filePath });
  }

  async predictVirality(analysis: any): Promise<any> {
    return invoke('predict_virality_groq', { 
      analysis,
      apiKey: this.apiKey 
    });
  }

  async generateThumbnail(filePath: string): Promise<any> {
    // Use local extraction
    return invoke('extract_thumbnail_local', { filePath });
  }
}

// Hugging Face provider
class HuggingFaceAIProvider implements AIProviderInterface {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribe(filePath: string): Promise<any> {
    return invoke('transcribe_huggingface', { 
      filePath,
      apiKey: this.apiKey,
      model: 'openai/whisper-base'
    });
  }

  async analyzeContent(filePath: string): Promise<any> {
    const metadata = await invoke('get_media_metadata', { filePath });
    
    // Use multiple HF models for comprehensive analysis
    return invoke('analyze_content_huggingface', { 
      filePath,
      metadata,
      apiKey: this.apiKey 
    });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Use local FFmpeg + HF vision models
    return invoke('detect_scenes_huggingface', { 
      filePath,
      apiKey: this.apiKey 
    });
  }

  async predictVirality(analysis: any): Promise<any> {
    return invoke('predict_virality_huggingface', { 
      analysis,
      apiKey: this.apiKey 
    });
  }

  async generateThumbnail(filePath: string): Promise<any> {
    // Use HF image generation models
    return invoke('generate_thumbnail_huggingface', { 
      filePath,
      apiKey: this.apiKey 
    });
  }
}

// DeepSeek provider
class DeepSeekProvider implements AIProviderInterface {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribe(_filePath: string): Promise<any> {
    // DeepSeek doesn't have native transcription, use with local preprocessing
    throw new Error('DeepSeek does not support direct transcription');
  }

  async analyzeContent(filePath: string): Promise<any> {
    const metadata = await invoke('get_media_metadata', { filePath });
    
    return invoke('analyze_content_deepseek', { 
      filePath,
      metadata,
      apiKey: this.apiKey,
      model: 'deepseek-chat' // or 'deepseek-coder' for technical content
    });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Use local FFmpeg for scene detection
    return invoke('detect_scenes_local', { filePath });
  }

  async predictVirality(analysis: any): Promise<any> {
    return invoke('predict_virality_deepseek', { 
      analysis,
      apiKey: this.apiKey 
    });
  }

  async generateThumbnail(filePath: string): Promise<any> {
    // Extract frames and use DeepSeek to select best one
    return invoke('generate_thumbnail_deepseek', { 
      filePath,
      apiKey: this.apiKey 
    });
  }
}

// Qwen provider
class QwenProvider implements AIProviderInterface {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribe(filePath: string): Promise<any> {
    // Qwen has audio capabilities
    return invoke('transcribe_qwen', { 
      filePath,
      apiKey: this.apiKey,
      model: 'qwen-audio' // Qwen's audio model
    });
  }

  async analyzeContent(filePath: string): Promise<any> {
    const metadata = await invoke('get_media_metadata', { filePath });
    
    return invoke('analyze_content_qwen', { 
      filePath,
      metadata,
      apiKey: this.apiKey,
      model: 'qwen-max' // Most capable model
    });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Qwen-VL for vision tasks
    return invoke('detect_scenes_qwen', { 
      filePath,
      apiKey: this.apiKey,
      model: 'qwen-vl-plus'
    });
  }

  async predictVirality(analysis: any): Promise<any> {
    return invoke('predict_virality_qwen', { 
      analysis,
      apiKey: this.apiKey 
    });
  }

  async generateThumbnail(filePath: string): Promise<any> {
    // Use Qwen's vision capabilities
    return invoke('generate_thumbnail_qwen', { 
      filePath,
      apiKey: this.apiKey,
      model: 'qwen-vl-plus'
    });
  }
}

// OpenAI provider (premium)
class OpenAIProvider implements AIProviderInterface {
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async transcribe(filePath: string): Promise<any> {
    return invoke('transcribe_openai', { 
      filePath,
      apiKey: this.apiKey 
    });
  }

  async analyzeContent(filePath: string): Promise<any> {
    return invoke('analyze_content_openai', { 
      filePath,
      apiKey: this.apiKey 
    });
  }

  async detectScenes(filePath: string): Promise<any> {
    // Combine local detection with GPT-4 Vision
    return invoke('detect_scenes_openai', { 
      filePath,
      apiKey: this.apiKey 
    });
  }

  async predictVirality(analysis: any): Promise<any> {
    return invoke('predict_virality_openai', { 
      analysis,
      apiKey: this.apiKey 
    });
  }

  async generateThumbnail(filePath: string): Promise<any> {
    return invoke('generate_thumbnail_openai', { 
      filePath,
      apiKey: this.apiKey 
    });
  }
}

export class AIProviderFactory {
  private static providers: Map<string, AIProviderInterface> = new Map();
  private static settings: AISettings;

  static initialize(settings: AISettings) {
    console.log('AIProviderFactory.initialize called with:', settings);
    this.settings = settings;
    this.createProviders();
  }

  private static createProviders() {
    // Always create local provider
    this.providers.set('local', new LocalAIProvider());

    // Create API-based providers if keys are available
    if (this.settings.apiKeys.groqApiKey) {
      this.providers.set('groq', new GroqAIProvider(this.settings.apiKeys.groqApiKey));
    }

    if (this.settings.apiKeys.deepseekApiKey) {
      this.providers.set('deepseek', new DeepSeekProvider(this.settings.apiKeys.deepseekApiKey));
    }

    if (this.settings.apiKeys.qwenApiKey) {
      this.providers.set('qwen', new QwenProvider(this.settings.apiKeys.qwenApiKey));
    }

    if (this.settings.apiKeys.huggingfaceApiKey) {
      this.providers.set('huggingface', new HuggingFaceAIProvider(this.settings.apiKeys.huggingfaceApiKey));
    }

    if (this.settings.apiKeys.openaiApiKey) {
      this.providers.set('openai', new OpenAIProvider(this.settings.apiKeys.openaiApiKey));
    }
  }

  static getProvider(feature: string): AIProviderInterface {
    const providerId = this.settings.providers[feature];
    
    if (!providerId || providerId === 'none') {
      throw new Error(`No provider configured for ${feature}`);
    }

    const provider = this.providers.get(providerId);
    if (!provider) {
      // Fallback to local if configured provider isn't available
      console.warn(`Provider ${providerId} not available, falling back to local`);
      return this.providers.get('local')!;
    }

    return provider;
  }

  static async performAnalysis(filePath: string, options: any = {}) {
    console.log('AIProviderFactory.performAnalysis called:', { filePath, options });
    
    if (!this.settings) {
      console.error('AIProviderFactory not initialized!');
      this.initialize(DEFAULT_AI_SETTINGS);
    }
    
    const results: any = {
      filePath,
      timestamp: new Date().toISOString(),
      tier: this.settings.tier,
      providers: {}
    };

    // Transcription
    try {
      const provider = this.getProvider('transcription');
      results.transcript = await provider.transcribe(filePath, options);
      results.providers.transcription = this.settings.providers.transcription;
    } catch (error) {
      console.log('Transcription not available in current tier');
      results.transcript = null;
    }

    // Scene Detection
    try {
      const provider = this.getProvider('sceneDetection');
      results.scenes = await provider.detectScenes(filePath, options);
      results.providers.sceneDetection = this.settings.providers.sceneDetection;
    } catch (error) {
      console.error('Scene detection failed:', error);
      results.scenes = [];
    }

    // Content Analysis
    try {
      const provider = this.getProvider('contentAnalysis');
      results.analysis = await provider.analyzeContent(filePath, options);
      results.providers.contentAnalysis = this.settings.providers.contentAnalysis;
    } catch (error) {
      console.error('Content analysis failed:', error);
      results.analysis = null;
    }

    // Virality Prediction
    if (results.analysis) {
      try {
        const provider = this.getProvider('viralityPrediction');
        results.virality = await provider.predictVirality(results.analysis);
        results.providers.viralityPrediction = this.settings.providers.viralityPrediction;
      } catch (error) {
        console.log('Virality prediction not available in current tier');
        results.virality = null;
      }
    }

    return results;
  }

  static getAvailableProviders(): typeof AI_PROVIDERS {
    return AI_PROVIDERS;
  }

  static getFeatureSupport(): typeof AI_FEATURES {
    return AI_FEATURES;
  }
}