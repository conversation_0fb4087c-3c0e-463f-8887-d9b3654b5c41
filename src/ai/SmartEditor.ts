import { invoke } from '@tauri-apps/api/core';
import {
  ContentAnalysis,
  EditSuggestion,
  EditType,
  EditParameters,
  CaptionStyle,
  AIJob,
  JobStatus
} from './types';

export interface EditProject {
  id: string;
  inputPath: string;
  outputPath?: string;
  analysis: ContentAnalysis;
  appliedEdits: EditSuggestion[];
  timeline: Timeline;
  status: 'draft' | 'processing' | 'completed';
  createdAt: Date;
  modifiedAt: Date;
}

interface Timeline {
  duration: number;
  tracks: {
    video: TimelineTrack;
    audio: TimelineTrack;
    captions?: TimelineTrack;
    effects?: TimelineTrack;
  };
}

interface TimelineTrack {
  segments: TimelineSegment[];
}

interface TimelineSegment {
  id: string;
  startTime: number;
  endTime: number;
  type: 'original' | 'edit' | 'generated';
  data: any;
}

interface AutoEditOptions {
  removeSilence?: boolean;
  removeFillerWords?: boolean;
  enhanceAudio?: boolean;
  autoColorCorrect?: boolean;
  generateCaptions?: boolean;
  optimizeForPlatform?: string;
  targetDuration?: number;
}

interface HighlightOptions {
  duration: number;
  criteria: 'engagement' | 'action' | 'emotion' | 'audio_peaks';
  includeHook?: boolean;
  includeOutro?: boolean;
  transitionStyle?: 'cut' | 'fade' | 'zoom';
}

export class SmartEditor {
  private static instance: SmartEditor;
  private projects: Map<string, EditProject> = new Map();
  private suggestionCache: Map<string, EditSuggestion> = new Map();
  private activeJobs: Map<string, AIJob> = new Map();
  private editHistory: Map<string, EditSuggestion[]> = new Map();

  private constructor() {}

  static getInstance(): SmartEditor {
    if (!SmartEditor.instance) {
      SmartEditor.instance = new SmartEditor();
    }
    return SmartEditor.instance;
  }

  /**
   * Create a new edit project from content analysis
   */
  async createProject(
    inputPath: string,
    analysis: ContentAnalysis
  ): Promise<EditProject> {
    const projectId = this.generateProjectId();
    
    const project: EditProject = {
      id: projectId,
      inputPath,
      analysis,
      appliedEdits: [],
      timeline: await this.createTimeline(inputPath, analysis),
      status: 'draft',
      createdAt: new Date(),
      modifiedAt: new Date()
    };

    this.projects.set(projectId, project);
    return project;
  }

  /**
   * Generate smart edit suggestions based on content analysis
   */
  async generateEditSuggestions(
    analysis: ContentAnalysis,
    options?: {
      targetPlatform?: string;
      editIntensity?: 'light' | 'medium' | 'aggressive';
    }
  ): Promise<EditSuggestion[]> {
    const suggestions: EditSuggestion[] = [];

    // Silence removal
    if (analysis.audioQuality?.volumeLevels.silent_segments) {
      const silentSegments = analysis.audioQuality.volumeLevels.silent_segments;
      if (silentSegments.length > 0) {
        suggestions.push({
          id: this.generateSuggestionId(),
          type: 'remove_silence',
          description: `Remove ${silentSegments.length} silent segments (save ~${this.calculateTimeSaved(silentSegments)}s)`,
          impact: 'high',
          autoApplicable: true,
          parameters: {
            silenceThreshold: -40,
            minSilenceDuration: 0.5
          }
        });
      }
    }

    // Filler word removal
    if (analysis.transcript) {
      const fillerWords = this.detectFillerWords(analysis.transcript);
      if (fillerWords.length > 0) {
        suggestions.push({
          id: this.generateSuggestionId(),
          type: 'remove_filler',
          description: `Remove ${fillerWords.length} filler words ("um", "uh", etc.)`,
          impact: 'medium',
          autoApplicable: true,
          parameters: {
            fillerWords: ['um', 'uh', 'like', 'you know', 'basically', 'actually']
          }
        });
      }
    }

    // Scene transitions
    if (analysis.scenes && analysis.scenes.length > 1) {
      const abruptTransitions = this.detectAbruptTransitions(analysis.scenes);
      if (abruptTransitions.length > 0) {
        suggestions.push({
          id: this.generateSuggestionId(),
          type: 'add_transition',
          description: `Add smooth transitions between ${abruptTransitions.length} scenes`,
          impact: 'medium',
          autoApplicable: true,
          parameters: {
            transitionType: 'fade',
            transitionDuration: 0.5
          }
        });
      }
    }

    // Pacing adjustment
    if (analysis.scenes) {
      const pacingIssue = this.analyzePacing(analysis.scenes);
      if (pacingIssue) {
        suggestions.push({
          id: this.generateSuggestionId(),
          type: 'adjust_pacing',
          description: `Adjust pacing - current pace is too ${pacingIssue}`,
          impact: 'high',
          autoApplicable: false,
          parameters: {
            targetPace: pacingIssue === 'slow' ? 'medium' : 'medium'
          }
        });
      }
    }

    // Color correction
    if (analysis.videoQuality?.issues?.includes('low light') || 
        analysis.videoQuality?.issues?.includes('color balance')) {
      suggestions.push({
        id: this.generateSuggestionId(),
        type: 'color_correction',
        description: 'Apply AI color correction and enhancement',
        impact: 'medium',
        autoApplicable: true,
        parameters: {
          brightness: 0,
          contrast: 0,
          saturation: 0,
          temperature: 0
        }
      });
    }

    // Audio enhancement
    if (analysis.audioQuality?.issues && analysis.audioQuality.issues.length > 0) {
      suggestions.push({
        id: this.generateSuggestionId(),
        type: 'audio_enhancement',
        description: 'Enhance audio quality (reduce noise, normalize volume)',
        impact: 'high',
        autoApplicable: true,
        parameters: {
          noiseReduction: true,
          volumeNormalization: true
        }
      });
    }

    // Captions
    if (analysis.transcript && analysis.transcript.length > 0) {
      suggestions.push({
        id: this.generateSuggestionId(),
        type: 'add_captions',
        description: 'Add animated captions for accessibility and engagement',
        impact: 'high',
        autoApplicable: true,
        parameters: {
          captionStyle: {
            font: 'Arial',
            fontSize: 24,
            color: '#FFFFFF',
            backgroundColor: '#000000',
            position: 'bottom',
            animation: 'pop'
          },
          languages: ['en']
        }
      });
    }

    // Platform-specific crop
    if (options?.targetPlatform) {
      const aspectRatio = this.getPlatformAspectRatio(options.targetPlatform);
      if (aspectRatio && analysis.videoQuality) {
        suggestions.push({
          id: this.generateSuggestionId(),
          type: 'crop_aspect_ratio',
          description: `Crop to ${aspectRatio} for ${options.targetPlatform}`,
          impact: 'medium',
          autoApplicable: true,
          parameters: {
            targetAspectRatio: aspectRatio,
            cropStrategy: analysis.faces && analysis.faces.length > 0 ? 'face_track' : 'center'
          }
        });
      }
    }

    // Highlight generation
    if (analysis.duration && analysis.duration > 120) {
      suggestions.push({
        id: this.generateSuggestionId(),
        type: 'generate_highlight',
        description: 'Create 60-second highlight reel for social media',
        impact: 'high',
        autoApplicable: false,
        parameters: {
          highlightDuration: 60,
          highlightCriteria: 'engagement'
        }
      });
    }

    // Cache suggestions
    suggestions.forEach(s => this.suggestionCache.set(s.id, s));
    
    return suggestions;
  }

  /**
   * Apply automatic edits to content
   */
  async applyAutoEdits(
    projectId: string,
    options: AutoEditOptions
  ): Promise<EditProject> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const jobId = this.generateJobId();
    const job: AIJob = {
      id: jobId,
      type: 'smart_edit',
      status: 'processing',
      priority: 'normal',
      inputPath: project.inputPath,
      options,
      progress: 0,
      startedAt: new Date()
    };

    this.activeJobs.set(jobId, job);

    try {
      // Apply edits in sequence
      let currentPath = project.inputPath;
      let appliedEdits: EditSuggestion[] = [];

      // Remove silence
      if (options.removeSilence) {
        const result = await this.removeSilence(currentPath, project.analysis);
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 20;
      }

      // Remove filler words
      if (options.removeFillerWords && project.analysis.transcript) {
        const result = await this.removeFillerWords(currentPath, project.analysis.transcript);
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 40;
      }

      // Enhance audio
      if (options.enhanceAudio) {
        const result = await this.enhanceAudio(currentPath);
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 60;
      }

      // Color correction
      if (options.autoColorCorrect) {
        const result = await this.applyColorCorrection(currentPath);
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 80;
      }

      // Generate captions
      if (options.generateCaptions && project.analysis.transcript) {
        const result = await this.generateCaptions(currentPath, project.analysis.transcript);
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 90;
      }

      // Platform optimization
      if (options.optimizeForPlatform) {
        const result = await this.optimizeForPlatform(
          currentPath, 
          options.optimizeForPlatform,
          options.targetDuration
        );
        currentPath = result.outputPath;
        appliedEdits.push(result.edit);
        job.progress = 100;
      }

      // Update project
      project.outputPath = currentPath;
      project.appliedEdits = appliedEdits;
      project.status = 'completed';
      project.modifiedAt = new Date();

      job.status = 'completed';
      job.completedAt = new Date();
      job.result = project;

      return project;
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Edit failed';
      throw error;
    }
  }

  /**
   * Generate highlight reel from content
   */
  async generateHighlightReel(
    inputPath: string,
    analysis: ContentAnalysis,
    options: HighlightOptions
  ): Promise<{
    outputPath: string;
    highlights: Array<{
      startTime: number;
      endTime: number;
      score: number;
      reason: string;
    }>;
  }> {
    try {
      const highlights = await invoke<any>('generate_highlights', {
        inputPath,
        duration: options.duration,
        criteria: options.criteria,
        analysis: {
          scenes: analysis.scenes,
          transcript: analysis.transcript,
          sentiment: analysis.sentiment,
          faces: analysis.faces
        }
      });

      // Apply transitions and compile
      const outputPath = await this.compileHighlights(
        inputPath,
        highlights,
        options
      );

      return {
        outputPath,
        highlights
      };
    } catch (error) {
      console.error('Highlight generation failed:', error);
      throw error;
    }
  }

  /**
   * Get edit suggestion by ID
   */
  private async getEditSuggestion(suggestionId: string): Promise<EditSuggestion | null> {
    return this.suggestionCache.get(suggestionId) || null;
  }

  /**
   * Apply single edit suggestion
   */
  async applyEditSuggestion(
    projectId: string,
    suggestionId: string
  ): Promise<EditProject> {
    const project = this.projects.get(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const suggestion = await this.getEditSuggestion(suggestionId);
    if (!suggestion) {
      throw new Error('Suggestion not found');
    }

    const inputPath = project.outputPath || project.inputPath;
    let result: any;

    switch (suggestion.type) {
      case 'remove_silence':
        result = await this.removeSilence(inputPath, project.analysis);
        break;
      case 'remove_filler':
        result = await this.removeFillerWords(inputPath, project.analysis.transcript!);
        break;
      case 'add_transition':
        result = await this.addTransitions(inputPath, project.analysis.scenes!);
        break;
      case 'color_correction':
        result = await this.applyColorCorrection(inputPath);
        break;
      case 'audio_enhancement':
        result = await this.enhanceAudio(inputPath);
        break;
      case 'add_captions':
        result = await this.generateCaptions(inputPath, project.analysis.transcript!);
        break;
      default:
        throw new Error(`Unsupported edit type: ${suggestion.type}`);
    }

    project.outputPath = result.outputPath;
    project.appliedEdits.push(suggestion);
    project.modifiedAt = new Date();

    return project;
  }

  /**
   * Undo last edit
   */
  async undoLastEdit(projectId: string): Promise<EditProject> {
    const project = this.projects.get(projectId);
    if (!project || project.appliedEdits.length === 0) {
      throw new Error('No edits to undo');
    }

    // Remove last edit
    project.appliedEdits.pop();
    
    // Reapply all remaining edits
    let currentPath = project.inputPath;
    for (const edit of project.appliedEdits) {
      const result = await this.applyEditSuggestion(projectId, edit.id);
      currentPath = result.outputPath!;
    }

    project.outputPath = currentPath;
    project.modifiedAt = new Date();

    return project;
  }

  /**
   * Export edited video
   */
  async exportProject(
    projectId: string,
    outputPath: string,
    options?: {
      format?: string;
      quality?: 'low' | 'medium' | 'high' | 'source';
      includeMetadata?: boolean;
    }
  ): Promise<string> {
    const project = this.projects.get(projectId);
    if (!project || !project.outputPath) {
      throw new Error('No edited content to export');
    }

    try {
      const exported = await invoke<string>('export_edited_video', {
        inputPath: project.outputPath,
        outputPath,
        format: options?.format || 'mp4',
        quality: options?.quality || 'high',
        metadata: options?.includeMetadata ? {
          title: project.analysis.fileName,
          edits: project.appliedEdits.map(e => e.description),
          software: 'CreatorOS SmartEditor'
        } : undefined
      });

      return exported;
    } catch (error) {
      console.error('Export failed:', error);
      throw error;
    }
  }

  // Private helper methods

  private async createTimeline(
    inputPath: string,
    analysis: ContentAnalysis
  ): Promise<Timeline> {
    const duration = analysis.duration || 0;

    return {
      duration,
      tracks: {
        video: {
          segments: [{
            id: 'original',
            startTime: 0,
            endTime: duration,
            type: 'original',
            data: { path: inputPath }
          }]
        },
        audio: {
          segments: [{
            id: 'original',
            startTime: 0,
            endTime: duration,
            type: 'original',
            data: { path: inputPath }
          }]
        }
      }
    };
  }

  private async removeSilence(
    inputPath: string,
    analysis: ContentAnalysis
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const outputPath = await invoke<string>('remove_silence', {
      inputPath,
      silenceThreshold: -40,
      minSilenceDuration: 0.5,
      padding: 0.1
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'remove_silence',
      description: 'Removed silence segments',
      impact: 'high',
      autoApplicable: true,
      parameters: {
        silenceThreshold: -40,
        minSilenceDuration: 0.5
      }
    };

    return { outputPath, edit };
  }

  private async removeFillerWords(
    inputPath: string,
    transcript: any[]
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const fillerSegments = this.detectFillerWords(transcript);
    
    const outputPath = await invoke<string>('remove_segments', {
      inputPath,
      segments: fillerSegments.map(f => ({
        startTime: f.startTime,
        endTime: f.endTime
      }))
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'remove_filler',
      description: `Removed ${fillerSegments.length} filler words`,
      impact: 'medium',
      autoApplicable: true,
      parameters: {
        fillerWords: ['um', 'uh', 'like', 'you know']
      }
    };

    return { outputPath, edit };
  }

  private async enhanceAudio(
    inputPath: string
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const outputPath = await invoke<string>('enhance_audio', {
      inputPath,
      noiseReduction: true,
      volumeNormalization: true,
      compressor: true,
      equalizer: 'voice'
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'audio_enhancement',
      description: 'Enhanced audio quality',
      impact: 'high',
      autoApplicable: true,
      parameters: {
        noiseReduction: true,
        volumeNormalization: true
      }
    };

    return { outputPath, edit };
  }

  private async applyColorCorrection(
    inputPath: string
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const outputPath = await invoke<string>('auto_color_correct', {
      inputPath,
      mode: 'ai_enhance'
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'color_correction',
      description: 'Applied AI color correction',
      impact: 'medium',
      autoApplicable: true,
      parameters: {
        brightness: 0,
        contrast: 0,
        saturation: 0,
        temperature: 0
      }
    };

    return { outputPath, edit };
  }

  private async generateCaptions(
    inputPath: string,
    transcript: any[]
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const outputPath = await invoke<string>('add_captions', {
      inputPath,
      transcript: transcript.map(seg => ({
        start: seg.startTime,
        end: seg.endTime,
        text: seg.text
      })),
      style: {
        font: 'Arial',
        fontSize: 24,
        color: '#FFFFFF',
        backgroundColor: '#000000',
        position: 'bottom'
      }
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'add_captions',
      description: 'Added captions',
      impact: 'high',
      autoApplicable: true,
      parameters: {
        captionStyle: {
          font: 'Arial',
          fontSize: 24,
          color: '#FFFFFF',
          position: 'bottom',
          animation: 'fade'
        },
        languages: ['en']
      }
    };

    return { outputPath, edit };
  }

  private async addTransitions(
    inputPath: string,
    scenes: any[]
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const transitions = scenes.slice(0, -1).map((scene, index) => ({
      timestamp: scene.endTime,
      type: 'fade',
      duration: 0.5
    }));

    const outputPath = await invoke<string>('add_transitions', {
      inputPath,
      transitions
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'add_transition',
      description: `Added ${transitions.length} transitions`,
      impact: 'medium',
      autoApplicable: true,
      parameters: {
        transitionType: 'fade',
        transitionDuration: 0.5
      }
    };

    return { outputPath, edit };
  }

  private async optimizeForPlatform(
    inputPath: string,
    platform: string,
    targetDuration?: number
  ): Promise<{ outputPath: string; edit: EditSuggestion }> {
    const outputPath = await invoke<string>('ai_optimize_for_platform', {
      inputPath,
      platform,
      targetDuration
    });

    const edit: EditSuggestion = {
      id: this.generateSuggestionId(),
      type: 'crop_aspect_ratio',
      description: `Optimized for ${platform}`,
      impact: 'high',
      autoApplicable: true,
      parameters: {
        targetAspectRatio: this.getPlatformAspectRatio(platform),
        cropStrategy: 'center'
      }
    };

    return { outputPath, edit };
  }

  private async compileHighlights(
    inputPath: string,
    highlights: any[],
    options: HighlightOptions
  ): Promise<string> {
    return await invoke<string>('compile_highlights', {
      inputPath,
      segments: highlights,
      transitionStyle: options.transitionStyle || 'fade',
      includeHook: options.includeHook,
      includeOutro: options.includeOutro
    });
  }

  private detectFillerWords(transcript: any[]): any[] {
    const fillerWords = ['um', 'uh', 'like', 'you know', 'basically', 'actually'];
    const fillerSegments: any[] = [];

    transcript.forEach(segment => {
      const words = segment.text.toLowerCase().split(/\s+/);
      words.forEach((word: string, index: number) => {
        // Remove punctuation for comparison
        const cleanWord = word.replace(/[,\.!?;:]/g, '');
        if (fillerWords.includes(cleanWord)) {
          // Estimate time position of filler word
          const wordPosition = index / words.length;
          const wordDuration = (segment.endTime - segment.startTime) / words.length;
          
          fillerSegments.push({
            startTime: segment.startTime + (wordPosition * (segment.endTime - segment.startTime)),
            endTime: segment.startTime + (wordPosition * (segment.endTime - segment.startTime)) + wordDuration,
            word: cleanWord
          });
        }
      });
    });

    return fillerSegments;
  }

  private detectAbruptTransitions(scenes: any[]): number[] {
    const abruptTransitions: number[] = [];

    for (let i = 1; i < scenes.length; i++) {
      const prevScene = scenes[i - 1];
      const currentScene = scenes[i];

      // Check if transition is abrupt (no overlap, different mood)
      if (prevScene.endTime === currentScene.startTime &&
          prevScene.mood !== currentScene.mood) {
        abruptTransitions.push(i);
      }
    }

    return abruptTransitions;
  }

  private analyzePacing(scenes: any[]): 'slow' | 'fast' | null {
    if (scenes.length === 0) return null;

    const avgSceneDuration = scenes.reduce((sum, scene) => 
      sum + (scene.endTime - scene.startTime), 0) / scenes.length;

    if (avgSceneDuration > 30) return 'slow';
    if (avgSceneDuration < 3) return 'fast';
    
    return null;
  }

  private calculateTimeSaved(segments: Array<[number, number]>): number {
    return segments.reduce((sum, [start, end]) => sum + (end - start), 0);
  }

  private getPlatformAspectRatio(platform: string): string {
    const ratios: { [key: string]: string } = {
      youtube: '16:9',
      'youtube-shorts': '9:16',
      tiktok: '9:16',
      instagram: '1:1',
      'instagram-reels': '9:16',
      'instagram-stories': '9:16',
      twitter: '16:9',
      linkedin: '16:9'
    };

    return ratios[platform] || '16:9';
  }

  private generateProjectId(): string {
    return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSuggestionId(): string {
    return `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export singleton instance
export const smartEditor = SmartEditor.getInstance();