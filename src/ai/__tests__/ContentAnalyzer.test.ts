import { describe, it, expect, vi, beforeEach } from 'vitest';
import { contentAnalyzer } from '../ContentAnalyzer';
import { ContentAnalysis, TranscriptSegment } from '../types';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}));

// Mock PredictionEngine
vi.mock('../PredictionEngine', () => ({
  predictionEngine: {
    predictVirality: vi.fn()
  }
}));

describe('ContentAnalyzer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Clear the cache before each test
    contentAnalyzer['analysisCache'].clear();
    contentAnalyzer['activeJobs'].clear();
  });

  describe('analyzeContent', () => {
    it('should analyze content and return analysis results', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 120,
        analysisDate: new Date(),
        scenes: [
          {
            startTime: 0,
            endTime: 10,
            sceneType: 'intro',
            description: 'Opening scene',
            keyframes: [],
            mood: 'energetic',
            pace: 'fast'
          }
        ],
        videoQuality: {
          resolution: '1920x1080',
          fps: 30,
          bitrate: 5000000,
          codec: 'h264',
          hasAudio: true,
          issues: []
        }
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce(mockAnalysis);

      const result = await contentAnalyzer.analyzeContent('/test/video.mp4');

      expect(invoke).toHaveBeenCalledWith('analyze_content', expect.objectContaining({
        filePath: '/test/video.mp4',
        options: expect.any(Object)
      }));
      
      expect(result).toEqual(mockAnalysis);
    });

    it('should cache analysis results', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 120,
        analysisDate: new Date()
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValue(mockAnalysis);

      // First call
      await contentAnalyzer.analyzeContent('/test/video.mp4');
      
      // Second call - should use cache
      const cachedResult = await contentAnalyzer.analyzeContent('/test/video.mp4');

      expect(invoke).toHaveBeenCalledTimes(1); // Only called once
      expect(cachedResult).toEqual(mockAnalysis);
    });
  });

  describe('transcribeContent', () => {
    it('should transcribe content and return segments', async () => {
      const mockSegments: TranscriptSegment[] = [
        {
          startTime: 0,
          endTime: 5,
          text: 'Hello world',
          speaker: 'Speaker 1',
          confidence: 0.95,
          language: 'en'
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce({
        segments: mockSegments
      });

      const result = await contentAnalyzer.transcribeContent('/test/audio.mp3');

      expect(invoke).toHaveBeenCalledWith('transcribe_content', expect.objectContaining({
        filePath: '/test/audio.mp3',
        language: 'auto',
        enableSpeakerDetection: true,
        enablePunctuation: true,
        enableTimestamps: true
      }));

      expect(result).toHaveLength(1);
      expect(result[0].text).toBe('Hello world');
    });

    it('should remove filler words when requested', async () => {
      const mockSegments: TranscriptSegment[] = [
        {
          startTime: 0,
          endTime: 5,
          text: 'Um, hello, like, world, you know',
          speaker: 'Speaker 1',
          confidence: 0.95,
          language: 'en'
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce({
        segments: mockSegments
      });

      const result = await contentAnalyzer.transcribeContent('/test/audio.mp3', {
        removeFiller: true
      });

      expect(result[0].text).not.toContain('um');
      expect(result[0].text).not.toContain('like');
      expect(result[0].text).not.toContain('you know');
    });
  });

  describe('detectScenes', () => {
    it('should detect and classify scenes', async () => {
      const mockScenes = [
        {
          startTime: 0,
          endTime: 5,
          sceneType: 'intro',
          description: 'Opening',
          keyframes: [],
          mood: 'calm',
          pace: 'slow'
        },
        {
          startTime: 5,
          endTime: 95,
          sceneType: 'main_content',
          description: 'Main content',
          keyframes: [],
          mood: 'energetic',
          pace: 'medium'
        },
        {
          startTime: 95,
          endTime: 100,
          sceneType: 'outro',
          description: 'Closing',
          keyframes: [],
          mood: 'calm',
          pace: 'slow'
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce({
        scenes: mockScenes
      });

      const result = await contentAnalyzer.detectScenes('/test/video.mp4');

      expect(result).toHaveLength(3);
      expect(result[0].sceneType).toBe('intro');
      expect(result[2].sceneType).toBe('outro');
    });
  });

  describe('analyzeSentiment', () => {
    it('should analyze sentiment from transcript', async () => {
      const transcript: TranscriptSegment[] = [
        {
          startTime: 0,
          endTime: 5,
          text: 'This is amazing!',
          confidence: 0.95
        }
      ];

      const mockSentiment = {
        overall: 'positive' as const,
        score: 0.85,
        timeline: [
          {
            timestamp: 0,
            sentiment: 'positive' as const,
            score: 0.85
          }
        ]
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce(mockSentiment);

      const result = await contentAnalyzer.analyzeSentiment(transcript);

      expect(result.overall).toBe('positive');
      expect(result.score).toBe(0.85);
    });
  });

  describe('predictVirality', () => {
    it('should predict content virality', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 120,
        analysisDate: new Date(),
        topics: [
          {
            topic: 'Technology',
            relevance: 0.9,
            keywords: ['AI', 'innovation'],
            category: 'tech'
          }
        ]
      };

      const mockPrediction = {
        score: 75,
        confidence: 0.8,
        factors: [
          {
            factor: 'Trending topic',
            impact: 'positive' as const,
            weight: 1.5
          }
        ]
      };

      // Mock the invoke call for predictVirality
      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce(mockPrediction);

      const result = await contentAnalyzer.predictVirality(mockAnalysis);

      expect(result.score).toBe(75);
      expect(result.confidence).toBe(0.8);
      expect(result.factors).toHaveLength(1);
    });
  });

  describe('getActiveJobs', () => {
    it('should track active analysis jobs', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 120,
        analysisDate: new Date()
      };

      const { invoke } = await import('@tauri-apps/api/core');
      
      // Create a promise that we can control
      let resolveAnalysis: (value: any) => void;
      const analysisPromise = new Promise((resolve) => {
        resolveAnalysis = resolve;
      });
      
      vi.mocked(invoke).mockReturnValue(analysisPromise);

      // Start analysis (don't await)
      const analyzePromise = contentAnalyzer.analyzeContent('/test/video.mp4');

      // Check active jobs immediately
      const activeJobs = contentAnalyzer.getActiveJobs();
      expect(activeJobs).toHaveLength(1);
      // Job starts as 'queued' then quickly becomes 'processing'
      expect(['queued', 'processing']).toContain(activeJobs[0].status);
      expect(activeJobs[0].type).toBe('content_analysis');
      
      // Now resolve the analysis
      resolveAnalysis!(mockAnalysis);
      
      // Wait for analysis to complete
      await analyzePromise;
    });
  });
});