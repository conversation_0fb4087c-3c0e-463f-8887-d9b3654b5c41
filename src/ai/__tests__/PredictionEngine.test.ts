import { describe, it, expect, vi, beforeEach } from 'vitest';
import { predictionEngine } from '../PredictionEngine';
import { ContentAnalysis, ViralityPrediction } from '../types';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}));

// Mock profile data
const mockProfile = {
  platforms: {
    youtube: {
      followers: 50000,
      avgEngagementRate: 0.05,
      contentFrequency: 3,
      bestPerformingCategories: ['tech', 'tutorial']
    }
  },
  contentHistory: [],
  growthTrajectory: []
};

describe('PredictionEngine', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Mock the loadCreatorProfile call to return our mock profile
    const { invoke } = await import('@tauri-apps/api/core');
    vi.mocked(invoke).mockImplementation((cmd) => {
      if (cmd === 'load_creator_profile') {
        return Promise.resolve(mockProfile);
      }
      if (cmd === 'get_trending_topics') {
        return Promise.resolve([]);
      }
      return Promise.resolve(null);
    });
    
    // Set the profile directly
    predictionEngine['creatorProfile'] = mockProfile;
  });

  describe('predictVirality', () => {
    it('should predict virality for content', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 300,
        analysisDate: new Date(),
        topics: [
          {
            topic: 'Technology',
            relevance: 0.9,
            keywords: ['AI', 'innovation'],
            category: 'tech'
          }
        ],
        sentiment: {
          overall: 'positive',
          score: 0.7,
          timeline: []
        }
      };

      const mockPrediction: ViralityPrediction = {
        score: 82,
        confidence: 0.85,
        factors: [
          {
            factor: 'Trending topic alignment',
            impact: 'positive',
            weight: 2.0
          },
          {
            factor: 'Optimal video length',
            impact: 'positive',
            weight: 1.5
          }
        ]
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockImplementation((cmd) => {
        if (cmd === 'predict_virality') {
          return Promise.resolve(mockPrediction);
        }
        if (cmd === 'load_creator_profile') {
          return Promise.resolve(mockProfile);
        }
        if (cmd === 'get_trending_topics') {
          return Promise.resolve([]);
        }
        return Promise.resolve(null);
      });

      const result = await predictionEngine.predictVirality(mockAnalysis, 'youtube');

      expect(invoke).toHaveBeenCalledWith('predict_virality', expect.objectContaining({
        features: expect.any(Object),
        platform: 'youtube'
      }));

      expect(result.score).toBe(82);
      expect(result.confidence).toBe(0.85);
      expect(result.factors).toHaveLength(2);
    });

    it('should provide fallback prediction on error', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 180, // 3 minutes - optimal
        analysisDate: new Date(),
        sentiment: {
          overall: 'positive',
          score: 0.8,
          timeline: []
        }
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockRejectedValueOnce(new Error('API error'));

      const result = await predictionEngine.predictVirality(mockAnalysis);

      expect(result.score).toBeGreaterThan(50); // Should have decent score
      expect(result.confidence).toBe(0.5); // Low confidence for fallback
      expect(result.factors.length).toBeGreaterThan(0);
    });
  });

  describe('predictEngagement', () => {
    it('should predict engagement metrics', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 600,
        analysisDate: new Date()
      };

      const mockEngagement = {
        predictedViews: 10000,
        predictedLikes: 500,
        predictedComments: 100,
        predictedShares: 50,
        engagementRate: 6.5,
        watchTime: 300
      };

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockImplementation((cmd) => {
        if (cmd === 'predict_engagement') {
          return Promise.resolve(mockEngagement);
        }
        if (cmd === 'load_creator_profile') {
          return Promise.resolve(mockProfile);
        }
        if (cmd === 'get_trending_topics') {
          return Promise.resolve([]);
        }
        return Promise.resolve(null);
      });

      const result = await predictionEngine.predictEngagement(mockAnalysis, 'youtube');

      // Check if the mock was called
      expect(invoke).toHaveBeenCalledWith('predict_engagement', expect.any(Object));
      
      // YouTube multiplier is 1.0 for views, so predicted views should remain 10000
      expect(result.predictedViews).toBe(10000);
      // engagementRate = (0.03 + 0.01 + 0.005) * 100 = 4.5
      expect(result.engagementRate).toBe(4.5);
    });
  });

  describe('getOptimalPostingTimes', () => {
    it('should return optimal posting times', async () => {
      const mockTimes = [
        '2024-01-15T14:00:00Z',
        '2024-01-15T20:00:00Z',
        '2024-01-16T14:00:00Z'
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockImplementation((cmd) => {
        if (cmd === 'get_optimal_posting_times') {
          return Promise.resolve(mockTimes);
        }
        if (cmd === 'load_creator_profile') {
          return Promise.resolve(mockProfile);
        }
        if (cmd === 'get_trending_topics') {
          return Promise.resolve([]);
        }
        return Promise.resolve(null);
      });

      const result = await predictionEngine.getOptimalPostingTimes('youtube', 'video');

      expect(result).toHaveLength(3);
      expect(result[0]).toBeInstanceOf(Date);
    });

    it('should return default times on error', async () => {
      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockRejectedValueOnce(new Error('API error'));

      const result = await predictionEngine.getOptimalPostingTimes('youtube', 'video');

      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toBeInstanceOf(Date);
    });
  });

  describe('analyzeTrendAlignment', () => {
    it('should analyze content-trend alignment', async () => {
      const mockAnalysis: ContentAnalysis = {
        id: 'test-id',
        filePath: '/test/video.mp4',
        fileName: 'video.mp4',
        fileType: 'video',
        duration: 300,
        analysisDate: new Date(),
        topics: [
          {
            topic: 'AI Technology',
            relevance: 0.9,
            keywords: ['artificial intelligence', 'machine learning'],
            category: 'tech'
          }
        ]
      };

      const mockTrends = [
        {
          topic: 'AI',
          platform: 'youtube',
          trendScore: 85,
          growthRate: 15,
          relatedHashtags: ['#AI', '#MachineLearning'],
          peakTimes: ['14:00', '20:00'],
          competitionLevel: 'medium' as const
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockImplementation((cmd) => {
        if (cmd === 'get_trending_topics') {
          return Promise.resolve(mockTrends);
        }
        if (cmd === 'load_creator_profile') {
          return Promise.resolve(mockProfile);
        }
        return Promise.resolve(null);
      });
      
      // Set trending topics in the cache
      predictionEngine['trendingTopics'].set('youtube', mockTrends);

      const result = await predictionEngine.analyzeTrendAlignment(mockAnalysis);

      expect(result.alignmentScore).toBeGreaterThan(0);
      expect(result.matchingTrends.length).toBeGreaterThan(0);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });
  });

  describe('generateContentIdeas', () => {
    it('should generate content ideas based on trends', async () => {
      const mockIdeas = [
        {
          title: 'AI Tools for Content Creators',
          description: 'Explore the latest AI tools',
          predictedScore: 0,
          trendAlignment: 85,
          difficulty: 'medium' as const,
          resources: ['Research tools', 'Create demos']
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockImplementation((cmd) => {
        if (cmd === 'load_creator_profile') {
          return Promise.resolve(mockProfile);
        }
        if (cmd === 'get_trending_topics') {
          return Promise.resolve([
            {
              topic: 'AI Tools',
              platform: 'youtube',
              trendScore: 85,
              growthRate: 20,
              relatedHashtags: ['#AI', '#Tools'],
              peakTimes: ['14:00'],
              competitionLevel: 'medium'
            }
          ]);
        }
        if (cmd === 'generate_content_ideas') {
          return Promise.resolve(mockIdeas);
        }
        return Promise.resolve(null);
      });

      const result = await predictionEngine.generateContentIdeas('youtube', 'tech');

      expect(result.length).toBeGreaterThan(0);
      expect(result[0].title).toBe('AI Tools for Content Creators');
      expect(result[0].predictedScore).toBeGreaterThan(0); // Should be calculated
    });
  });
});