import { describe, it, expect, vi, beforeEach } from 'vitest';
import { smartEditor } from '../SmartEditor';
import { ContentAnalysis, EditSuggestion } from '../types';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}));

describe('SmartEditor', () => {
  const mockAnalysis: ContentAnalysis = {
    id: 'test-id',
    filePath: '/test/video.mp4',
    fileName: 'video.mp4',
    fileType: 'video',
    duration: 600,
    analysisDate: new Date(),
    scenes: [
      {
        startTime: 0,
        endTime: 100,
        sceneType: 'intro',
        description: 'Opening',
        keyframes: [],
        mood: 'energetic',
        pace: 'medium'
      },
      {
        startTime: 100,
        endTime: 500,
        sceneType: 'main_content',
        description: 'Main content',
        keyframes: [],
        mood: 'calm',
        pace: 'medium'
      }
    ],
    audioQuality: {
      sampleRate: 48000,
      bitrate: 128000,
      channels: 2,
      codec: 'aac',
      volumeLevels: {
        average: -20,
        peak: -10,
        silent_segments: [[50, 55], [200, 203]]
      },
      issues: ['background noise']
    },
    transcript: [
      {
        startTime: 0,
        endTime: 5,
        text: 'Um, hello everyone',
        confidence: 0.95
      },
      {
        startTime: 5,
        endTime: 10,
        text: 'Today we will, like, discuss AI',
        confidence: 0.93
      }
    ],
    videoQuality: {
      resolution: '1920x1080',
      fps: 30,
      bitrate: 5000000,
      codec: 'h264',
      hasAudio: true,
      issues: []
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createProject', () => {
    it('should create a new edit project', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);

      expect(project.id).toBeDefined();
      expect(project.inputPath).toBe('/test/video.mp4');
      expect(project.analysis).toEqual(mockAnalysis);
      expect(project.status).toBe('draft');
      expect(project.appliedEdits).toHaveLength(0);
    });
  });

  describe('generateEditSuggestions', () => {
    it('should generate edit suggestions based on analysis', async () => {
      const suggestions = await smartEditor.generateEditSuggestions(mockAnalysis);

      // Should suggest silence removal
      const silenceSuggestion = suggestions.find(s => s.type === 'remove_silence');
      expect(silenceSuggestion).toBeDefined();
      expect(silenceSuggestion?.description).toContain('2 silent segments');

      // Should suggest filler word removal
      const fillerSuggestion = suggestions.find(s => s.type === 'remove_filler');
      expect(fillerSuggestion).toBeDefined();

      // Should suggest audio enhancement
      const audioSuggestion = suggestions.find(s => s.type === 'audio_enhancement');
      expect(audioSuggestion).toBeDefined();
      expect(audioSuggestion?.impact).toBe('high');

      // Should suggest captions
      const captionSuggestion = suggestions.find(s => s.type === 'add_captions');
      expect(captionSuggestion).toBeDefined();
    });

    it('should generate platform-specific suggestions', async () => {
      const suggestions = await smartEditor.generateEditSuggestions(mockAnalysis, {
        targetPlatform: 'tiktok'
      });

      const cropSuggestion = suggestions.find(s => s.type === 'crop_aspect_ratio');
      expect(cropSuggestion).toBeDefined();
      expect(cropSuggestion?.description).toContain('9:16');
      expect(cropSuggestion?.description).toContain('tiktok');
    });

    it('should suggest highlight generation for long videos', async () => {
      const suggestions = await smartEditor.generateEditSuggestions(mockAnalysis);

      const highlightSuggestion = suggestions.find(s => s.type === 'generate_highlight');
      expect(highlightSuggestion).toBeDefined();
      expect(highlightSuggestion?.description).toContain('60-second highlight reel');
    });
  });

  describe('applyAutoEdits', () => {
    it('should apply automatic edits to content', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke)
        .mockResolvedValueOnce('/tmp/video_no_silence.mp4') // remove_silence
        .mockResolvedValueOnce('/tmp/video_no_filler.mp4')  // remove_segments
        .mockResolvedValueOnce('/tmp/video_enhanced.mp4')   // enhance_audio
        .mockResolvedValueOnce('/tmp/video_final.mp4');     // add_captions

      const result = await smartEditor.applyAutoEdits(project.id, {
        removeSilence: true,
        removeFillerWords: true,
        enhanceAudio: true,
        generateCaptions: true
      });

      expect(result.outputPath).toBe('/tmp/video_final.mp4');
      expect(result.appliedEdits).toHaveLength(4);
      expect(result.status).toBe('completed');
    });

    it('should handle errors during editing', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockRejectedValueOnce(new Error('Processing failed'));

      await expect(smartEditor.applyAutoEdits(project.id, {
        removeSilence: true
      })).rejects.toThrow('Processing failed');
    });
  });

  describe('generateHighlightReel', () => {
    it('should generate highlight reel from content', async () => {
      const mockHighlights = [
        {
          startTime: 10,
          endTime: 20,
          score: 0.9,
          reason: 'High engagement moment'
        },
        {
          startTime: 100,
          endTime: 110,
          score: 0.85,
          reason: 'Key point discussed'
        }
      ];

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke)
        .mockResolvedValueOnce(mockHighlights) // generate_highlights
        .mockResolvedValueOnce('/tmp/highlights.mp4'); // compile_highlights

      const result = await smartEditor.generateHighlightReel(
        '/test/video.mp4',
        mockAnalysis,
        {
          duration: 60,
          criteria: 'engagement',
          includeHook: true,
          transitionStyle: 'fade'
        }
      );

      expect(result.outputPath).toBe('/tmp/highlights.mp4');
      expect(result.highlights).toHaveLength(2);
      expect(result.highlights[0].score).toBe(0.9);
    });
  });

  describe('applyEditSuggestion', () => {
    it('should apply individual edit suggestion', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);
      const suggestions = await smartEditor.generateEditSuggestions(mockAnalysis);
      const silenceSuggestion = suggestions.find(s => s.type === 'remove_silence')!;

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce('/tmp/video_edited.mp4');

      // Mock the private getEditSuggestion method to return our suggestion
      smartEditor['suggestionCache'] = new Map([[silenceSuggestion.id, silenceSuggestion]]);

      const result = await smartEditor.applyEditSuggestion(project.id, silenceSuggestion.id);

      expect(result.outputPath).toBe('/tmp/video_edited.mp4');
      expect(result.appliedEdits).toHaveLength(1);
      expect(result.appliedEdits[0].type).toBe('remove_silence');
    });
  });

  describe('exportProject', () => {
    it('should export edited video', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);
      project.outputPath = '/tmp/edited.mp4';

      const { invoke } = await import('@tauri-apps/api/core');
      vi.mocked(invoke).mockResolvedValueOnce('/output/final.mp4');

      const result = await smartEditor.exportProject(
        project.id,
        '/output/final.mp4',
        {
          format: 'mp4',
          quality: 'high',
          includeMetadata: true
        }
      );

      expect(result).toBe('/output/final.mp4');
      expect(invoke).toHaveBeenCalledWith('export_edited_video', expect.objectContaining({
        inputPath: '/tmp/edited.mp4',
        outputPath: '/output/final.mp4',
        format: 'mp4',
        quality: 'high'
      }));
    });

    it('should throw error if no edited content', async () => {
      const project = await smartEditor.createProject('/test/video.mp4', mockAnalysis);

      await expect(smartEditor.exportProject(project.id, '/output/final.mp4'))
        .rejects.toThrow('No edited content to export');
    });
  });
});