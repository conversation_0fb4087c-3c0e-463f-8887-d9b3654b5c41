import { invoke } from '@tauri-apps/api/core';
import { 
  ContentAnalysis, 
  SceneAnalysis, 
  TranscriptSegment,
  SentimentAnalysis,
  TopicExtraction,
  VideoQualityMetrics,
  AudioQualityMetrics,
  PlatformOptimization,
  ViralityPrediction,
  AIJob,
  JobStatus
} from './types';

export class ContentAnalyzer {
  private static instance: ContentAnalyzer;
  private analysisCache: Map<string, ContentAnalysis> = new Map();
  private activeJobs: Map<string, AIJob> = new Map();

  private constructor() {}

  static getInstance(): ContentAnalyzer {
    if (!ContentAnalyzer.instance) {
      ContentAnalyzer.instance = new ContentAnalyzer();
    }
    return ContentAnalyzer.instance;
  }

  /**
   * Analyze a media file with AI
   */
  async analyzeContent(filePath: string, options?: AnalysisOptions): Promise<ContentAnalysis> {
    // Check cache first
    const cacheKey = this.getCacheKey(filePath, options);
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!;
    }

    // Create analysis job
    const jobId = this.generateJobId();
    const job: AIJob = {
      id: jobId,
      type: 'content_analysis',
      status: 'queued',
      priority: options?.priority || 'normal',
      inputPath: filePath,
      options: options || {},
      progress: 0,
      startedAt: new Date()
    };

    this.activeJobs.set(jobId, job);

    try {
      // Start analysis in backend
      const analysis = await invoke<ContentAnalysis>('analyze_content', {
        filePath,
        options: {
          enableTranscription: options?.enableTranscription ?? true,
          enableSceneDetection: options?.enableSceneDetection ?? true,
          enableObjectDetection: options?.enableObjectDetection ?? true,
          enableFaceDetection: options?.enableFaceDetection ?? false,
          enableSentimentAnalysis: options?.enableSentimentAnalysis ?? true,
          enablePlatformOptimization: options?.enablePlatformOptimization ?? true,
          targetPlatforms: options?.targetPlatforms || ['youtube', 'tiktok', 'instagram'],
        }
      });

      // Update job status
      job.status = 'completed';
      job.completedAt = new Date();
      job.result = analysis;

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);

      return analysis;
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Analysis failed';
      throw error;
    } finally {
      // Clean up job after a delay
      setTimeout(() => {
        this.activeJobs.delete(jobId);
      }, 60000); // Keep for 1 minute for status checking
    }
  }

  /**
   * Get real-time transcription with speaker detection
   */
  async transcribeContent(
    filePath: string, 
    options?: TranscriptionOptions
  ): Promise<TranscriptSegment[]> {
    try {
      const response = await invoke<{ segments: TranscriptSegment[] }>('transcribe_content', {
        filePath,
        language: options?.language || 'auto',
        enableSpeakerDetection: options?.enableSpeakerDetection ?? true,
        enablePunctuation: options?.enablePunctuation ?? true,
        enableTimestamps: true,
      });

      // Post-process transcription
      return this.enhanceTranscription(response.segments, options);
    } catch (error) {
      console.error('Transcription failed:', error);
      throw error;
    }
  }

  /**
   * Detect and analyze scenes in video
   */
  async detectScenes(filePath: string): Promise<SceneAnalysis[]> {
    try {
      const response = await invoke<{ scenes: SceneAnalysis[] }>('detect_scenes', {
        filePath,
        minSceneDuration: 1.0, // seconds
        threshold: 0.3, // scene change threshold
      });

      // Enhance scene data with additional analysis
      return this.enhanceSceneAnalysis(response.scenes);
    } catch (error) {
      console.error('Scene detection failed:', error);
      throw error;
    }
  }

  /**
   * Analyze sentiment throughout content
   */
  async analyzeSentiment(
    transcript: TranscriptSegment[]
  ): Promise<SentimentAnalysis> {
    try {
      const sentiment = await invoke<SentimentAnalysis>('analyze_sentiment', {
        segments: transcript.map(seg => ({
          text: seg.text,
          timestamp: seg.startTime
        }))
      });

      return sentiment;
    } catch (error) {
      console.error('Sentiment analysis failed:', error);
      throw error;
    }
  }

  /**
   * Extract key topics and keywords
   */
  async extractTopics(
    transcript: TranscriptSegment[],
    metadata?: any
  ): Promise<TopicExtraction[]> {
    try {
      const topics = await invoke<TopicExtraction[]>('extract_topics', {
        text: transcript.map(seg => seg.text).join(' '),
        metadata,
        maxTopics: 10,
        minRelevance: 0.3
      });

      return topics.sort((a, b) => b.relevance - a.relevance);
    } catch (error) {
      console.error('Topic extraction failed:', error);
      throw error;
    }
  }

  /**
   * Analyze video quality metrics
   */
  async analyzeVideoQuality(filePath: string): Promise<VideoQualityMetrics> {
    try {
      const metrics = await invoke<VideoQualityMetrics>('analyze_video_quality', {
        filePath
      });

      // Add quality recommendations
      const issues = this.detectVideoIssues(metrics);
      return { ...metrics, issues };
    } catch (error) {
      console.error('Video quality analysis failed:', error);
      throw error;
    }
  }

  /**
   * Analyze audio quality metrics
   */
  async analyzeAudioQuality(filePath: string): Promise<AudioQualityMetrics> {
    try {
      const metrics = await invoke<AudioQualityMetrics>('analyze_audio_quality', {
        filePath
      });

      // Add quality recommendations
      const issues = this.detectAudioIssues(metrics);
      return { ...metrics, issues };
    } catch (error) {
      console.error('Audio quality analysis failed:', error);
      throw error;
    }
  }

  /**
   * Generate platform-specific optimization suggestions
   */
  async generatePlatformOptimizations(
    analysis: ContentAnalysis,
    platforms: string[]
  ): Promise<PlatformOptimization[]> {
    const optimizations: PlatformOptimization[] = [];

    for (const platform of platforms) {
      try {
        const optimization = await invoke<PlatformOptimization>('ai_optimize_for_platform', {
          analysis: {
            duration: analysis.duration,
            hasTranscript: !!analysis.transcript,
            topics: analysis.topics,
            videoQuality: analysis.videoQuality,
            sentiment: analysis.sentiment?.overall
          },
          platform
        });

        optimizations.push(optimization);
      } catch (error) {
        console.error(`Platform optimization failed for ${platform}:`, error);
      }
    }

    return optimizations;
  }

  /**
   * Predict content virality and engagement
   */
  async predictVirality(
    analysis: ContentAnalysis,
    historicalData?: any[]
  ): Promise<ViralityPrediction> {
    try {
      const prediction = await invoke<ViralityPrediction>('predict_virality', {
        contentFeatures: this.extractContentFeatures(analysis),
        historicalPerformance: historicalData,
        platform: analysis.platformSuggestions?.[0]?.platform || 'youtube'
      });

      return prediction;
    } catch (error) {
      console.error('Virality prediction failed:', error);
      throw error;
    }
  }

  /**
   * Get analysis job status
   */
  getJobStatus(jobId: string): AIJob | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Get all active jobs
   */
  getActiveJobs(): AIJob[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Cancel an analysis job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId);
    if (!job || job.status !== 'processing') {
      return false;
    }

    try {
      await invoke('cancel_ai_job', { jobId });
      job.status = 'cancelled';
      return true;
    } catch (error) {
      console.error('Failed to cancel job:', error);
      return false;
    }
  }

  // Private helper methods

  private getCacheKey(filePath: string, options?: any): string {
    return `${filePath}_${JSON.stringify(options || {})}`;
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private enhanceTranscription(
    segments: TranscriptSegment[],
    options?: TranscriptionOptions
  ): TranscriptSegment[] {
    // Add punctuation improvement, filler word detection, etc.
    return segments.map(segment => {
      let text = segment.text;

      // Remove filler words if requested
      if (options?.removeFiller) {
        const fillerWords = ['um', 'uh', 'like', 'you know', 'basically'];
        fillerWords.forEach(filler => {
          text = text.replace(new RegExp(`\\b${filler}\\b`, 'gi'), '');
        });
      }

      // Clean up extra spaces
      text = text.replace(/\s+/g, ' ').trim();

      return { ...segment, text };
    });
  }

  private enhanceSceneAnalysis(scenes: SceneAnalysis[]): SceneAnalysis[] {
    // Add scene type classification based on content
    return scenes.map((scene, index) => {
      let sceneType = scene.sceneType || 'other';

      // Simple heuristics for scene classification
      if (index === 0 && scene.endTime - scene.startTime < 10) {
        sceneType = 'intro';
      } else if (index === scenes.length - 1 && scene.endTime - scene.startTime < 10) {
        sceneType = 'outro';
      } else if (scene.endTime - scene.startTime < 3) {
        sceneType = 'transition';
      }

      return { ...scene, sceneType };
    });
  }

  private detectVideoIssues(metrics: VideoQualityMetrics): string[] {
    const issues: string[] = [];

    // Check resolution
    const [width, height] = metrics.resolution.split('x').map(Number);
    if (width < 1280 || height < 720) {
      issues.push('Low resolution (recommend at least 720p)');
    }

    // Check FPS
    if (metrics.fps < 24) {
      issues.push('Low frame rate (recommend at least 24fps)');
    }

    // Check bitrate
    if (metrics.bitrate < 2000000) { // 2 Mbps
      issues.push('Low bitrate may result in compression artifacts');
    }

    return issues;
  }

  private detectAudioIssues(metrics: AudioQualityMetrics): string[] {
    const issues: string[] = [];

    // Check sample rate
    if (metrics.sampleRate < 44100) {
      issues.push('Low sample rate (recommend at least 44.1kHz)');
    }

    // Check for silence
    const totalSilence = metrics.volumeLevels.silent_segments.reduce(
      (sum, [start, end]) => sum + (end - start), 0
    );
    if (totalSilence > 10) { // More than 10 seconds of silence
      issues.push('Extended silence detected');
    }

    // Check volume levels
    if (metrics.volumeLevels.peak > -3) {
      issues.push('Audio may be clipping');
    }
    if (metrics.volumeLevels.average < -30) {
      issues.push('Audio levels too low');
    }

    return issues;
  }

  private extractContentFeatures(analysis: ContentAnalysis): any {
    return {
      duration: analysis.duration,
      sceneCount: analysis.scenes?.length || 0,
      averageSceneDuration: analysis.scenes 
        ? analysis.duration! / analysis.scenes.length 
        : 0,
      hasFaces: (analysis.faces?.length || 0) > 0,
      sentiment: analysis.sentiment?.overall,
      topicCount: analysis.topics?.length || 0,
      videoQuality: analysis.videoQuality?.resolution,
      audioQuality: analysis.audioQuality?.sampleRate,
    };
  }
}

// Options interfaces

interface AnalysisOptions {
  priority?: 'high' | 'normal' | 'low';
  enableTranscription?: boolean;
  enableSceneDetection?: boolean;
  enableObjectDetection?: boolean;
  enableFaceDetection?: boolean;
  enableSentimentAnalysis?: boolean;
  enablePlatformOptimization?: boolean;
  targetPlatforms?: string[];
}

interface TranscriptionOptions {
  language?: string;
  enableSpeakerDetection?: boolean;
  enablePunctuation?: boolean;
  removeFiller?: boolean;
}

// Export singleton instance
export const contentAnalyzer = ContentAnalyzer.getInstance();