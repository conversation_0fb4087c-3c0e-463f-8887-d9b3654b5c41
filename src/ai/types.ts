// AI Service Types and Interfaces

export interface ContentAnalysis {
  id: string;
  filePath: string;
  fileName: string;
  fileType: 'video' | 'audio' | 'image';
  duration?: number;
  analysisDate: Date;
  
  // Content Understanding
  scenes?: SceneAnalysis[];
  objects?: ObjectDetection[];
  faces?: FaceDetection[];
  transcript?: TranscriptSegment[];
  sentiment?: SentimentAnalysis;
  topics?: TopicExtraction[];
  
  // Quality Metrics
  videoQuality?: VideoQualityMetrics;
  audioQuality?: AudioQualityMetrics;
  
  // Platform Optimization
  platformSuggestions?: PlatformOptimization[];
  
  // Performance Prediction
  virality?: ViralityPrediction;
  engagementPrediction?: EngagementMetrics;
}

export interface SceneAnalysis {
  startTime: number;
  endTime: number;
  sceneType: 'intro' | 'main_content' | 'transition' | 'outro' | 'other';
  description: string;
  keyframes: string[]; // Base64 encoded thumbnails
  mood?: 'energetic' | 'calm' | 'dramatic' | 'humorous' | 'serious';
  pace?: 'fast' | 'medium' | 'slow';
}

export interface ObjectDetection {
  timestamp: number;
  objects: Array<{
    label: string;
    confidence: number;
    boundingBox: BoundingBox;
  }>;
}

export interface FaceDetection {
  timestamp: number;
  faces: Array<{
    id: string;
    boundingBox: BoundingBox;
    emotions?: EmotionScores;
    landmarks?: FaceLandmarks;
  }>;
}

export interface TranscriptSegment {
  startTime: number;
  endTime: number;
  text: string;
  speaker?: string;
  confidence: number;
  language?: string;
}

export interface SentimentAnalysis {
  overall: 'positive' | 'negative' | 'neutral' | 'mixed';
  score: number; // -1 to 1
  timeline: Array<{
    timestamp: number;
    sentiment: 'positive' | 'negative' | 'neutral';
    score: number;
  }>;
}

export interface TopicExtraction {
  topic: string;
  relevance: number; // 0 to 1
  keywords: string[];
  category?: string;
}

export interface VideoQualityMetrics {
  resolution: string;
  fps: number;
  bitrate: number;
  codec: string;
  hasAudio: boolean;
  issues?: string[]; // e.g., "low light", "shaky footage", "compression artifacts"
}

export interface AudioQualityMetrics {
  sampleRate: number;
  bitrate: number;
  channels: number;
  codec: string;
  volumeLevels: {
    average: number;
    peak: number;
    silent_segments: Array<[number, number]>;
  };
  issues?: string[]; // e.g., "background noise", "clipping", "echo"
}

export interface PlatformOptimization {
  platform: 'youtube' | 'tiktok' | 'instagram' | 'twitter' | 'linkedin';
  recommendations: {
    idealLength?: number;
    aspectRatio?: string;
    thumbnailSuggestions?: string[];
    titleSuggestions?: string[];
    descriptionTemplate?: string;
    hashtags?: string[];
    bestPostingTimes?: string[];
    formatRecommendation?: string;
  };
  score: number; // 0 to 100 - how well suited for this platform
}

export interface ViralityPrediction {
  score: number; // 0 to 100
  confidence: number; // 0 to 1
  factors: {
    factor: string;
    impact: 'positive' | 'negative';
    weight: number;
  }[];
  similarContent?: {
    contentId: string;
    performance: EngagementMetrics;
  }[];
}

export interface EngagementMetrics {
  predictedViews: number;
  predictedLikes: number;
  predictedComments: number;
  predictedShares: number;
  engagementRate: number;
  watchTime: number;
}

// Smart Editing Types

export interface EditSuggestion {
  id: string;
  type: EditType;
  description: string;
  impact: 'high' | 'medium' | 'low';
  autoApplicable: boolean;
  parameters: EditParameters;
  preview?: string; // Preview URL or base64
  estimatedTimeSaved?: number; // in seconds
}

export type EditType = 
  | 'remove_silence'
  | 'remove_filler'
  | 'add_transition'
  | 'adjust_pacing'
  | 'color_correction'
  | 'audio_enhancement'
  | 'add_captions'
  | 'crop_aspect_ratio'
  | 'generate_highlight'
  | 'add_music'
  | 'add_intro_outro';

export interface EditParameters {
  // Remove silence
  silenceThreshold?: number;
  minSilenceDuration?: number;
  
  // Remove filler words
  fillerWords?: string[];
  
  // Transitions
  transitionType?: 'fade' | 'cut' | 'swipe' | 'zoom';
  transitionDuration?: number;
  
  // Pacing
  targetPace?: 'fast' | 'medium' | 'slow';
  
  // Color correction
  brightness?: number;
  contrast?: number;
  saturation?: number;
  temperature?: number;
  
  // Audio
  noiseReduction?: boolean;
  volumeNormalization?: boolean;
  
  // Captions
  captionStyle?: CaptionStyle;
  languages?: string[];
  
  // Aspect ratio
  targetAspectRatio?: string;
  cropStrategy?: 'center' | 'face_track' | 'action_track';
  
  // Highlights
  highlightDuration?: number;
  highlightCriteria?: 'engagement' | 'action' | 'emotion';
  
  // Music
  musicMood?: string;
  musicGenre?: string;
  
  // Intro/Outro
  templateId?: string;
  brandingElements?: BrandingElements;
}

export interface CaptionStyle {
  font: string;
  fontSize: number;
  color: string;
  backgroundColor?: string;
  position: 'top' | 'center' | 'bottom';
  animation?: 'none' | 'fade' | 'pop' | 'slide';
}

export interface BrandingElements {
  logo?: string;
  watermark?: string;
  colors?: string[];
  fonts?: string[];
}

// AI Model Management

export interface AIModel {
  id: string;
  name: string;
  type: ModelType;
  version: string;
  size: number;
  path?: string;
  remoteUrl?: string;
  isDownloaded: boolean;
  isLoaded: boolean;
  capabilities: string[];
  requirements: ModelRequirements;
}

export type ModelType = 
  | 'speech_recognition'
  | 'object_detection'
  | 'face_detection'
  | 'scene_classification'
  | 'sentiment_analysis'
  | 'style_transfer'
  | 'upscaling'
  | 'background_removal';

export interface ModelRequirements {
  minRAM: number;
  recommendedRAM: number;
  gpu: boolean;
  supportedFormats: string[];
}

// Processing Queue

export interface AIJob {
  id: string;
  type: AIJobType;
  status: JobStatus;
  priority: 'high' | 'normal' | 'low';
  inputPath: string;
  outputPath?: string;
  options: any;
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
  result?: any;
}

export type AIJobType = 
  | 'content_analysis'
  | 'smart_edit'
  | 'transcription'
  | 'thumbnail_generation'
  | 'highlight_extraction'
  | 'platform_optimization';

export type JobStatus = 
  | 'queued'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'cancelled';

// Utility Types

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface EmotionScores {
  happiness: number;
  sadness: number;
  anger: number;
  fear: number;
  surprise: number;
  disgust: number;
  neutral: number;
}

export interface FaceLandmarks {
  leftEye: [number, number];
  rightEye: [number, number];
  nose: [number, number];
  mouth: [number, number];
  leftEar?: [number, number];
  rightEar?: [number, number];
}

// AI Service Configuration

export interface AIConfig {
  enableLocalProcessing: boolean;
  enableCloudProcessing: boolean;
  maxConcurrentJobs: number;
  cacheAnalysisResults: boolean;
  privacyMode: boolean;
  modelStoragePath: string;
  supportedFormats: {
    video: string[];
    audio: string[];
    image: string[];
  };
  processingLimits: {
    maxFileSize: number;
    maxDuration: number;
    maxResolution: string;
  };
}