import { invoke } from '@tauri-apps/api/core';
import { 
  ContentAnalysis, 
  AIJob
} from './types';
import { AIProviderFactory } from './AIProviderFactory';
import { AISettings, DEFAULT_AI_SETTINGS } from '../config/aiConfig';
import { Store } from '@tauri-apps/plugin-store';

let settingsStore: Store;

export interface AnalysisOptions {
  enableTranscription?: boolean;
  enableSceneDetection?: boolean;
  enableObjectDetection?: boolean;
  enableFaceDetection?: boolean;
  enableSentimentAnalysis?: boolean;
  enablePlatformOptimization?: boolean;
  targetPlatforms?: string[];
  forceProvider?: string; // Override default provider for specific analysis
}

export class MultiModalContentAnalyzer {
  private static instance: MultiModalContentAnalyzer;
  private analysisCache: Map<string, ContentAnalysis> = new Map();
  private activeJobs: Map<string, AIJob> = new Map();
  private settings: AISettings = DEFAULT_AI_SETTINGS;

  private constructor() {
    this.initialize();
  }

  private async initialize() {
    // Initialize with defaults first
    AIProviderFactory.initialize(DEFAULT_AI_SETTINGS);
    
    // Try to load store and settings later
    setTimeout(async () => {
      try {
        settingsStore = await Store.load('ai-settings.json');
        const saved = await settingsStore.get<AISettings>('ai-settings');
        if (saved) {
          this.settings = saved;
          AIProviderFactory.initialize(saved);
        }
      } catch (error) {
        console.log('Using default AI settings (store not available)');
      }
    }, 1000);
  }

  static getInstance(): MultiModalContentAnalyzer {
    if (!MultiModalContentAnalyzer.instance) {
      MultiModalContentAnalyzer.instance = new MultiModalContentAnalyzer();
      console.log('MultiModalContentAnalyzer initialized with settings:', DEFAULT_AI_SETTINGS);
    }
    return MultiModalContentAnalyzer.instance;
  }

  /**
   * Analyze content using the configured multi-modal AI system
   */
  async analyzeContent(
    filePath: string, 
    options?: AnalysisOptions
  ): Promise<ContentAnalysis> {
    console.log('Analyzing content:', { filePath, options, settings: this.settings });
    
    const cacheKey = `${filePath}_${JSON.stringify(options)}`;
    
    // Check cache first
    if (this.analysisCache.has(cacheKey)) {
      console.log('Returning cached analysis');
      return this.analysisCache.get(cacheKey)!;
    }

    const jobId = `job_${Date.now()}`;
    const job: AIJob = {
      id: jobId,
      type: 'content_analysis',
      status: 'processing',
      priority: 'normal',
      inputPath: filePath,
      options: options || {},
      progress: 0,
      startedAt: new Date()
    };

    this.activeJobs.set(jobId, job);

    try {
      console.log('Using AIProviderFactory to perform analysis...');
      
      // Use the AI Provider Factory for flexible processing
      const result = await AIProviderFactory.performAnalysis(filePath, {
        ...options,
        settings: this.settings
      });
      
      console.log('Analysis result from factory:', result);

      // Convert to ContentAnalysis format
      const analysis: ContentAnalysis = {
        id: `analysis_${Date.now()}`,
        filePath,
        fileName: filePath.split('/').pop() || 'unknown',
        fileType: this.detectFileType(filePath),
        duration: result.analysis?.duration,
        analysisDate: new Date(),
        scenes: result.scenes || [],
        transcript: result.transcript || [],
        sentiment: result.analysis?.sentiment,
        topics: result.analysis?.topics || [],
        virality: result.virality,
        platformSuggestions: result.analysis?.platformSuggestions || [],
        videoQuality: result.analysis?.videoQuality,
        audioQuality: result.analysis?.audioQuality,
      };

      // Update job status
      job.status = 'completed';
      job.completedAt = new Date();
      job.result = analysis;

      // Cache the result
      this.analysisCache.set(cacheKey, analysis);

      return analysis;
    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Analysis failed';
      
      // Always fallback to local analysis on error
      console.log('Error occurred, falling back to local analysis:', error);
      return this.performLocalAnalysis(filePath, options);
    } finally {
      // Clean up job after a delay
      setTimeout(() => {
        this.activeJobs.delete(jobId);
      }, 60000);
    }
  }

  /**
   * Perform local-only analysis using FFmpeg
   */
  private async performLocalAnalysis(
    filePath: string,
    _options?: AnalysisOptions
  ): Promise<ContentAnalysis> {
    const metadata = await invoke<any>('get_media_metadata', { filePath });
    const scenes = await invoke<any[]>('detect_scenes_local', { filePath });
    
    return {
      id: `analysis_${Date.now()}`,
      filePath,
      fileName: filePath.split('/').pop() || 'unknown',
      fileType: this.detectFileType(filePath),
      duration: metadata.duration,
      analysisDate: new Date(),
      scenes: scenes.map(s => ({
        startTime: s.timestamp,
        endTime: s.timestamp + 5, // Estimate
        sceneType: 'transition' as const,
        description: 'Scene change detected',
        keyframes: [],
        mood: undefined,
        pace: 'medium'
      })),
      transcript: [],
      topics: [],
      platformSuggestions: [],
    };
  }

  /**
   * Get analysis progress for a specific job
   */
  getJobStatus(jobId: string): AIJob | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Get all active analysis jobs
   */
  getActiveJobs(): AIJob[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Clear analysis cache
   */
  clearCache(): void {
    this.analysisCache.clear();
  }

  /**
   * Update AI settings
   */
  async updateSettings(settings: AISettings): Promise<void> {
    this.settings = settings;
    AIProviderFactory.initialize(settings);
    await settingsStore.set('ai-settings', settings);
    await settingsStore.save();
  }

  /**
   * Get current AI settings
   */
  getSettings(): AISettings {
    return this.settings;
  }

  /**
   * Check if a specific feature is available with current settings
   */
  isFeatureAvailable(feature: string): boolean {
    const provider = this.settings.providers[feature];
    return Boolean(provider && provider !== 'none');
  }

  /**
   * Get cost estimate for analyzing a file
   */
  async estimateCost(filePath: string, options?: AnalysisOptions): Promise<{
    estimatedCost: number;
    breakdown: { feature: string; provider: string; cost: number }[];
  }> {
    const metadata = await invoke<any>('get_media_metadata', { filePath });
    const duration = metadata.duration || 0;
    const durationMinutes = duration / 60;

    const breakdown = [];
    let totalCost = 0;

    // Estimate costs based on providers and features
    if (options?.enableTranscription && this.isFeatureAvailable('transcription')) {
      const provider = this.settings.providers.transcription;
      let cost = 0;
      
      switch (provider) {
        case 'openai':
          cost = durationMinutes * 0.006; // $0.006/minute
          break;
        case 'qwen':
          cost = 0; // Free tier
          break;
        case 'deepseek':
          cost = durationMinutes * 0.002; // Estimate
          break;
      }
      
      breakdown.push({ feature: 'transcription', provider, cost });
      totalCost += cost;
    }

    if (options?.enablePlatformOptimization) {
      const provider = this.settings.providers.contentAnalysis;
      let cost = 0;
      
      switch (provider) {
        case 'openai':
          cost = 0.03; // ~1000 tokens
          break;
        case 'anthropic':
          cost = 0.015; // ~1000 tokens
          break;
        case 'deepseek':
          cost = 0.0003; // ~1000 tokens
          break;
        case 'groq':
          cost = 0; // Free tier
          break;
      }
      
      breakdown.push({ feature: 'analysis', provider, cost });
      totalCost += cost;
    }

    return {
      estimatedCost: totalCost,
      breakdown
    };
  }

  private detectFileType(filePath: string): 'video' | 'audio' | 'image' {
    const ext = filePath.split('.').pop()?.toLowerCase() || '';
    
    if (['mp4', 'mkv', 'avi', 'mov', 'webm'].includes(ext)) {
      return 'video';
    } else if (['mp3', 'wav', 'flac', 'aac', 'm4a'].includes(ext)) {
      return 'audio';
    } else {
      return 'image';
    }
  }
}

// Export singleton instance
export const multiModalAnalyzer = MultiModalContentAnalyzer.getInstance();