/**
 * OpenAI Realtime API Provider
 * Handles real-time voice interactions, live transcription, and voice synthesis
 */

import { 
  AIProvider, 
  GenerationOptions, 
  GenerationResult, 
  AICapability,
  ModelInfo 
} from '../gateway/types';

export class OpenAIRealtimeProvider implements AIProvider {
  name = 'openai_realtime';
  private apiKey: string = '';
  private realtimeKey: string = '';
  private ws: WebSocket | null = null;
  private sessionId: string | null = null;
  
  constructor(config?: any) {
    if (config) {
      this.initialize(config);
    }
  }

  async initialize(config: any): Promise<void> {
    this.apiKey = config.apiKey;
    this.realtimeKey = config.realtimeKey || config.apiKey;
  }

  async generate(prompt: string, options: GenerationOptions): Promise<GenerationResult> {
    switch (options.type) {
      case 'realtime_transcription':
        return this.handleTranscription(prompt, options);
      case 'voice_synthesis':
        return this.handleVoiceSynthesis(prompt, options);
      default:
        throw new Error(`Unsupported generation type: ${options.type}`);
    }
  }

  async generateStream(
    prompt: string, 
    options: GenerationOptions,
    onProgress: (partial: Partial<GenerationResult>) => void
  ): Promise<GenerationResult> {
    if (options.type === 'realtime_transcription') {
      return this.streamTranscription(prompt, options, onProgress);
    }
    
    return this.generate(prompt, options);
  }

  private async handleTranscription(
    audioData: string, 
    options: GenerationOptions
  ): Promise<GenerationResult> {
    try {
      // Connect to WebSocket if not connected
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        await this.connectWebSocket();
      }

      // Send audio data for transcription
      const response = await this.sendRealtimeRequest({
        type: 'input_audio_buffer.append',
        audio: audioData
      });

      return {
        success: true,
        data: response.transcript,
        metadata: {
          model: 'whisper-1',
          language: response.language
        },
        processingTime: response.latency,
        cost: 0.006 // $0.006 per minute
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Transcription failed'
      };
    }
  }

  private async handleVoiceSynthesis(
    text: string,
    options: GenerationOptions
  ): Promise<GenerationResult> {
    try {
      const response = await fetch('https://api.openai.com/v1/audio/speech', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'tts-1-hd',
          input: text,
          voice: options.voice || 'alloy',
          response_format: options.format || 'mp3'
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.statusText}`);
      }

      const audioBuffer = await response.arrayBuffer();
      
      return {
        success: true,
        data: audioBuffer,
        metadata: {
          format: options.format || 'mp3',
          model: 'tts-1-hd',
          voice: options.voice || 'alloy'
        },
        cost: text.length * 0.00003, // $0.030 per 1K characters
        processingTime: Date.now() - (response.headers.get('x-request-start') as any)
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Voice synthesis failed'
      };
    }
  }

  private async streamTranscription(
    audioStream: string,
    _options: GenerationOptions,
    onProgress: (partial: Partial<GenerationResult>) => void
  ): Promise<GenerationResult> {
    return new Promise((resolve, reject) => {
      let fullTranscript = '';
      let startTime = Date.now();

      this.connectWebSocket().then(() => {
        if (!this.ws) {
          reject(new Error('WebSocket connection failed'));
          return;
        }

        this.ws.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          if (data.type === 'response.audio_transcript.delta') {
            fullTranscript += data.delta;
            onProgress({
              data: fullTranscript,
              metadata: { partial: true }
            });
          } else if (data.type === 'response.audio_transcript.done') {
            resolve({
              success: true,
              data: fullTranscript,
              metadata: {
                model: 'whisper-1',
                language: data.language
              },
              processingTime: Date.now() - startTime,
              cost: ((Date.now() - startTime) / 60000) * 0.006
            });
          } else if (data.type === 'error') {
            reject(new Error(data.error.message));
          }
        };

        // Send audio stream
        this.ws.send(JSON.stringify({
          type: 'input_audio_buffer.append',
          audio: audioStream
        }));
      });
    });
  }

  private async connectWebSocket(): Promise<void> {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    return new Promise((resolve, reject) => {
      // Note: Browser WebSocket doesn't support custom headers
      // Authentication is done via URL parameter
      this.ws = new WebSocket(`wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01&api_key=${this.realtimeKey}`);

      this.ws.onopen = () => {
        console.log('OpenAI Realtime WebSocket connected');
        resolve();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.ws = null;
        this.sessionId = null;
      };
    });
  }

  private async sendRealtimeRequest(request: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      const requestId = Math.random().toString(36).substring(7);
      
      const handleResponse = (event: MessageEvent) => {
        const data = JSON.parse(event.data);
        if (data.request_id === requestId) {
          this.ws?.removeEventListener('message', handleResponse);
          resolve(data);
        }
      };

      this.ws.addEventListener('message', handleResponse);
      this.ws.send(JSON.stringify({ ...request, request_id: requestId }));

      // Timeout after 30 seconds
      setTimeout(() => {
        this.ws?.removeEventListener('message', handleResponse);
        reject(new Error('Request timeout'));
      }, 30000);
    });
  }

  async testConnection(): Promise<void> {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`
      }
    });

    if (!response.ok) {
      throw new Error('Invalid OpenAI API key');
    }
  }

  getSpeed(): number {
    return 9; // Very fast for realtime
  }

  getQuality(): number {
    return 9; // High quality
  }

  getCost(): number {
    return 6; // Medium-high cost
  }

  supportsCapability(capability: AICapability): boolean {
    const supported: AICapability[] = [
      'realtime_transcription',
      'voice_synthesis',
      'live_translation',
      'realtime_processing'
    ];
    return supported.includes(capability);
  }

  async getAvailableModels(): Promise<ModelInfo[]> {
    return [
      {
        id: 'whisper-1',
        name: 'Whisper',
        description: 'OpenAI\'s automatic speech recognition model',
        type: ['realtime_transcription'],
        costPerRequest: 0.006,
        speed: 'fast',
        quality: 'high'
      },
      {
        id: 'tts-1',
        name: 'Text-to-Speech',
        description: 'Standard quality text-to-speech',
        type: ['voice_synthesis'],
        costPerRequest: 0.015,
        speed: 'fast',
        quality: 'medium'
      },
      {
        id: 'tts-1-hd',
        name: 'Text-to-Speech HD',
        description: 'High quality text-to-speech',
        type: ['voice_synthesis'],
        costPerRequest: 0.030,
        speed: 'medium',
        quality: 'high'
      },
      {
        id: 'gpt-4o-realtime-preview',
        name: 'GPT-4 Realtime',
        description: 'Realtime voice conversation model',
        type: ['realtime_transcription', 'voice_synthesis'],
        costPerRequest: 0.06,
        speed: 'fast',
        quality: 'high'
      }
    ];
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.sessionId = null;
    }
  }
}