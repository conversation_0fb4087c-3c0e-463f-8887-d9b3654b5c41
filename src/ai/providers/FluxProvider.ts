/**
 * Flux Provider - Black Forest Labs
 * High-quality image generation with extended context understanding
 */

import { 
  AIProvider, 
  GenerationOptions, 
  GenerationResult, 
  AICapability,
  ModelInfo 
} from '../gateway/types';
import { invoke } from '@tauri-apps/api/core';
import { getProviderConfig, getModelConfig, calculateCost } from '../config/providerConfig';

export class FluxProvider implements AIProvider {
  name = 'flux';
  private apiKey: string = '';
  private baseUrl: string;
  private config = getProviderConfig('flux')!;
  
  constructor(config?: any) {
    this.baseUrl = this.config.baseUrl;
    if (config) {
      this.initialize(config);
    }
  }

  async initialize(config: any): Promise<void> {
    this.apiKey = config.apiKey;
  }

  async generate(prompt: string, options: GenerationOptions): Promise<GenerationResult> {
    try {
      const model = this.selectModel(options);
      const startTime = Date.now();

      const response = await this.callFluxAPI(model, prompt, options);
      
      if (!response.success) {
        throw new Error(response.error || 'Generation failed');
      }

      // Download the generated image
      const imageData = await this.downloadImage(response.result.sample);
      
      // Save locally using Tauri
      const localPath = await this.saveImageLocally(imageData, options.format || 'png');

      return {
        success: true,
        data: imageData,
        url: response.result.sample,
        localPath,
        metadata: {
          width: options.width || 1024,
          height: options.height || 1024,
          format: options.format || 'png',
          model,
          seed: response.result.seed
        },
        cost: this.calculateCost(model, options),
        processingTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Generation failed'
      };
    }
  }

  private selectModel(options: GenerationOptions): string {
    if (options.model) return options.model;

    // Select based on requirements using config
    if (options.quality === 'high' && !options.realtime) {
      return this.config.models.pro.id;
    } else if (options.realtime || options.quality === 'low') {
      return this.config.models.schnell.id;
    } else {
      return this.config.models.dev.id;
    }
  }

  private async callFluxAPI(
    model: string, 
    prompt: string, 
    options: GenerationOptions
  ): Promise<any> {
    const modelConfig = Object.values(this.config.models).find(m => m.id === model);
    const endpoint = modelConfig?.endpoint || `/v1/${model}`;
    
    const body: any = {
      prompt,
      width: options.width || this.config.defaults.width,
      height: options.height || this.config.defaults.height,
      steps: options.numInferenceSteps || modelConfig?.defaults?.steps || this.config.defaults.steps,
      guidance: options.guidanceScale || this.config.defaults.guidanceScale,
      safety_tolerance: this.config.defaults.safetyTolerance,
      output_format: options.format || 'png'
    };

    if (options.seed) {
      body.seed = options.seed;
    }

    if (model === this.config.models.pro.id) {
      // Pro model supports additional features
      body.prompt_upsampling = this.config.defaults.promptUpsampling;
    }

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Key': this.apiKey
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Flux API error: ${error}`);
    }

    return response.json();
  }

  private async downloadImage(url: string): Promise<ArrayBuffer> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to download generated image');
    }
    return response.arrayBuffer();
  }

  private async saveImageLocally(
    imageData: ArrayBuffer, 
    format: string
  ): Promise<string> {
    try {
      // Use Tauri to save the file
      const timestamp = Date.now();
      const filename = `flux_${timestamp}.${format}`;
      
      const localPath = await invoke<string>('save_generated_image', {
        imageData: Array.from(new Uint8Array(imageData)),
        filename
      });
      
      return localPath;
    } catch (error) {
      console.error('Failed to save image locally:', error);
      return '';
    }
  }

  private calculateCost(model: string, options: GenerationOptions): number {
    return calculateCost('flux', model, options);
  }

  private calculateCostOld(model: string, options: GenerationOptions): number {
    const baseCosts = {
      'flux-schnell': 0.003, // $0.003 per image
      'flux-dev': 0.025,     // $0.025 per image
      'flux-pro-1.1': 0.05   // $0.05 per image
    };

    let cost = baseCosts[model as keyof typeof baseCosts] || 0.025;

    // Additional costs for higher resolution
    const pixels = (options.width || 1024) * (options.height || 1024);
    if (pixels > 1024 * 1024) {
      cost *= 1.5; // 50% more for high resolution
    }

    return cost;
  }

  async generateStream(
    prompt: string, 
    options: GenerationOptions,
    onProgress: (partial: Partial<GenerationResult>) => void
  ): Promise<GenerationResult> {
    // Flux doesn't support streaming, but we can simulate progress
    onProgress({ metadata: { status: 'Starting generation...' } });
    
    const result = await this.generate(prompt, options);
    
    onProgress({ metadata: { status: 'Complete' } });
    
    return result;
  }

  async testConnection(): Promise<void> {
    const response = await fetch(`${this.baseUrl}/v1/get_key_info`, {
      headers: {
        'X-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error('Invalid Flux API key');
    }
  }

  getSpeed(): number {
    return this.config.speed;
  }

  getQuality(): number {
    return this.config.quality;
  }

  getCost(): number {
    return this.config.costs.base;
  }

  supportsCapability(capability: AICapability): boolean {
    return this.config.capabilities.includes(capability);
  }

  async getAvailableModels(): Promise<ModelInfo[]> {
    return Object.entries(this.config.models).map(([key, model]) => ({
      id: model.id,
      name: model.name,
      description: key === 'schnell' ? 'Fastest model, 4 steps, good for real-time' :
                   key === 'dev' ? 'Balanced quality and speed' :
                   'Highest quality, slower generation',
      type: ['text_to_image'],
      maxResolution: { width: 2048, height: 2048 },
      costPerRequest: model.cost || 0,
      speed: key === 'schnell' ? 'fast' : key === 'dev' ? 'medium' : 'slow',
      quality: key === 'schnell' ? 'medium' : 'high'
    }));
  }
}