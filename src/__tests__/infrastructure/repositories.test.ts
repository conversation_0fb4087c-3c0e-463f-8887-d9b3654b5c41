import { describe, it, expect, beforeEach } from 'vitest';
import { InMemoryDownloadRepository } from '../../infrastructure/repositories/InMemoryDownloadRepository';
import { Download, DownloadStatus } from '../../domain/models';

describe('InMemoryDownloadRepository', () => {
  let repository: InMemoryDownloadRepository;
  let download1: Download;
  let download2: Download;

  beforeEach(() => {
    repository = new InMemoryDownloadRepository();
    download1 = Download.create('https://example.com/file1.mp4', '/downloads/file1.mp4', '1080p');
    download2 = Download.create('https://example.com/file2.mp4', '/downloads/file2.mp4', '720p');
  });

  it('should save and find download by ID', async () => {
    await repository.save(download1);
    
    const found = await repository.findById(download1.getId());
    expect(found).toBe(download1);
  });

  it('should return null for non-existent download', async () => {
    const found = await repository.findById(download1.getId());
    expect(found).toBeNull();
  });

  it('should find all downloads', async () => {
    await repository.save(download1);
    await repository.save(download2);
    
    const all = await repository.findAll();
    expect(all).toHaveLength(2);
    expect(all).toContain(download1);
    expect(all).toContain(download2);
  });

  it('should find downloads by status', async () => {
    download1.start();
    download2.start();
    download1.complete();
    
    await repository.save(download1);
    await repository.save(download2);
    
    const completed = await repository.findByStatus(DownloadStatus.COMPLETED);
    const downloading = await repository.findByStatus(DownloadStatus.DOWNLOADING);
    
    expect(completed).toHaveLength(1);
    expect(completed[0]).toBe(download1);
    expect(downloading).toHaveLength(1);
    expect(downloading[0]).toBe(download2);
  });

  it('should delete download by ID', async () => {
    await repository.save(download1);
    await repository.save(download2);
    
    await repository.delete(download1.getId());
    
    const remaining = await repository.findAll();
    expect(remaining).toHaveLength(1);
    expect(remaining[0]).toBe(download2);
  });
});
