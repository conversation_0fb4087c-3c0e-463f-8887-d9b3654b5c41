import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DownloadApplicationService, EventEmitter } from '../../application/services/DownloadApplicationService';
import { InMemoryDownloadRepository } from '../../infrastructure/repositories/InMemoryDownloadRepository';
import { MockDownloadEngine } from '../../infrastructure/DownloadEngine';
import { DownloadStatus } from '../../domain/models';

describe('DownloadApplicationService', () => {
  let service: DownloadApplicationService;
  let repository: InMemoryDownloadRepository;
  let downloadEngine: MockDownloadEngine;
  let eventEmitter: EventEmitter;

  beforeEach(() => {
    repository = new InMemoryDownloadRepository();
    downloadEngine = new MockDownloadEngine();
    eventEmitter = { emit: vi.fn() };
    
    service = new DownloadApplicationService(repository, eventEmitter, downloadEngine);
  });

  it('should create download successfully', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    expect(downloadId).toBeDefined();
    
    const downloads = await repository.findAll();
    expect(downloads).toHaveLength(1);
    expect(downloads[0].getStatus()).toBe(DownloadStatus.DOWNLOADING);
  });

  it('should reject invalid download requests', async () => {
    await expect(service.createDownload('', 'video.mp4', '1080p', '/downloads'))
      .rejects.toThrow('URL cannot be empty');
    
    await expect(service.createDownload('https://example.com/video.mp4', '', '1080p', '/downloads'))
      .rejects.toThrow('Filename cannot be empty');
  });

  it('should enforce concurrent download limit', async () => {
    // Create 3 downloads (max limit)
    await service.createDownload('https://example.com/video1.mp4', 'video1.mp4', '1080p', '/downloads');
    await service.createDownload('https://example.com/video2.mp4', 'video2.mp4', '1080p', '/downloads');
    await service.createDownload('https://example.com/video3.mp4', 'video3.mp4', '1080p', '/downloads');

    // 4th download should fail
    await expect(service.createDownload('https://example.com/video4.mp4', 'video4.mp4', '1080p', '/downloads'))
      .rejects.toThrow('Maximum number of concurrent downloads reached');
  });

  it('should pause and resume downloads', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    await service.pauseDownload(downloadId);
    
    const downloads = await repository.findAll();
    expect(downloads[0].getStatus()).toBe(DownloadStatus.PAUSED);

    await service.resumeDownload(downloadId);
    expect(downloads[0].getStatus()).toBe(DownloadStatus.DOWNLOADING);
  });

  it('should handle download progress updates', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    await service.handleDownloadProgress({
      downloadId,
      bytesDownloaded: 50,
      totalBytes: 100,
      speedBytesPerSecond: 1000
    });

    const downloads = await repository.findAll();
    expect(downloads[0].getProgress().getPercentage()).toBe(50);
    expect(eventEmitter.emit).toHaveBeenCalledWith({
      type: 'progress',
      data: expect.objectContaining({ downloadId })
    });
  });

  it('should clear completed downloads', async () => {
    const downloadId1 = await service.createDownload(
      'https://example.com/video1.mp4',
      'video1.mp4',
      '1080p',
      '/downloads'
    );
    const downloadId2 = await service.createDownload(
      'https://example.com/video2.mp4',
      'video2.mp4',
      '1080p',
      '/downloads'
    );

    // Complete first download
    await service.handleDownloadCompleted({
      downloadId: downloadId1,
      filePath: '/downloads/video1.mp4'
    });

    await service.clearCompletedDownloads();

    const remaining = await repository.findAll();
    expect(remaining).toHaveLength(1);
    expect(remaining[0].getId().toString()).toBe(downloadId2);
  });
});
