import { describe, it, expect } from 'vitest';
import { DownloadAdapter } from '../../adapters/DownloadAdapter';
import { Download as DomainDownload } from '../../domain/models';

describe('DownloadAdapter', () => {
  it('should convert legacy downloads to domain entities', () => {
    const legacyDownload = {
      id: 'test_123',
      url: 'https://example.com/video.mp4',
      filename: 'video.mp4',
      quality: '1080p',
      downloadPath: '/downloads',
      progress: 50,
      status: 'downloading' as const,
      createdAt: new Date(),
      bytesDownloaded: 50,
      bytesTotal: 100,
      downloadSpeed: '1.5 MB/s'
    };

    const domain = DownloadAdapter.toDomain(legacyDownload);
    
    expect(domain.getUrl().getValue()).toBe(legacyDownload.url);
    expect(domain.getProgress().getPercentage()).toBe(50);
    expect(domain.getStatus()).toBe('downloading');
  });

  it('should convert domain entities back to legacy format', () => {
    const domain = DomainDownload.create(
      'https://example.com/video.mp4',
      '/downloads/video.mp4',
      '1080p'
    );
    
    domain.start();
    domain.updateProgress(50, 100, 1572864); // 1.5 MB/s

    const legacy = DownloadAdapter.toLegacy(domain);
    
    expect(legacy.url).toBe('https://example.com/video.mp4');
    expect(legacy.progress).toBe(50);
    expect(legacy.status).toBe('downloading');
    expect(legacy.downloadSpeed).toBe('1.5 MB/s');
  });

  it('should maintain data integrity during round-trip conversion', () => {
    const originalLegacy = {
      id: 'test_123',
      url: 'https://example.com/video.mp4',
      filename: 'video.mp4',
      quality: '1080p',
      downloadPath: '/downloads',
      progress: 75,
      status: 'completed' as const,
      createdAt: new Date(),
      completedAt: new Date(),
      bytesDownloaded: 75,
      bytesTotal: 100
    };

    const domain = DownloadAdapter.toDomain(originalLegacy);
    const convertedLegacy = DownloadAdapter.toLegacy(domain);

    expect(convertedLegacy.url).toBe(originalLegacy.url);
    expect(convertedLegacy.status).toBe(originalLegacy.status);
    expect(convertedLegacy.progress).toBe(originalLegacy.progress);
  });
});
