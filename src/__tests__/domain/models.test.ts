import { describe, it, expect, beforeEach } from 'vitest';
import { 
  Download, 
  DownloadId, 
  DownloadUrl, 
  DownloadStatus
} from '../../domain/models';

describe('DownloadId', () => {
  it('should create unique IDs', () => {
    const id1 = DownloadId.create();
    const id2 = DownloadId.create();
    
    expect(id1.toString()).not.toBe(id2.toString());
  });

  it('should create from string', () => {
    const idString = 'download_123_abc';
    const id = DownloadId.fromString(idString);
    
    expect(id.toString()).toBe(idString);
  });

  it('should throw error for empty ID', () => {
    expect(() => DownloadId.fromString('')).toThrow('DownloadId cannot be empty');
  });
});

describe('DownloadUrl', () => {
  it('should accept valid HTTP URLs', () => {
    const url = DownloadUrl.create('https://example.com/file.mp4');
    expect(url.getValue()).toBe('https://example.com/file.mp4');
  });

  it('should accept social media URLs', () => {
    const url = DownloadUrl.create('https://youtube.com/watch?v=test');
    expect(url.isSocialMedia()).toBe(true);
    expect(url.requiresExternalTool()).toBe(true);
  });

  it('should reject invalid URLs', () => {
    expect(() => DownloadUrl.create('')).toThrow('URL cannot be empty');
    expect(() => DownloadUrl.create('invalid-url')).toThrow('Invalid URL format');
  });
});

describe('Download Entity', () => {
  let download: Download;

  beforeEach(() => {
    download = Download.create(
      'https://example.com/video.mp4',
      '/downloads/video.mp4',
      '1080p'
    );
  });

  it('should create download with correct initial state', () => {
    expect(download.getStatus()).toBe(DownloadStatus.PENDING);
    expect(download.getProgress().getPercentage()).toBe(0);
    expect(download.getQuality()).toBe('1080p');
  });

  it('should transition from pending to downloading', () => {
    download.start();
    expect(download.getStatus()).toBe(DownloadStatus.DOWNLOADING);
  });

  it('should enforce business rules for state transitions', () => {
    // Cannot start non-pending download
    download.start();
    expect(() => download.start()).toThrow('Cannot start download in downloading status');
  });
});
