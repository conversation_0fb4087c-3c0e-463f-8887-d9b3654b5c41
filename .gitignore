# Node modules
node_modules/
dist/
.pnp
.pnp.js

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-project
*.sublime-workspace

# Build artifacts
/public/build
/target
/.vite
/.cache
/.turbo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated
.DS_Store
Thumbs.db
ehthumbs_vista.db
Desktop.ini
$RECYCLE.BIN/

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Misc
.vercel
.next
.docusaurus
.turbo
.husky
.commitlintrc.yml

# Tauri
/src-tauri/target
/src-tauri/Cargo.lock
/src-tauri/Downloads/

# Salesforce
.sfdx/ 
# Environment variables
.env
.env.local
