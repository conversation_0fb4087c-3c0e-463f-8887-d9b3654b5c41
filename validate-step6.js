#!/usr/bin/env node

/**
 * Step 6 Validation Script
 * Tests the enhanced download store and migration functionality
 */

console.log('🧪 Step 6 Implementation Validation');
console.log('==================================\n');

// Validation checklist
const validationResults = [];

// Check 1: Verify all domain layer files exist
const domainFiles = [
  'src/domain/models.ts',
  'src/domain/repositories.ts',
  'src/domain/services/DownloadDomainService.ts',
  'src/domain/index.ts'
];

// Check 2: Verify infrastructure layer files exist
const infrastructureFiles = [
  'src/infrastructure/repositories/InMemoryDownloadRepository.ts',
  'src/infrastructure/repositories/LocalStorageDownloadRepository.ts',
  'src/infrastructure/repositories/TauriDownloadRepository.ts',
  'src/infrastructure/repositories/index.ts',
  'src/infrastructure/DownloadEngine.ts'
];

// Check 3: Verify adapter layer files exist
const adapterFiles = [
  'src/adapters/DownloadAdapter.ts',
  'src/adapters/FeatureFlags.ts'
];

// Check 4: Verify application layer files exist
const applicationFiles = [
  'src/application/services/DownloadApplicationService.ts',
  'src/application/services/ApplicationServiceFactory.ts'
];

// Check 5: Verify enhanced store and migration components
const migrationFiles = [
  'src/store/enhancedDownloadStore.ts',
  'src/components/migration/MigrationControl.tsx',
  'src/components/migration/MigrationErrorBoundary.tsx'
];

// Check 6: Verify test files exist
const testFiles = [
  'src/__tests__/domain/models.test.ts',
  'src/__tests__/infrastructure/repositories.test.ts',
  'src/__tests__/migration/adapter.test.ts',
  'src/__tests__/application/DownloadApplicationService.test.ts'
];

import fs from 'fs';
import path from 'path';

function checkFiles(files, category) {
  console.log(`📁 Checking ${category} files:`);
  let allExist = true;
  
  for (const file of files) {
    const exists = fs.existsSync(path.resolve(file));
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allExist = false;
  }
  
  validationResults.push({ category, passed: allExist });
  console.log();
  return allExist;
}

// Run all checks
checkFiles(domainFiles, 'Domain Layer');
checkFiles(infrastructureFiles, 'Infrastructure Layer');
checkFiles(adapterFiles, 'Adapter Layer');
checkFiles(applicationFiles, 'Application Layer');
checkFiles(migrationFiles, 'Migration Components');
checkFiles(testFiles, 'Test Files');

// Check package.json for test scripts
console.log('📦 Checking package.json test scripts:');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredScripts = ['test:domain', 'test:infrastructure', 'test:migration', 'test:application'];
let scriptsExist = true;

for (const script of requiredScripts) {
  const exists = packageJson.scripts && packageJson.scripts[script];
  console.log(`  ${exists ? '✅' : '❌'} ${script}`);
  if (!exists) scriptsExist = false;
}

validationResults.push({ category: 'Test Scripts', passed: scriptsExist });
console.log();

// Summary
console.log('📊 Validation Summary:');
console.log('===================');

let allPassed = true;
for (const result of validationResults) {
  console.log(`${result.passed ? '✅' : '❌'} ${result.category}: ${result.passed ? 'PASSED' : 'FAILED'}`);
  if (!result.passed) allPassed = false;
}

console.log();
if (allPassed) {
  console.log('🎉 SUCCESS! Step 6 implementation is complete and ready for testing.');
  console.log();
  console.log('🚀 Next Steps:');
  console.log('1. Visit http://localhost:3456 to see the app');
  console.log('2. Click the Settings gear icon in the header');
  console.log('3. Go to the "Developer" tab (only visible in dev mode)');
  console.log('4. Use the Migration Control Panel to test domain layer features');
  console.log('5. Enable "Developer Mode" to make the tab persistent');
  console.log('6. Test the complete migration workflow');
  console.log();
  console.log('🧪 To run all tests: npm run test:domain && npm run test:infrastructure && npm run test:migration && npm run test:application');
} else {
  console.log('❌ FAILED! Some required files are missing. Please check the implementation.');
  process.exit(1);
}
