{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "strict": true, "noEmit": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "jsx": "react-jsx", "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["./src"], "exclude": ["./src-tauri"]}