# Download Optimization Guide

## Performance Improvements

### 1. Concurrent Fragment Downloading
- Downloads 4 fragments simultaneously for faster speeds
- Configurable via `--concurrent-fragments` parameter
- Significantly improves download speed for segmented videos

### 2. <PERSON><PERSON><PERSON> and <PERSON><PERSON> Size Optimization
- Increased buffer size to 16KB for smoother streaming
- HTTP chunk size set to 10MB for better throughput
- Reduces overhead and improves overall performance

### 3. Real-time Progress Monitoring
- Parses yt-dlp output in real-time
- Shows accurate download percentage, speed, and ETA
- Updates UI every time progress changes

## Video Codec Compatibility

### QuickTime Compatibility
To ensure downloaded videos play correctly in QuickTime Player:

1. **H.264 Codec Preference**
   - Format string prioritizes `avc1` (H.264) codec
   - Falls back to best available if H.264 not found
   - Ensures broad compatibility across devices

2. **Post-Processing Settings**
   - MP4 files: Uses `-movflags +faststart` for web optimization
   - MKV files: Converts to H.264 with AAC audio
   - Automatic format conversion for incompatible codecs

### Recommended Settings by Quality

| Quality | Video Codec | Audio Codec | Bitrate |
|---------|-------------|-------------|---------|
| 4K/2160p | H.264 (CRF 22) | AAC 256k | ~8-12 Mbps |
| 1080p | H.264 (CRF 22) | AAC 192k | ~4-6 Mbps |
| 720p | H.264 (CRF 23) | AAC 128k | ~2-3 Mbps |
| 480p | H.264 (CRF 23) | AAC 128k | ~1-2 Mbps |

## Required Dependencies

### macOS
```bash
# Install ffmpeg for video processing
brew install ffmpeg

# Install yt-dlp for downloading
brew install yt-dlp
```

### Windows
```powershell
# Using Chocolatey
choco install ffmpeg
choco install yt-dlp

# Or download manually from:
# ffmpeg: https://ffmpeg.org/download.html
# yt-dlp: https://github.com/yt-dlp/yt-dlp/releases
```

### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ffmpeg
sudo apt install yt-dlp

# Or use pip
pip install yt-dlp
```

## Troubleshooting

### "QuickTime Player cannot open files of this type"
This usually means the video codec is not supported. The app now automatically:
1. Prefers H.264 codec during download
2. Post-processes videos to ensure compatibility
3. Adds proper metadata for QuickTime

### Slow Download Speeds
1. Check your internet connection
2. Try a different video quality (lower quality = faster download)
3. Ensure yt-dlp is up to date: `yt-dlp -U`
4. The app now uses concurrent downloads for better speed

### Download Fails
1. Ensure yt-dlp is installed and accessible
2. Check if the URL is supported
3. Try updating yt-dlp: `yt-dlp -U`
4. Check error message for specific issues

## Advanced Configuration

You can modify download behavior in the constants file:
- `/src/config/constants.ts` - Frontend settings
- `/src-tauri/src/lib.rs` - Backend download parameters

Key parameters:
- `concurrent_fragments`: Number of simultaneous downloads (default: 4)
- `buffer_size`: Download buffer size (default: 16K)
- `http_chunk_size`: HTTP chunk size (default: 10M)
- `video_codec`: Preferred codec (default: H.264/avc1)