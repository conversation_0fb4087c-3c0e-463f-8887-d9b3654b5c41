// Test script for FlowDownload app
// This script will test downloading a file and verifying its persistence

const { invoke } = window.__TAURI__.core;

async function testDownload() {
  console.log('🧪 Starting download test...');
  
  // Test file URL - using a small test file from GitHub
  const testUrl = 'https://raw.githubusercontent.com/github/gitignore/main/Node.gitignore';
  const downloadId = 'test-download-' + Date.now();
  const fileName = 'test-node-gitignore.txt';
  const filePath = `/Users/<USER>/Downloads/${fileName}`;
  
  try {
    console.log('📥 Initiating download...');
    console.log('URL:', testUrl);
    console.log('File path:', filePath);
    console.log('Download ID:', downloadId);
    
    // Start the download
    await invoke('download_file', {
      id: downloadId,
      url: testUrl,
      filePath: filePath
    });
    
    console.log('✅ Download command sent successfully');
    
    // Listen for download events
    const unlisten = await window.__TAURI__.event.listen('download-progress', (event) => {
      console.log('📊 Download progress:', event.payload);
    });
    
    const unlistenComplete = await window.__TAURI__.event.listen('download-complete', (event) => {
      console.log('🎉 Download complete:', event.payload);
      // Clean up listeners
      unlisten();
      unlistenComplete();
      
      // Test opening the file
      testOpenFile(filePath);
    });
    
    const unlistenError = await window.__TAURI__.event.listen('download-error', (event) => {
      console.error('❌ Download error:', event.payload);
      // Clean up listeners
      unlisten();
      unlistenComplete();
      unlistenError();
    });
    
  } catch (error) {
    console.error('❌ Error initiating download:', error);
  }
}

async function testOpenFile(filePath) {
  console.log('🔍 Testing file open functionality...');
  
  try {
    // Check if file exists first
    const { exists } = await invoke('path_exists', { path: filePath });
    console.log('File exists:', exists);
    
    if (exists) {
      // Open the file in the default application
      console.log('📂 Opening file in default application...');
      await window.__TAURI__.shell.open(filePath);
      console.log('✅ File opened successfully');
      
      // Test persistence by storing download info
      testPersistence(filePath);
    } else {
      console.error('❌ File does not exist at path:', filePath);
    }
  } catch (error) {
    console.error('❌ Error opening file:', error);
  }
}

async function testPersistence(filePath) {
  console.log('💾 Testing download persistence...');
  
  // Store download info in localStorage (or your app's persistence layer)
  const downloadInfo = {
    id: 'test-download-' + Date.now(),
    url: 'https://raw.githubusercontent.com/github/gitignore/main/Node.gitignore',
    filePath: filePath,
    downloadedAt: new Date().toISOString(),
    status: 'completed'
  };
  
  // Get existing downloads from localStorage
  let downloads = JSON.parse(localStorage.getItem('test-downloads') || '[]');
  downloads.push(downloadInfo);
  localStorage.setItem('test-downloads', JSON.stringify(downloads));
  
  console.log('✅ Download info persisted to localStorage');
  console.log('📊 Total persisted downloads:', downloads.length);
  
  // Verify persistence by reading back
  console.log('🔄 Verifying persistence...');
  const persistedDownloads = JSON.parse(localStorage.getItem('test-downloads') || '[]');
  console.log('✅ Successfully retrieved', persistedDownloads.length, 'downloads from storage');
  console.log('Latest download:', persistedDownloads[persistedDownloads.length - 1]);
}

// Function to check persistence on app restart
async function checkPersistedDownloads() {
  console.log('🔍 Checking for persisted downloads...');
  
  const persistedDownloads = JSON.parse(localStorage.getItem('test-downloads') || '[]');
  console.log('Found', persistedDownloads.length, 'persisted downloads');
  
  for (const download of persistedDownloads) {
    console.log('📄 Download:', download);
    
    // Check if file still exists
    try {
      const { exists } = await invoke('path_exists', { path: download.filePath });
      console.log(`File ${download.filePath} exists:`, exists);
    } catch (error) {
      console.error('Error checking file:', error);
    }
  }
}

// Export functions for use in console
window.testDownload = testDownload;
window.checkPersistedDownloads = checkPersistedDownloads;

console.log('🧪 Test functions loaded!');
console.log('Run testDownload() to test downloading a file');
console.log('Run checkPersistedDownloads() to check persisted downloads');