#!/bin/bash

# <PERSON>ript to sign update packages for FlowDownload
# This ensures updates are authentic and haven't been tampered with

set -e

# Check if minisign is installed
if ! command -v minisign &> /dev/null; then
    echo "Error: minisign is not installed. Please install it first:"
    echo "  macOS: brew install minisign"
    echo "  Linux: apt-get install minisign"
    exit 1
fi

# Configuration
UPDATE_DIR="./dist"
PRIVATE_KEY_PATH="./keys/minisign.key"
PUBLIC_KEY_PATH="./keys/minisign.pub"

# Generate keys if they don't exist
if [ ! -f "$PRIVATE_KEY_PATH" ] || [ ! -f "$PUBLIC_KEY_PATH" ]; then
    echo "Generating new signing keys..."
    mkdir -p ./keys
    minisign -G -p "$PUBLIC_KEY_PATH" -s "$PRIVATE_KEY_PATH"
    echo "Keys generated. Public key:"
    cat "$PUBLIC_KEY_PATH"
    echo ""
    echo "IMPORTANT: Back up your private key and keep it secure!"
fi

# Function to sign a file
sign_file() {
    local file=$1
    echo "Signing $file..."
    minisign -S -s "$PRIVATE_KEY_PATH" -m "$file" -t "FlowDownload Update $(date +%Y%m%d)"
}

# Sign all update packages
echo "Signing update packages..."

# macOS
if [ -f "$UPDATE_DIR/FlowDownload.app.tar.gz" ]; then
    sign_file "$UPDATE_DIR/FlowDownload.app.tar.gz"
fi

# Windows
if [ -f "$UPDATE_DIR/FlowDownload-setup.exe" ]; then
    sign_file "$UPDATE_DIR/FlowDownload-setup.exe"
fi

# Linux
if [ -f "$UPDATE_DIR/FlowDownload.AppImage" ]; then
    sign_file "$UPDATE_DIR/FlowDownload.AppImage"
fi

echo "Update signing complete!"

# Generate update manifest
echo "Generating update manifest..."
node scripts/generate-manifest.js

echo "Done!"