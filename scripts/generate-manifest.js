const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

async function getFileHash(filePath) {
  const fileBuffer = await fs.readFile(filePath);
  const hashSum = crypto.createHash('sha256');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

async function getFileSize(filePath) {
  const stats = await fs.stat(filePath);
  return stats.size;
}

async function generateManifest() {
  const version = process.env.npm_package_version || '1.0.0';
  const distDir = path.join(__dirname, '..', 'dist');
  
  const manifest = {
    version,
    notes: `FlowDownload v${version} - See release notes for details`,
    pub_date: new Date().toISOString(),
    platforms: {}
  };

  // Platform configurations
  const platforms = [
    {
      name: 'darwin-x86_64',
      file: 'FlowDownload_x64.app.tar.gz',
      url: `https://github.com/flowdownload/releases/download/v${version}/FlowDownload_${version}_x64.app.tar.gz`
    },
    {
      name: 'darwin-aarch64',
      file: 'FlowDownload_aarch64.app.tar.gz',
      url: `https://github.com/flowdownload/releases/download/v${version}/FlowDownload_${version}_aarch64.app.tar.gz`
    },
    {
      name: 'windows-x86_64',
      file: 'FlowDownload-setup.exe',
      url: `https://github.com/flowdownload/releases/download/v${version}/FlowDownload_${version}_x64-setup.exe`
    },
    {
      name: 'linux-x86_64',
      file: 'FlowDownload.AppImage',
      url: `https://github.com/flowdownload/releases/download/v${version}/FlowDownload_${version}_amd64.AppImage`
    }
  ];

  // Process each platform
  for (const platform of platforms) {
    const filePath = path.join(distDir, platform.file);
    const sigPath = `${filePath}.minisig`;
    
    try {
      // Check if file exists
      await fs.access(filePath);
      
      // Get file size
      const size = await getFileSize(filePath);
      
      // Read signature if it exists
      let signature = '';
      try {
        signature = await fs.readFile(sigPath, 'utf8');
      } catch (e) {
        console.warn(`No signature found for ${platform.file}`);
      }
      
      manifest.platforms[platform.name] = {
        signature: signature.trim(),
        url: platform.url,
        size
      };
      
      console.log(`✓ Processed ${platform.name}`);
    } catch (e) {
      console.log(`✗ Skipping ${platform.name} - file not found`);
    }
  }

  // Write manifest
  const manifestPath = path.join(__dirname, '..', 'update-manifest.json');
  await fs.writeFile(
    manifestPath,
    JSON.stringify(manifest, null, 2)
  );
  
  console.log(`\nUpdate manifest generated: ${manifestPath}`);
  console.log(`Version: ${version}`);
  console.log(`Platforms: ${Object.keys(manifest.platforms).join(', ')}`);
}

// Run the script
generateManifest().catch(console.error);