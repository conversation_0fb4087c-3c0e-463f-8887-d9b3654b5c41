#!/usr/bin/env node

/**
 * Generate Tauri configuration dynamically from our constants
 * This ensures consistency between frontend and backend configuration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Import our configuration constants
// Note: We need to use a simple require since this is a Node.js script
const configPath = path.join(__dirname, '../src/config/constants.ts');
const configContent = fs.readFileSync(configPath, 'utf8');

// Extract DEFAULT_CONFIG values using regex (simple approach)
const extractConfigValue = (key) => {
  // Look for the key followed by a colon and capture the value
  const stringRegex = new RegExp(`${key}:\\s*['"]([^'"]+)['"]`, 'g');
  const stringMatch = stringRegex.exec(configContent);
  if (stringMatch) {
    return stringMatch[1].trim();
  }

  // Try numeric values
  const numRegex = new RegExp(`${key}:\\s*(\\d+)`, 'g');
  const numMatch = numRegex.exec(configContent);
  if (numMatch) {
    return numMatch[1].trim();
  }

  return null;
};

// Extract configuration values
const appName = extractConfigValue('name') || 'FlowDownload';
const appVersion = extractConfigValue('version') || '1.0.0';
const appIdentifier = extractConfigValue('identifier') || 'com.flowdownload.app';
const appTitle = extractConfigValue('title') || 'FlowDownload Desktop Pro';
const defaultWidth = extractConfigValue('defaultWidth') || '1280';
const defaultHeight = extractConfigValue('defaultHeight') || '800';
const minWidth = extractConfigValue('minWidth') || '800';
const minHeight = extractConfigValue('minHeight') || '600';
const devPort = extractConfigValue('devPort') || '3456';

// Generate Tauri configuration
const tauriConfig = {
  "$schema": "../node_modules/@tauri-apps/cli/config.schema.json",
  "productName": appName,
  "version": appVersion,
  "identifier": appIdentifier,
  "build": {
    "frontendDist": "../dist",
    "devUrl": `http://localhost:${devPort}`,
    "beforeDevCommand": "npm run dev",
    "beforeBuildCommand": "npm run build"
  },
  "app": {
    "windows": [
      {
        "title": appTitle,
        "width": parseInt(defaultWidth),
        "height": parseInt(defaultHeight),
        "minWidth": parseInt(minWidth),
        "minHeight": parseInt(minHeight),
        "resizable": true,
        "fullscreen": false,
        "center": true,
        "decorations": true
      }
    ],
    "security": {
      "csp": null
    }
  },
  "plugins": {
    "shell": {
      "open": true
    },
    "dialog": null,
    "fs": null,
    "path": null
  },
  "bundle": {
    "active": true,
    "targets": "all",
    "icon": [
      "icons/32x32.png",
      "icons/128x128.png",
      "icons/<EMAIL>",
      "icons/icon.icns",
      "icons/icon.ico"
    ],
    "resources": [
      "bin/*"
    ]
  }
};

// Write the configuration file
const outputPath = path.join(__dirname, '../src-tauri/tauri.conf.json');
fs.writeFileSync(outputPath, JSON.stringify(tauriConfig, null, 2));

console.log('✅ Tauri configuration generated successfully!');
console.log(`📝 Configuration written to: ${outputPath}`);
console.log(`🚀 App: ${appName} v${appVersion}`);
console.log(`🪟 Window: ${defaultWidth}x${defaultHeight} (min: ${minWidth}x${minHeight})`);
console.log(`🌐 Dev server: http://localhost:${devPort}`);
