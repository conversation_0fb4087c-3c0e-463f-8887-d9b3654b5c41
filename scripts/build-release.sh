#!/bin/bash

# Build script for FlowDownload releases
# Builds for all platforms and prepares them for distribution

set -e

echo "🚀 Building FlowDownload Release..."

# Get version from package.json
VERSION=$(node -p "require('./package.json').version")
echo "Version: $VERSION"

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf dist
rm -rf src-tauri/target/release/bundle

# Build frontend
echo "🏗️  Building frontend..."
npm run build

# Build Tauri app for all platforms
echo "🔨 Building Tauri app..."

# macOS Universal
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Building macOS universal binary..."
    npm run tauri build -- --target universal-apple-darwin
fi

# Windows
if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "Building Windows x64..."
    npm run tauri build -- --target x86_64-pc-windows-msvc
fi

# Linux
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Building Linux AppImage..."
    npm run tauri build -- --target x86_64-unknown-linux-gnu
fi

# Create dist directory
mkdir -p dist

# Copy built files to dist
echo "📦 Packaging builds..."

# macOS
if [ -d "src-tauri/target/release/bundle/macos" ]; then
    cp -r src-tauri/target/release/bundle/macos/*.app dist/
    cd dist
    tar -czf "FlowDownload_${VERSION}_universal.app.tar.gz" *.app
    rm -rf *.app
    cd ..
fi

# Windows
if [ -f "src-tauri/target/release/bundle/msi/*.msi" ]; then
    cp src-tauri/target/release/bundle/msi/*.msi "dist/FlowDownload_${VERSION}_x64-setup.msi"
fi
if [ -f "src-tauri/target/release/bundle/nsis/*.exe" ]; then
    cp src-tauri/target/release/bundle/nsis/*.exe "dist/FlowDownload_${VERSION}_x64-setup.exe"
fi

# Linux
if [ -f "src-tauri/target/release/bundle/appimage/*.AppImage" ]; then
    cp src-tauri/target/release/bundle/appimage/*.AppImage "dist/FlowDownload_${VERSION}_amd64.AppImage"
fi
if [ -f "src-tauri/target/release/bundle/deb/*.deb" ]; then
    cp src-tauri/target/release/bundle/deb/*.deb "dist/FlowDownload_${VERSION}_amd64.deb"
fi

# Sign the releases
echo "🔐 Signing releases..."
./scripts/sign-update.sh

# Generate checksums
echo "🔢 Generating checksums..."
cd dist
shasum -a 256 * > checksums.txt
cd ..

echo "✅ Build complete!"
echo "📁 Builds available in ./dist/"
ls -la dist/