#!/usr/bin/env node

/**
 * Test script to verify the downloads folder functionality works
 * This script can be run independently to test the enhanced folder utilities
 */

import { invoke } from '@tauri-apps/api/core';

async function testFolderCommands() {
  console.log('🧪 Testing enhanced folder commands...\n');

  try {
    // Test 1: Get downloads folder
    console.log('1️⃣ Testing get_downloads_folder...');
    const downloadsPath = await invoke('get_downloads_folder');
    console.log('✅ Downloads folder:', downloadsPath);
    
    // Test 2: Test directory access
    console.log('\n2️⃣ Testing test_directory_access...');
    const isAccessible = await invoke('test_directory_access', { path: downloadsPath });
    console.log('✅ Directory accessible:', isAccessible);
    
    // Test 3: Ensure directory exists
    console.log('\n3️⃣ Testing ensure_directory_exists...');
    const testPath = `${downloadsPath}/FlowDownload-Test`;
    const dirExists = await invoke('ensure_directory_exists', { path: testPath });
    console.log('✅ Directory ensured:', dirExists, 'at', testPath);
    
    // Test 4: Resolve path
    console.log('\n4️⃣ Testing resolve_path...');
    const resolvedPath = await invoke('resolve_path', { path: '~/Downloads' });
    console.log('✅ Path resolved:', resolvedPath);
    
    console.log('\n🎉 All folder command tests passed!');
    console.log('📁 Downloads folder issue should now be fixed permanently.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run tests if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  testFolderCommands();
}

export { testFolderCommands };