{"permissions": {"allow": ["Bash(rustc --version)", "Bash(rustup --version)", "Bash(cargo --version)", "Bash(rustup:*)", "Bash(cargo install:*)", "Bash(npm install:*)", "<PERSON><PERSON>(tauri init:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(cargo check:*)", "Bash(npm run tauri:dev:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(find:*)", "Bash(osascript:*)"], "deny": []}}