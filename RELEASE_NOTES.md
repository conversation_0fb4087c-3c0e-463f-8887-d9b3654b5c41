# Release Notes - v1.1.0

## Feature: Quality & Format Selection for Downloads

### What's New
- **Quality Selection**: Choose video quality from Best, 4K, 1440p, 1080p, 720p, 480p, or 360p
- **Audio-Only Downloads**: Extract audio in High (320kbps), Medium (128kbps), or Low (64kbps) quality
- **Format Options**: Download in various formats:
  - Video: MP4, MKV, WebM, MOV, AVI
  - Audio: MP3, M4A, WAV, FLAC
- **Smart Format Strings**: Backend automatically generates optimal yt-dlp commands based on your selections
- **Dynamic Configuration**: New configuration system for easy customization

### Technical Improvements
- Added quality and format parameters to the Rust `download_file` command
- Implemented `build_format_string()` function for intelligent format selection
- Created comprehensive PRD for transforming FlowDownload into a Canva-like media editor
- Enhanced UI with proper quality indicators and format descriptions

### How to Test
1. Start the app with `npm run tauri dev`
2. Paste a YouTube URL
3. Select your preferred quality (e.g., 1080p)
4. Select your preferred format (e.g., MP4)
5. Click "Start Download"
6. The file will be downloaded in your selected quality and format to your Downloads folder

### Next Steps
- Phase 2: Media Preview & Library
- Phase 3: Image Editor (Canva-like features)
- Phase 4: Video Editor
- Phase 5: Audio Editor

See `CLAUDE.md` for the complete Product Requirements Document.