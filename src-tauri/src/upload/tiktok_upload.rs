use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::path::Path;
use tokio::fs::File;
use tokio::io::AsyncReadExt;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VideoMetadata {
    pub title: String,
    pub privacy_level: PrivacyLevel,
    pub allow_comments: bool,
    pub allow_duet: bool,
    pub allow_stitch: bool,
    pub video_cover_timestamp_ms: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
pub enum PrivacyLevel {
    MutualFollowFriends,
    SelfOnly, 
    PublicToEveryone,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadProgress {
    pub bytes_uploaded: u64,
    pub total_bytes: u64,
    pub percentage: f64,
    pub status: String,
}

const CHUNK_SIZE: usize = 10 * 1024 * 1024; // 10MB chunks as per TikTok recommendation

pub struct TikTokUploader {
    client: Client,
    access_token: String,
}

impl TikTokUploader {
    pub fn new(access_token: String) -> Self {
        Self {
            client: Client::builder()
                .timeout(std::time::Duration::from_secs(300))
                .build()
                .unwrap(),
            access_token,
        }
    }

    pub async fn upload_video<F>(
        &self,
        file_path: &str,
        metadata: VideoMetadata,
        mut progress_callback: F,
    ) -> Result<String, String>
    where
        F: FnMut(UploadProgress) + Send,
    {
        // Validate file
        let path = Path::new(file_path);
        if !path.exists() {
            return Err("Video file does not exist".to_string());
        }

        let file_metadata = std::fs::metadata(file_path)
            .map_err(|e| format!("Failed to get file metadata: {}", e))?;
        let file_size = file_metadata.len();

        // Check file size limit (4GB for TikTok)
        if file_size > 4 * 1024 * 1024 * 1024 {
            return Err("File size exceeds TikTok's 4GB limit".to_string());
        }

        // Initialize upload
        progress_callback(UploadProgress {
            bytes_uploaded: 0,
            total_bytes: file_size,
            percentage: 0.0,
            status: "Initializing upload...".to_string(),
        });

        let upload_info = self.initialize_upload(file_size).await?;

        // Upload chunks
        progress_callback(UploadProgress {
            bytes_uploaded: 0,
            total_bytes: file_size,
            percentage: 0.0,
            status: "Uploading video...".to_string(),
        });

        self.upload_chunks(
            file_path,
            &upload_info.upload_id,
            file_size,
            |uploaded, total| {
                let percentage = (uploaded as f64 / total as f64) * 100.0;
                progress_callback(UploadProgress {
                    bytes_uploaded: uploaded,
                    total_bytes: total,
                    percentage,
                    status: format!("Uploading... {:.1}%", percentage),
                });
            },
        ).await?;

        // Finalize and publish
        progress_callback(UploadProgress {
            bytes_uploaded: file_size,
            total_bytes: file_size,
            percentage: 100.0,
            status: "Publishing video...".to_string(),
        });

        let share_id = self.finalize_upload(&upload_info.upload_id, metadata).await?;

        progress_callback(UploadProgress {
            bytes_uploaded: file_size,
            total_bytes: file_size,
            percentage: 100.0,
            status: "Upload complete!".to_string(),
        });

        Ok(share_id)
    }

    async fn initialize_upload(&self, file_size: u64) -> Result<InitUploadResponse, String> {
        let body = serde_json::json!({
            "upload_mode": "UPLOAD_MODE_CHUNK_INIT",
            "chunk_size": CHUNK_SIZE,
            "total_byte_count": file_size,
        });

        let response = self.client
            .post("https://open-api.tiktok.com/share/video/upload/")
            .header("Authorization", format!("Bearer {}", self.access_token))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to initialize upload: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to initialize upload: {}", error_text));
        }

        let result: ApiResponse<InitUploadData> = response.json().await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        if let Some(error) = result.error {
            return Err(format!("TikTok API error {}: {}", error.code, error.message));
        }

        result.data.ok_or_else(|| "No upload data in response".to_string())
    }

    async fn upload_chunks<F>(
        &self,
        file_path: &str,
        upload_id: &str,
        file_size: u64,
        mut progress_callback: F,
    ) -> Result<(), String>
    where
        F: FnMut(u64, u64) + Send,
    {
        let mut file = File::open(file_path).await
            .map_err(|e| format!("Failed to open file: {}", e))?;
        
        let mut uploaded = 0u64;
        let mut chunk_number = 0u32;
        let mut buffer = vec![0u8; CHUNK_SIZE];

        while uploaded < file_size {
            let bytes_to_read = std::cmp::min(CHUNK_SIZE, (file_size - uploaded) as usize);
            let bytes_read = file.read(&mut buffer[..bytes_to_read]).await
                .map_err(|e| format!("Failed to read file: {}", e))?;

            if bytes_read == 0 {
                break;
            }

            // Upload chunk
            let form = reqwest::multipart::Form::new()
                .text("upload_mode", "UPLOAD_MODE_CHUNK_UPLOAD")
                .text("upload_id", upload_id.to_string())
                .text("chunk_number", chunk_number.to_string())
                .part(
                    "video",
                    reqwest::multipart::Part::bytes(buffer[..bytes_read].to_vec())
                        .file_name("chunk.mp4")
                );

            let response = self.client
                .post("https://open-api.tiktok.com/share/video/upload/")
                .header("Authorization", format!("Bearer {}", self.access_token))
                .multipart(form)
                .send()
                .await
                .map_err(|e| format!("Failed to upload chunk {}: {}", chunk_number, e))?;

            if !response.status().is_success() {
                let error_text = response.text().await.unwrap_or_default();
                return Err(format!("Failed to upload chunk {}: {}", chunk_number, error_text));
            }

            uploaded += bytes_read as u64;
            chunk_number += 1;
            progress_callback(uploaded, file_size);
        }

        Ok(())
    }

    async fn finalize_upload(
        &self,
        upload_id: &str,
        metadata: VideoMetadata,
    ) -> Result<String, String> {
        let body = serde_json::json!({
            "upload_mode": "UPLOAD_MODE_CHUNK_FINISH",
            "upload_id": upload_id,
            "post_info": {
                "title": metadata.title,
                "privacy_level": metadata.privacy_level,
                "disable_duet": !metadata.allow_duet,
                "disable_comment": !metadata.allow_comments,
                "disable_stitch": !metadata.allow_stitch,
                "video_cover_timestamp_ms": metadata.video_cover_timestamp_ms,
            }
        });

        let response = self.client
            .post("https://open-api.tiktok.com/share/video/upload/")
            .header("Authorization", format!("Bearer {}", self.access_token))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to finalize upload: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to finalize upload: {}", error_text));
        }

        let result: ApiResponse<PublishData> = response.json().await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        if let Some(error) = result.error {
            return Err(format!("TikTok API error {}: {}", error.code, error.message));
        }

        result.data
            .and_then(|d| d.share_id)
            .ok_or_else(|| "No share ID in response".to_string())
    }
}

// TikTok API response structures
#[derive(Debug, Deserialize)]
struct ApiResponse<T> {
    data: Option<T>,
    error: Option<ApiError>,
}

#[derive(Debug, Deserialize)]
struct ApiError {
    code: i32,
    message: String,
}

#[derive(Debug, Deserialize)]
struct InitUploadData {
    upload_id: String,
    #[allow(dead_code)]
    upload_url: String,
}

#[derive(Debug, Deserialize)]
struct PublishData {
    share_id: Option<String>,
}

// Response alias for cleaner code
type InitUploadResponse = InitUploadData;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_privacy_level_serialization() {
        let privacy = PrivacyLevel::PublicToEveryone;
        let json = serde_json::to_string(&privacy).unwrap();
        assert_eq!(json, "\"PUBLIC_TO_EVERYONE\"");
    }

    #[test]
    fn test_video_metadata_creation() {
        let metadata = VideoMetadata {
            title: "Test Video".to_string(),
            privacy_level: PrivacyLevel::PublicToEveryone,
            allow_comments: true,
            allow_duet: true,
            allow_stitch: true,
            video_cover_timestamp_ms: 1000,
        };

        assert_eq!(metadata.title, "Test Video");
        assert!(metadata.allow_comments);
    }
}