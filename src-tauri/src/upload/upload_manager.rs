use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UploadTask {
    pub id: String,
    pub file_path: String,
    pub platform: String,
    pub status: UploadTaskStatus,
    pub progress: f32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UploadTaskStatus {
    Queued,
    Processing,
    Uploading,
    Completed,
    Failed,
    Cancelled,
}

pub struct UploadManager {
    tasks: Arc<Mutex<HashMap<String, UploadTask>>>,
}

impl UploadManager {
    pub fn new() -> Self {
        Self {
            tasks: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn add_task(&self, task: UploadTask) -> Result<(), String> {
        let mut tasks = self.tasks.lock().await;
        tasks.insert(task.id.clone(), task);
        Ok(())
    }

    pub async fn get_task(&self, id: &str) -> Option<UploadTask> {
        let tasks = self.tasks.lock().await;
        tasks.get(id).cloned()
    }

    pub async fn update_task_status(&self, id: &str, status: UploadTaskStatus) -> Result<(), String> {
        let mut tasks = self.tasks.lock().await;
        if let Some(task) = tasks.get_mut(id) {
            task.status = status;
            task.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Task not found".to_string())
        }
    }

    pub async fn update_task_progress(&self, id: &str, progress: f32) -> Result<(), String> {
        let mut tasks = self.tasks.lock().await;
        if let Some(task) = tasks.get_mut(id) {
            task.progress = progress;
            task.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Task not found".to_string())
        }
    }

    pub async fn set_task_error(&self, id: &str, error: String) -> Result<(), String> {
        let mut tasks = self.tasks.lock().await;
        if let Some(task) = tasks.get_mut(id) {
            task.status = UploadTaskStatus::Failed;
            task.error = Some(error);
            task.updated_at = Utc::now();
            Ok(())
        } else {
            Err("Task not found".to_string())
        }
    }

    pub async fn get_all_tasks(&self) -> Vec<UploadTask> {
        let tasks = self.tasks.lock().await;
        tasks.values().cloned().collect()
    }

    pub async fn remove_task(&self, id: &str) -> Result<(), String> {
        let mut tasks = self.tasks.lock().await;
        tasks.remove(id).ok_or_else(|| "Task not found".to_string())?;
        Ok(())
    }
}

impl Default for UploadManager {
    fn default() -> Self {
        Self::new()
    }
}