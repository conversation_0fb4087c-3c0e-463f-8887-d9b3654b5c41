use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::path::Path;
use tokio::fs::File;
use tokio::io::AsyncReadExt;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VideoMetadata {
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub category_id: String,
    pub privacy_status: PrivacyStatus,
    pub thumbnail_path: Option<String>,
    pub notify_subscribers: bool,
    pub scheduled_time: Option<chrono::DateTime<chrono::Utc>>,
    pub playlist_id: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PrivacyStatus {
    Private,
    Unlisted,
    Public,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UploadProgress {
    pub bytes_uploaded: u64,
    pub total_bytes: u64,
    pub percentage: f64,
    pub status: String,
}

pub const YOUTUBE_CATEGORIES: &[(&str, &str)] = &[
    ("1", "Film & Animation"),
    ("2", "Autos & Vehicles"),
    ("10", "Music"),
    ("15", "Pets & Animals"),
    ("17", "Sports"),
    ("19", "Travel & Events"),
    ("20", "Gaming"),
    ("22", "People & Blogs"),
    ("23", "Comedy"),
    ("24", "Entertainment"),
    ("25", "News & Politics"),
    ("26", "Howto & Style"),
    ("27", "Education"),
    ("28", "Science & Technology"),
];

pub struct YouTubeUploader {
    client: Client,
    access_token: String,
}

impl YouTubeUploader {
    pub fn new(access_token: String) -> Self {
        Self {
            client: Client::builder()
                .timeout(std::time::Duration::from_secs(300)) // 5 minute timeout for large uploads
                .build()
                .unwrap(),
            access_token,
        }
    }

    pub async fn upload_video<F>(
        &self,
        file_path: &str,
        metadata: VideoMetadata,
        mut progress_callback: F,
    ) -> Result<String, String>
    where
        F: FnMut(UploadProgress) + Send,
    {
        // Validate file exists
        let path = Path::new(file_path);
        if !path.exists() {
            return Err("Video file does not exist".to_string());
        }

        // Get file size
        let file_metadata = std::fs::metadata(file_path)
            .map_err(|e| format!("Failed to get file metadata: {}", e))?;
        let file_size = file_metadata.len();

        // Initialize resumable upload
        progress_callback(UploadProgress {
            bytes_uploaded: 0,
            total_bytes: file_size,
            percentage: 0.0,
            status: "Initializing upload...".to_string(),
        });

        let upload_url = self.initialize_resumable_upload(&metadata, file_size).await?;

        // Upload the video
        progress_callback(UploadProgress {
            bytes_uploaded: 0,
            total_bytes: file_size,
            percentage: 0.0,
            status: "Uploading video...".to_string(),
        });

        let video_id = self.upload_file_resumable(
            &upload_url,
            file_path,
            file_size,
            |uploaded, total| {
                let percentage = (uploaded as f64 / total as f64) * 100.0;
                progress_callback(UploadProgress {
                    bytes_uploaded: uploaded,
                    total_bytes: total,
                    percentage,
                    status: format!("Uploading... {:.1}%", percentage),
                });
            },
        ).await?;

        // Upload thumbnail if provided
        if let Some(thumbnail_path) = &metadata.thumbnail_path {
            progress_callback(UploadProgress {
                bytes_uploaded: file_size,
                total_bytes: file_size,
                percentage: 100.0,
                status: "Uploading thumbnail...".to_string(),
            });

            self.upload_thumbnail(&video_id, thumbnail_path).await?;
        }

        progress_callback(UploadProgress {
            bytes_uploaded: file_size,
            total_bytes: file_size,
            percentage: 100.0,
            status: "Upload complete!".to_string(),
        });

        Ok(video_id)
    }

    async fn initialize_resumable_upload(
        &self,
        metadata: &VideoMetadata,
        file_size: u64,
    ) -> Result<String, String> {
        let url = "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=resumable&part=snippet,status";

        let body = serde_json::json!({
            "snippet": {
                "title": metadata.title,
                "description": metadata.description,
                "tags": metadata.tags,
                "categoryId": metadata.category_id,
                "defaultLanguage": "en",
                "defaultAudioLanguage": "en"
            },
            "status": {
                "privacyStatus": metadata.privacy_status,
                "selfDeclaredMadeForKids": false,
                "notifySubscribers": metadata.notify_subscribers,
                "publishAt": metadata.scheduled_time.map(|t| t.to_rfc3339())
            }
        });

        let response = self.client
            .post(url)
            .header("Authorization", format!("Bearer {}", self.access_token))
            .header("Content-Type", "application/json")
            .header("X-Upload-Content-Length", file_size.to_string())
            .header("X-Upload-Content-Type", "video/*")
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to initialize upload: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to initialize upload: {}", error_text));
        }

        response.headers()
            .get("Location")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string())
            .ok_or_else(|| "No upload URL in response".to_string())
    }

    async fn upload_file_resumable<F>(
        &self,
        upload_url: &str,
        file_path: &str,
        file_size: u64,
        mut progress_callback: F,
    ) -> Result<String, String>
    where
        F: FnMut(u64, u64) + Send,
    {
        const CHUNK_SIZE: usize = 5 * 1024 * 1024; // 5MB chunks
        
        let mut file = File::open(file_path).await
            .map_err(|e| format!("Failed to open file: {}", e))?;
        
        let mut uploaded = 0u64;
        let mut buffer = vec![0u8; CHUNK_SIZE];

        while uploaded < file_size {
            let bytes_to_read = std::cmp::min(CHUNK_SIZE, (file_size - uploaded) as usize);
            let bytes_read = file.read(&mut buffer[..bytes_to_read]).await
                .map_err(|e| format!("Failed to read file: {}", e))?;

            if bytes_read == 0 {
                break;
            }

            let chunk_end = uploaded + bytes_read as u64;
            
            let response = self.client
                .put(upload_url)
                .header("Content-Length", bytes_read.to_string())
                .header(
                    "Content-Range",
                    format!("bytes {}-{}/{}", uploaded, chunk_end - 1, file_size)
                )
                .body(buffer[..bytes_read].to_vec())
                .send()
                .await
                .map_err(|e| format!("Failed to upload chunk: {}", e))?;

            let status = response.status();
            
            if status.is_success() {
                // Upload complete, parse response for video ID
                let response_body: serde_json::Value = response.json().await
                    .map_err(|e| format!("Failed to parse response: {}", e))?;
                
                return response_body["id"]
                    .as_str()
                    .map(|s| s.to_string())
                    .ok_or_else(|| "No video ID in response".to_string());
            } else if status.as_u16() == 308 {
                // Resume incomplete - continue uploading
                uploaded = chunk_end;
                progress_callback(uploaded, file_size);
            } else {
                let error_text = response.text().await.unwrap_or_default();
                return Err(format!("Upload failed with status {}: {}", status, error_text));
            }
        }

        Err("Upload incomplete".to_string())
    }

    async fn upload_thumbnail(&self, video_id: &str, thumbnail_path: &str) -> Result<(), String> {
        let url = format!(
            "https://www.googleapis.com/upload/youtube/v3/thumbnails/set?videoId={}",
            video_id
        );

        let thumbnail_data = tokio::fs::read(thumbnail_path).await
            .map_err(|e| format!("Failed to read thumbnail: {}", e))?;

        let part = reqwest::multipart::Part::bytes(thumbnail_data)
            .file_name("thumbnail.jpg")
            .mime_str("image/jpeg")
            .map_err(|e| format!("Failed to create multipart: {}", e))?;

        let form = reqwest::multipart::Form::new()
            .part("media", part);

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.access_token))
            .multipart(form)
            .send()
            .await
            .map_err(|e| format!("Failed to upload thumbnail: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to upload thumbnail: {}", error_text));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_privacy_status_serialization() {
        let privacy = PrivacyStatus::Public;
        let json = serde_json::to_string(&privacy).unwrap();
        assert_eq!(json, "\"public\"");

        let privacy = PrivacyStatus::Private;
        let json = serde_json::to_string(&privacy).unwrap();
        assert_eq!(json, "\"private\"");
    }

    #[test]
    fn test_video_metadata_creation() {
        let metadata = VideoMetadata {
            title: "Test Video".to_string(),
            description: "Test Description".to_string(),
            tags: vec!["test".to_string(), "video".to_string()],
            category_id: "22".to_string(), // People & Blogs
            privacy_status: PrivacyStatus::Private,
            thumbnail_path: None,
            notify_subscribers: false,
            scheduled_time: None,
            playlist_id: None,
        };

        assert_eq!(metadata.title, "Test Video");
        assert_eq!(metadata.tags.len(), 2);
    }
}