use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::fs::File;
use tokio::io::{AsyncReadExt, AsyncSeekExt};
use std::io::SeekFrom;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ResumableUpload {
    pub upload_id: String,
    pub file_path: String,
    pub file_size: u64,
    pub bytes_uploaded: u64,
    pub upload_url: Option<String>,
    pub upload_token: Option<String>,
    pub chunk_size: u64,
    pub platform: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadChunk {
    pub data: Vec<u8>,
    pub offset: u64,
    pub size: u64,
    pub is_final: bool,
}

impl ResumableUpload {
    pub fn new(
        upload_id: String,
        file_path: String,
        file_size: u64,
        platform: String,
    ) -> Self {
        Self {
            upload_id,
            file_path,
            file_size,
            bytes_uploaded: 0,
            upload_url: None,
            upload_token: None,
            chunk_size: 10 * 1024 * 1024, // 10MB default
            platform,
        }
    }

    pub fn progress_percentage(&self) -> f32 {
        if self.file_size == 0 {
            return 0.0;
        }
        (self.bytes_uploaded as f32 / self.file_size as f32) * 100.0
    }

    pub fn is_complete(&self) -> bool {
        self.bytes_uploaded >= self.file_size
    }

    pub fn remaining_bytes(&self) -> u64 {
        self.file_size.saturating_sub(self.bytes_uploaded)
    }

    pub async fn get_next_chunk(&self) -> Result<Option<UploadChunk>, String> {
        if self.is_complete() {
            return Ok(None);
        }

        let path = Path::new(&self.file_path);
        if !path.exists() {
            return Err(format!("File not found: {}", self.file_path));
        }

        let mut file = File::open(path)
            .await
            .map_err(|e| format!("Failed to open file: {}", e))?;

        // Seek to the position where we left off
        file.seek(SeekFrom::Start(self.bytes_uploaded))
            .await
            .map_err(|e| format!("Failed to seek in file: {}", e))?;

        // Calculate chunk size
        let remaining = self.remaining_bytes();
        let chunk_size = std::cmp::min(self.chunk_size, remaining);

        // Read chunk data
        let mut buffer = vec![0u8; chunk_size as usize];
        let _bytes_read = file.read_exact(&mut buffer)
            .await
            .map_err(|e| format!("Failed to read chunk: {}", e))?;

        Ok(Some(UploadChunk {
            data: buffer,
            offset: self.bytes_uploaded,
            size: chunk_size,
            is_final: self.bytes_uploaded + chunk_size >= self.file_size,
        }))
    }

    pub fn update_progress(&mut self, bytes_uploaded: u64) {
        self.bytes_uploaded = bytes_uploaded;
    }

    pub fn set_resume_data(&mut self, upload_url: String, upload_token: Option<String>) {
        self.upload_url = Some(upload_url);
        self.upload_token = upload_token;
    }
}

#[derive(Debug, Clone)]
pub struct ResumableUploadManager {
    uploads: std::sync::Arc<tokio::sync::Mutex<std::collections::HashMap<String, ResumableUpload>>>,
}

impl ResumableUploadManager {
    pub fn new() -> Self {
        Self {
            uploads: std::sync::Arc::new(tokio::sync::Mutex::new(std::collections::HashMap::new())),
        }
    }

    pub async fn create_upload(
        &self,
        upload_id: String,
        file_path: String,
        platform: String,
    ) -> Result<ResumableUpload, String> {
        // Get file size
        let path = Path::new(&file_path);
        let metadata = tokio::fs::metadata(path)
            .await
            .map_err(|e| format!("Failed to get file metadata: {}", e))?;

        let upload = ResumableUpload::new(
            upload_id.clone(),
            file_path,
            metadata.len(),
            platform,
        );

        // Store in memory
        let mut uploads = self.uploads.lock().await;
        uploads.insert(upload_id, upload.clone());

        Ok(upload)
    }

    pub async fn get_upload(&self, upload_id: &str) -> Option<ResumableUpload> {
        let uploads = self.uploads.lock().await;
        uploads.get(upload_id).cloned()
    }

    pub async fn update_upload(&self, upload: ResumableUpload) {
        let mut uploads = self.uploads.lock().await;
        uploads.insert(upload.upload_id.clone(), upload);
    }

    pub async fn remove_upload(&self, upload_id: &str) {
        let mut uploads = self.uploads.lock().await;
        uploads.remove(upload_id);
    }

    pub async fn get_all_uploads(&self) -> Vec<ResumableUpload> {
        let uploads = self.uploads.lock().await;
        uploads.values().cloned().collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::fs;
    use std::io::Write;

    #[tokio::test]
    async fn test_resumable_upload() {
        // Create a test file
        let temp_dir = tempfile::TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.bin");
        
        let test_data = vec![0u8; 100 * 1024]; // 100KB
        let mut file = std::fs::File::create(&file_path).unwrap();
        file.write_all(&test_data).unwrap();
        drop(file);

        // Create resumable upload
        let mut upload = ResumableUpload::new(
            "test-upload".to_string(),
            file_path.to_string_lossy().to_string(),
            test_data.len() as u64,
            "test".to_string(),
        );
        upload.chunk_size = 30 * 1024; // 30KB chunks

        // Test getting chunks
        let mut chunks_received = 0;
        let mut total_bytes = 0;

        while let Some(chunk) = upload.get_next_chunk().await.unwrap() {
            chunks_received += 1;
            total_bytes += chunk.size;
            upload.update_progress(upload.bytes_uploaded + chunk.size);
            
            assert!(chunk.size <= upload.chunk_size);
            assert_eq!(chunk.offset, total_bytes - chunk.size);
        }

        assert_eq!(chunks_received, 4); // 100KB / 30KB = 3.33, so 4 chunks
        assert_eq!(total_bytes, test_data.len() as u64);
        assert!(upload.is_complete());
        assert_eq!(upload.progress_percentage(), 100.0);
    }

    #[tokio::test]
    async fn test_resume_from_middle() {
        // Create a test file
        let temp_dir = tempfile::TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.bin");
        
        let test_data = vec![1u8; 100 * 1024]; // 100KB of 1s
        let mut file = std::fs::File::create(&file_path).unwrap();
        file.write_all(&test_data).unwrap();
        drop(file);

        // Create resumable upload that's already 40KB uploaded
        let mut upload = ResumableUpload::new(
            "test-upload".to_string(),
            file_path.to_string_lossy().to_string(),
            test_data.len() as u64,
            "test".to_string(),
        );
        upload.chunk_size = 30 * 1024; // 30KB chunks
        upload.bytes_uploaded = 40 * 1024; // Already uploaded 40KB

        // Get next chunk - should start from 40KB offset
        let chunk = upload.get_next_chunk().await.unwrap().unwrap();
        assert_eq!(chunk.offset, 40 * 1024);
        assert_eq!(chunk.size, 30 * 1024);
        assert!(chunk.data.iter().all(|&b| b == 1)); // All bytes should be 1
    }
}