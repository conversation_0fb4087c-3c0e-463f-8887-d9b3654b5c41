use crate::upload::resumable::{ResumableUpload, UploadChunk};
use crate::upload::youtube_upload::{VideoMetadata, UploadProgress};
use reqwest::Client;
use serde_json::Value;

pub struct YouTubeResumableUploader {
    client: Client,
    access_token: String,
}

impl YouTubeResumableUploader {
    pub fn new(access_token: String) -> Self {
        Self {
            client: Client::builder()
                .timeout(std::time::Duration::from_secs(300))
                .build()
                .unwrap(),
            access_token,
        }
    }

    /// Resume an interrupted YouTube upload
    pub async fn resume_upload<F>(
        &self,
        resumable_upload: &mut ResumableUpload,
        metadata: &VideoMetadata,
        mut progress_callback: F,
    ) -> Result<String, String>
    where
        F: FnMut(UploadProgress) + Send,
    {
        // If no upload URL exists, initialize a new upload
        let upload_url = if let Some(url) = &resumable_upload.upload_url {
            // Check upload status first
            let url_clone = url.clone();
            let current_offset = self.check_upload_status(&url_clone).await?;
            resumable_upload.update_progress(current_offset);
            url_clone
        } else {
            // Initialize new resumable upload
            progress_callback(UploadProgress {
                bytes_uploaded: 0,
                total_bytes: resumable_upload.file_size,
                percentage: 0.0,
                status: "Initializing resumable upload...".to_string(),
            });

            let url = self.initialize_resumable_upload(metadata, resumable_upload.file_size).await?;
            resumable_upload.set_resume_data(url.clone(), None);
            url
        };

        // Upload remaining chunks
        while let Some(chunk) = resumable_upload.get_next_chunk().await? {
            progress_callback(UploadProgress {
                bytes_uploaded: resumable_upload.bytes_uploaded,
                total_bytes: resumable_upload.file_size,
                percentage: resumable_upload.progress_percentage() as f64,
                status: format!("Uploading... {:.1}%", resumable_upload.progress_percentage()),
            });

            let video_id = self.upload_chunk(&upload_url, &chunk, resumable_upload.file_size).await?;
            
            resumable_upload.update_progress(chunk.offset + chunk.size);

            // If we got a video ID, the upload is complete
            if let Some(id) = video_id {
                progress_callback(UploadProgress {
                    bytes_uploaded: resumable_upload.file_size,
                    total_bytes: resumable_upload.file_size,
                    percentage: 100.0,
                    status: "Upload complete!".to_string(),
                });
                return Ok(id);
            }
        }

        Err("Upload completed but no video ID received".to_string())
    }

    /// Check the current status of a resumable upload
    async fn check_upload_status(&self, upload_url: &str) -> Result<u64, String> {
        let response = self.client
            .put(upload_url)
            .header("Content-Length", "0")
            .header("Content-Range", "bytes */0")
            .send()
            .await
            .map_err(|e| format!("Failed to check upload status: {}", e))?;

        if response.status().as_u16() == 308 {
            // Parse Range header to get current offset
            if let Some(range_header) = response.headers().get("Range") {
                if let Ok(range_str) = range_header.to_str() {
                    // Format: "bytes=0-12345"
                    if let Some(end) = range_str.strip_prefix("bytes=0-") {
                        if let Ok(offset) = end.parse::<u64>() {
                            return Ok(offset + 1); // Range is inclusive, so add 1
                        }
                    }
                }
            }
            Ok(0) // No range header means start from beginning
        } else if response.status().is_success() {
            // Upload is already complete
            Ok(u64::MAX)
        } else {
            let error_text = response.text().await.unwrap_or_default();
            Err(format!("Failed to check upload status: {}", error_text))
        }
    }

    /// Initialize a new resumable upload session
    async fn initialize_resumable_upload(
        &self,
        metadata: &VideoMetadata,
        file_size: u64,
    ) -> Result<String, String> {
        let url = "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=resumable&part=snippet,status";

        let body = serde_json::json!({
            "snippet": {
                "title": metadata.title,
                "description": metadata.description,
                "tags": metadata.tags,
                "categoryId": metadata.category_id,
                "defaultLanguage": "en",
                "defaultAudioLanguage": "en"
            },
            "status": {
                "privacyStatus": metadata.privacy_status,
                "selfDeclaredMadeForKids": false,
                "notifySubscribers": metadata.notify_subscribers,
                "publishAt": metadata.scheduled_time.map(|t| t.to_rfc3339())
            }
        });

        let response = self.client
            .post(url)
            .header("Authorization", format!("Bearer {}", self.access_token))
            .header("Content-Type", "application/json")
            .header("X-Upload-Content-Length", file_size.to_string())
            .header("X-Upload-Content-Type", "video/*")
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to initialize upload: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to initialize upload: {}", error_text));
        }

        response.headers()
            .get("Location")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string())
            .ok_or_else(|| "No upload URL in response".to_string())
    }

    /// Upload a single chunk
    async fn upload_chunk(
        &self,
        upload_url: &str,
        chunk: &UploadChunk,
        total_size: u64,
    ) -> Result<Option<String>, String> {
        let chunk_end = chunk.offset + chunk.size - 1;
        
        let response = self.client
            .put(upload_url)
            .header("Content-Length", chunk.size.to_string())
            .header(
                "Content-Range",
                format!("bytes {}-{}/{}", chunk.offset, chunk_end, total_size)
            )
            .body(chunk.data.clone())
            .send()
            .await
            .map_err(|e| format!("Failed to upload chunk: {}", e))?;

        let status = response.status();
        
        if status.is_success() {
            // Upload complete, parse response for video ID
            let response_body: Value = response.json().await
                .map_err(|e| format!("Failed to parse response: {}", e))?;
            
            Ok(response_body["id"]
                .as_str()
                .map(|s| s.to_string()))
        } else if status.as_u16() == 308 {
            // Resume incomplete - chunk uploaded successfully
            Ok(None)
        } else {
            let error_text = response.text().await.unwrap_or_default();
            Err(format!("Upload failed with status {}: {}", status, error_text))
        }
    }

    /// Upload thumbnail for a video
    pub async fn upload_thumbnail(
        &self,
        video_id: &str,
        thumbnail_path: &str,
    ) -> Result<(), String> {
        let url = format!(
            "https://www.googleapis.com/upload/youtube/v3/thumbnails/set?videoId={}",
            video_id
        );

        let file_bytes = tokio::fs::read(thumbnail_path)
            .await
            .map_err(|e| format!("Failed to read thumbnail: {}", e))?;

        let response = self.client
            .post(&url)
            .header("Authorization", format!("Bearer {}", self.access_token))
            .header("Content-Type", "image/jpeg")
            .body(file_bytes)
            .send()
            .await
            .map_err(|e| format!("Failed to upload thumbnail: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Failed to upload thumbnail: {}", error_text));
        }

        Ok(())
    }
}