use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::path::Path;

use crate::commands::upload::UploadProgress;

#[derive(Debug, <PERSON>lone)]
pub struct InstagramUploader {
    client: Client,
    access_token: String,
    user_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InstagramUploadConfig {
    pub caption: String,
    #[serde(default)]
    pub location_id: Option<String>,
    #[serde(default)]
    pub user_tags: Vec<UserTag>,
    #[serde(default)]
    pub product_tags: Vec<ProductTag>,
    #[serde(default)]
    pub cover_url: Option<String>,
    #[serde(default)]
    pub share_to_feed: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserTag {
    pub username: String,
    pub x: f32,
    pub y: f32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProductTag {
    pub product_id: String,
    pub x: f32,
    pub y: f32,
}

#[derive(Debug, Serialize, Deserialize)]
struct MediaContainerResponse {
    id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct MediaPublishResponse {
    id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct MediaStatusResponse {
    status_code: String,
    #[serde(default)]
    id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InstagramMediaInfo {
    pub id: String,
    pub permalink: String,
    pub media_type: String,
    pub media_url: String,
    pub caption: String,
}

impl InstagramUploader {
    pub fn new(access_token: String, user_id: String) -> Self {
        Self {
            client: Client::new(),
            access_token,
            user_id,
        }
    }

    /// Upload video to Instagram (Business/Creator accounts only)
    pub async fn upload_video(
        &self,
        video_path: &Path,
        config: InstagramUploadConfig,
        progress_callback: impl Fn(UploadProgress) + Send + 'static,
    ) -> Result<String> {
        // Step 1: Upload video to Instagram's servers
        let video_url = self.upload_video_file(video_path, &progress_callback).await?;

        // Step 2: Create media container
        let container_id = self.create_video_container(&video_url, &config).await?;

        // Step 3: Wait for video processing
        self.wait_for_processing(&container_id).await?;

        // Step 4: Publish the video
        let media_id = self.publish_media(&container_id).await?;

        Ok(media_id)
    }

    /// Upload image to Instagram (Business/Creator accounts only)
    pub async fn upload_image(
        &self,
        image_path: &Path,
        config: InstagramUploadConfig,
        progress_callback: impl Fn(UploadProgress) + Send + 'static,
    ) -> Result<String> {
        // Step 1: Upload image to Instagram's servers
        let image_url = self.upload_image_file(image_path, &progress_callback).await?;

        // Step 2: Create media container
        let container_id = self.create_image_container(&image_url, &config).await?;

        // Step 3: Publish the image
        let media_id = self.publish_media(&container_id).await?;

        Ok(media_id)
    }

    /// Upload carousel (multiple images/videos)
    pub async fn upload_carousel(
        &self,
        media_paths: Vec<&Path>,
        config: InstagramUploadConfig,
        progress_callback: impl Fn(UploadProgress) + Send + 'static,
    ) -> Result<String> {
        if media_paths.is_empty() || media_paths.len() > 10 {
            anyhow::bail!("Carousel must contain 1-10 media items");
        }

        let mut children = Vec::new();

        // Upload each media item
        for (index, path) in media_paths.iter().enumerate() {
            let progress = UploadProgress {
                upload_id: format!("carousel_{}", index),
                platform: crate::commands::upload::Platform::Instagram,
                progress: (index as f32 / media_paths.len() as f32) * 100.0,
                status: crate::commands::upload::UploadStatus::Uploading,
                message: format!("Processing item {} of {}", index + 1, media_paths.len()),
            };
            progress_callback(progress);

            let is_video = path.extension()
                .and_then(|ext| ext.to_str())
                .map(|ext| matches!(ext.to_lowercase().as_str(), "mp4" | "mov"))
                .unwrap_or(false);

            let media_url = if is_video {
                self.upload_video_file(path, &progress_callback).await?
            } else {
                self.upload_image_file(path, &progress_callback).await?
            };

            let child_id = self.create_carousel_item(&media_url, is_video).await?;
            children.push(child_id);
        }

        // Create carousel container
        let container_id = self.create_carousel_container(&children, &config).await?;

        // Publish carousel
        let media_id = self.publish_media(&container_id).await?;

        Ok(media_id)
    }

    /// Upload video file to Instagram's servers
    async fn upload_video_file(
        &self,
        video_path: &Path,
        progress_callback: &impl Fn(UploadProgress),
    ) -> Result<String> {
        // For Instagram, we need to host the video temporarily
        // In production, this would upload to a cloud storage service
        // and return a publicly accessible URL
        
        // For now, we'll simulate this with a placeholder
        // Real implementation would use S3, GCS, or similar
        
        let file_size = tokio::fs::metadata(video_path)
            .await
            .context("Failed to get video metadata")?
            .len();

        // Simulate upload progress
        for i in 0..=10 {
            let progress = UploadProgress {
                upload_id: format!("video_{}", video_path.to_string_lossy()),
                platform: crate::commands::upload::Platform::Instagram,
                progress: i as f32 * 10.0,
                status: crate::commands::upload::UploadStatus::Uploading,
                message: format!("Uploading video: {}%", i * 10),
            };
            progress_callback(progress);
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        // In production: Upload to cloud storage and return URL
        Ok("https://example.com/temp/video.mp4".to_string())
    }

    /// Upload image file to Instagram's servers
    async fn upload_image_file(
        &self,
        image_path: &Path,
        progress_callback: &impl Fn(UploadProgress),
    ) -> Result<String> {
        // Similar to video upload, needs cloud storage in production
        
        let file_size = tokio::fs::metadata(image_path)
            .await
            .context("Failed to get image metadata")?
            .len();

        // Simulate upload progress
        for i in 0..=10 {
            let progress = UploadProgress {
                upload_id: format!("image_{}", image_path.to_string_lossy()),
                platform: crate::commands::upload::Platform::Instagram,
                progress: i as f32 * 10.0,
                status: crate::commands::upload::UploadStatus::Uploading,
                message: format!("Uploading image: {}%", i * 10),
            };
            progress_callback(progress);
            tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        }

        // In production: Upload to cloud storage and return URL
        Ok("https://example.com/temp/image.jpg".to_string())
    }

    /// Create video container for publishing
    async fn create_video_container(
        &self,
        video_url: &str,
        config: &InstagramUploadConfig,
    ) -> Result<String> {
        let mut params: Vec<(&str, String)> = vec![
            ("video_url", video_url.to_string()),
            ("caption", config.caption.clone()),
            ("media_type", "REELS".to_string()), // or "VIDEO" for feed videos
            ("share_to_feed", config.share_to_feed.to_string()),
            ("access_token", self.access_token.clone()),
        ];

        if let Some(cover_url) = &config.cover_url {
            params.push(("cover_url", cover_url.clone()));
        }

        if let Some(location_id) = &config.location_id {
            params.push(("location_id", location_id.clone()));
        }

        let url = format!(
            "https://graph.facebook.com/v18.0/{}/media",
            self.user_id
        );

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .context("Failed to create video container")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to create video container: {}", error_text);
        }

        let container: MediaContainerResponse = response
            .json()
            .await
            .context("Failed to parse container response")?;

        Ok(container.id)
    }

    /// Create image container for publishing
    async fn create_image_container(
        &self,
        image_url: &str,
        config: &InstagramUploadConfig,
    ) -> Result<String> {
        let mut params: Vec<(&str, String)> = vec![
            ("image_url", image_url.to_string()),
            ("caption", config.caption.clone()),
            ("access_token", self.access_token.clone()),
        ];

        if let Some(location_id) = &config.location_id {
            params.push(("location_id", location_id.clone()));
        }

        // Add user tags if any
        if !config.user_tags.is_empty() {
            let user_tags_json = serde_json::to_string(&config.user_tags)?;
            params.push(("user_tags", user_tags_json));
        }

        let url = format!(
            "https://graph.facebook.com/v18.0/{}/media",
            self.user_id
        );

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .context("Failed to create image container")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to create image container: {}", error_text);
        }

        let container: MediaContainerResponse = response
            .json()
            .await
            .context("Failed to parse container response")?;

        Ok(container.id)
    }

    /// Create carousel item
    async fn create_carousel_item(&self, media_url: &str, is_video: bool) -> Result<String> {
        let mut params = vec![
            ("is_carousel_item", "true"),
            ("access_token", &self.access_token),
        ];

        if is_video {
            params.push(("video_url", media_url));
            params.push(("media_type", "VIDEO"));
        } else {
            params.push(("image_url", media_url));
        }

        let url = format!(
            "https://graph.facebook.com/v18.0/{}/media",
            self.user_id
        );

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .context("Failed to create carousel item")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to create carousel item: {}", error_text);
        }

        let container: MediaContainerResponse = response
            .json()
            .await
            .context("Failed to parse carousel item response")?;

        Ok(container.id)
    }

    /// Create carousel container
    async fn create_carousel_container(
        &self,
        children: &[String],
        config: &InstagramUploadConfig,
    ) -> Result<String> {
        let children_str = children.join(",");
        
        let params = vec![
            ("media_type", "CAROUSEL"),
            ("children", &children_str),
            ("caption", &config.caption),
            ("access_token", &self.access_token),
        ];

        let url = format!(
            "https://graph.facebook.com/v18.0/{}/media",
            self.user_id
        );

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .context("Failed to create carousel container")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to create carousel container: {}", error_text);
        }

        let container: MediaContainerResponse = response
            .json()
            .await
            .context("Failed to parse carousel container response")?;

        Ok(container.id)
    }

    /// Wait for video processing to complete
    async fn wait_for_processing(&self, container_id: &str) -> Result<()> {
        let url = format!(
            "https://graph.facebook.com/v18.0/{}?fields=status_code&access_token={}",
            container_id, self.access_token
        );

        let mut attempts = 0;
        let max_attempts = 60; // 5 minutes timeout

        loop {
            let response = self.client
                .get(&url)
                .send()
                .await
                .context("Failed to check processing status")?;

            if !response.status().is_success() {
                let error_text = response.text().await.unwrap_or_default();
                anyhow::bail!("Failed to check processing status: {}", error_text);
            }

            let status: MediaStatusResponse = response
                .json()
                .await
                .context("Failed to parse status response")?;

            match status.status_code.as_str() {
                "FINISHED" => return Ok(()),
                "IN_PROGRESS" => {
                    attempts += 1;
                    if attempts >= max_attempts {
                        anyhow::bail!("Video processing timeout");
                    }
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
                "ERROR" => anyhow::bail!("Video processing failed"),
                _ => anyhow::bail!("Unknown processing status: {}", status.status_code),
            }
        }
    }

    /// Publish the media container
    async fn publish_media(&self, container_id: &str) -> Result<String> {
        let url = format!(
            "https://graph.facebook.com/v18.0/{}/media_publish",
            self.user_id
        );

        let params = vec![
            ("creation_id", container_id),
            ("access_token", &self.access_token),
        ];

        let response = self.client
            .post(&url)
            .form(&params)
            .send()
            .await
            .context("Failed to publish media")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to publish media: {}", error_text);
        }

        let publish_response: MediaPublishResponse = response
            .json()
            .await
            .context("Failed to parse publish response")?;

        Ok(publish_response.id)
    }

    /// Get information about uploaded media
    pub async fn get_media_info(&self, media_id: &str) -> Result<InstagramMediaInfo> {
        let url = format!(
            "https://graph.facebook.com/v18.0/{}?fields=id,permalink,media_type,media_url,caption&access_token={}",
            media_id, self.access_token
        );

        let response = self.client
            .get(&url)
            .send()
            .await
            .context("Failed to get media info")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to get media info: {}", error_text);
        }

        let media_info: InstagramMediaInfo = response
            .json()
            .await
            .context("Failed to parse media info")?;

        Ok(media_info)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_instagram_upload_config_serialization() {
        let config = InstagramUploadConfig {
            caption: "Test caption #rust #tauri".to_string(),
            location_id: Some("123456".to_string()),
            user_tags: vec![
                UserTag {
                    username: "testuser".to_string(),
                    x: 0.5,
                    y: 0.5,
                }
            ],
            product_tags: vec![],
            cover_url: None,
            share_to_feed: true,
        };

        let json = serde_json::to_string(&config).unwrap();
        let deserialized: InstagramUploadConfig = serde_json::from_str(&json).unwrap();

        assert_eq!(config.caption, deserialized.caption);
        assert_eq!(config.location_id, deserialized.location_id);
        assert_eq!(config.user_tags.len(), deserialized.user_tags.len());
        assert_eq!(config.share_to_feed, deserialized.share_to_feed);
    }
}