use anyhow::{Result, Context};
use std::process::Command;
use serde::{Serialize, Deserialize};
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
pub struct LocalAnalysis {
    pub duration: f64,
    pub format: String,
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub resolution: Option<String>,
    pub fps: Option<f64>,
    pub bitrate: Option<i64>,
    pub file_size: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SceneChange {
    pub timestamp: f64,
    pub confidence: f64,
}

pub struct LocalProcessor;

impl LocalProcessor {
    // Get basic media metadata using FFprobe
    pub async fn get_media_metadata(file_path: &str) -> Result<LocalAnalysis> {
        let output = Command::new("ffprobe")
            .args(&[
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                "-show_streams",
                file_path
            ])
            .output()
            .context("Failed to run ffprobe. Please ensure FFmpeg is installed.")?;

        if !output.status.success() {
            return Err(anyhow::anyhow!("FFprobe failed to analyze file"));
        }

        let json_str = String::from_utf8_lossy(&output.stdout);
        let probe_data: serde_json::Value = serde_json::from_str(&json_str)?;

        // Extract format info
        let format = probe_data["format"]["format_name"]
            .as_str()
            .unwrap_or("unknown")
            .to_string();
        
        let duration = probe_data["format"]["duration"]
            .as_str()
            .and_then(|s| s.parse::<f64>().ok())
            .unwrap_or(0.0);
        
        let bitrate = probe_data["format"]["bit_rate"]
            .as_str()
            .and_then(|s| s.parse::<i64>().ok());
        
        let file_size = probe_data["format"]["size"]
            .as_str()
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0);

        // Extract stream info
        let empty_vec = vec![];
        let streams = probe_data["streams"].as_array().unwrap_or(&empty_vec);
        
        let mut video_codec = None;
        let mut audio_codec = None;
        let mut resolution = None;
        let mut fps = None;

        for stream in streams {
            match stream["codec_type"].as_str() {
                Some("video") => {
                    video_codec = stream["codec_name"].as_str().map(String::from);
                    
                    if let (Some(width), Some(height)) = (
                        stream["width"].as_i64(),
                        stream["height"].as_i64()
                    ) {
                        resolution = Some(format!("{}x{}", width, height));
                    }
                    
                    // Calculate FPS
                    if let Some(r_frame_rate) = stream["r_frame_rate"].as_str() {
                        let parts: Vec<&str> = r_frame_rate.split('/').collect();
                        if parts.len() == 2 {
                            if let (Ok(num), Ok(den)) = (
                                parts[0].parse::<f64>(),
                                parts[1].parse::<f64>()
                            ) {
                                if den > 0.0 {
                                    fps = Some(num / den);
                                }
                            }
                        }
                    }
                }
                Some("audio") => {
                    audio_codec = stream["codec_name"].as_str().map(String::from);
                }
                _ => {}
            }
        }

        Ok(LocalAnalysis {
            duration,
            format,
            video_codec,
            audio_codec,
            resolution,
            fps,
            bitrate,
            file_size,
        })
    }

    // Detect scene changes using FFmpeg
    pub async fn detect_scenes(file_path: &str, threshold: f64) -> Result<Vec<SceneChange>> {
        let output = Command::new("ffmpeg")
            .args(&[
                "-i", file_path,
                "-filter:v", &format!("select='gt(scene,{})',showinfo", threshold),
                "-f", "null",
                "-"
            ])
            .output()
            .context("Failed to run scene detection")?;

        let stderr = String::from_utf8_lossy(&output.stderr);
        let mut scenes = Vec::new();

        // Parse FFmpeg output for scene changes
        for line in stderr.lines() {
            if line.contains("pts_time:") {
                // Extract timestamp from line like "pts_time:1.5"
                if let Some(time_str) = line.split("pts_time:").nth(1) {
                    if let Some(time_part) = time_str.split_whitespace().next() {
                        if let Ok(timestamp) = time_part.parse::<f64>() {
                            scenes.push(SceneChange {
                                timestamp,
                                confidence: threshold,
                            });
                        }
                    }
                }
            }
        }

        Ok(scenes)
    }

    // Extract thumbnail at specific timestamp
    pub async fn extract_thumbnail(
        file_path: &str, 
        timestamp: f64,
        output_path: &str
    ) -> Result<String> {
        Command::new("ffmpeg")
            .args(&[
                "-ss", &timestamp.to_string(),
                "-i", file_path,
                "-vframes", "1",
                "-q:v", "2",
                output_path,
                "-y"
            ])
            .output()
            .context("Failed to extract thumbnail")?;

        Ok(output_path.to_string())
    }

    // Get audio levels for visualization
    pub async fn analyze_audio_levels(file_path: &str) -> Result<Vec<f64>> {
        let output = Command::new("ffmpeg")
            .args(&[
                "-i", file_path,
                "-af", "astats=metadata=1:reset=1,ametadata=print:key=lavfi.astats.Overall.RMS_level",
                "-f", "null",
                "-"
            ])
            .output()
            .context("Failed to analyze audio levels")?;

        let stderr = String::from_utf8_lossy(&output.stderr);
        let mut levels = Vec::new();

        for line in stderr.lines() {
            if line.contains("lavfi.astats.Overall.RMS_level=") {
                if let Some(level_str) = line.split('=').nth(1) {
                    if let Ok(level) = level_str.trim().parse::<f64>() {
                        levels.push(level);
                    }
                }
            }
        }

        Ok(levels)
    }

    // Detect silence periods for smart editing
    pub async fn detect_silence(
        file_path: &str,
        noise_threshold: f64,
        duration_threshold: f64
    ) -> Result<Vec<(f64, f64)>> {
        let output = Command::new("ffmpeg")
            .args(&[
                "-i", file_path,
                "-af", &format!(
                    "silencedetect=noise={}dB:d={}",
                    noise_threshold,
                    duration_threshold
                ),
                "-f", "null",
                "-"
            ])
            .output()
            .context("Failed to detect silence")?;

        let stderr = String::from_utf8_lossy(&output.stderr);
        let mut silence_periods = Vec::new();
        let mut silence_start = None;

        for line in stderr.lines() {
            if line.contains("silence_start:") {
                if let Some(start_str) = line.split("silence_start:").nth(1) {
                    if let Some(start_part) = start_str.split_whitespace().next() {
                        if let Ok(start) = start_part.parse::<f64>() {
                            silence_start = Some(start);
                        }
                    }
                }
            } else if line.contains("silence_end:") && silence_start.is_some() {
                if let Some(end_str) = line.split("silence_end:").nth(1) {
                    if let Some(end_part) = end_str.split_whitespace().next() {
                        if let Ok(end) = end_part.parse::<f64>() {
                            silence_periods.push((silence_start.unwrap(), end));
                            silence_start = None;
                        }
                    }
                }
            }
        }

        Ok(silence_periods)
    }

    // Generate video preview (animated thumbnail)
    pub async fn generate_preview(
        file_path: &str,
        output_path: &str,
        duration: f64,
        fps: i32
    ) -> Result<String> {
        Command::new("ffmpeg")
            .args(&[
                "-i", file_path,
                "-vf", &format!("fps={},scale=320:-1", fps),
                "-t", &duration.to_string(),
                "-loop", "0",
                output_path,
                "-y"
            ])
            .output()
            .context("Failed to generate preview")?;

        Ok(output_path.to_string())
    }

    // Extract keyframes for analysis
    pub async fn extract_keyframes(file_path: &str) -> Result<Vec<f64>> {
        let output = Command::new("ffmpeg")
            .args(&[
                "-i", file_path,
                "-vf", "select='eq(pict_type,I)',showinfo",
                "-f", "null",
                "-"
            ])
            .output()
            .context("Failed to extract keyframes")?;

        let stderr = String::from_utf8_lossy(&output.stderr);
        let mut keyframes = Vec::new();

        for line in stderr.lines() {
            if line.contains("pts_time:") {
                if let Some(time_str) = line.split("pts_time:").nth(1) {
                    if let Some(time_part) = time_str.split_whitespace().next() {
                        if let Ok(timestamp) = time_part.parse::<f64>() {
                            keyframes.push(timestamp);
                        }
                    }
                }
            }
        }

        Ok(keyframes)
    }
}