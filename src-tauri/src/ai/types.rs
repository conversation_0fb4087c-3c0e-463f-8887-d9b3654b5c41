use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ContentAnalysis {
    pub id: String,
    pub file_path: String,
    pub file_name: String,
    pub file_type: String,
    pub duration: Option<f64>,
    pub analysis_date: DateTime<Utc>,
    
    pub scenes: Option<Vec<SceneAnalysis>>,
    pub transcript: Option<Vec<TranscriptSegment>>,
    pub sentiment: Option<SentimentAnalysis>,
    pub topics: Option<Vec<Topic>>,
    pub virality: Option<ViralityPrediction>,
    pub platform_suggestions: Option<Vec<PlatformOptimization>>,
    pub video_quality: Option<VideoQualityMetrics>,
    pub audio_quality: Option<AudioQualityMetrics>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneAnalysis {
    pub start_time: f64,
    pub end_time: f64,
    pub scene_type: String,
    pub description: String,
    pub keyframes: Vec<String>,
    pub mood: String,
    pub pace: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TranscriptSegment {
    pub start_time: f64,
    pub end_time: f64,
    pub text: String,
    pub confidence: f64,
    pub speaker: Option<String>,
    pub language: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SentimentAnalysis {
    pub overall: String,
    pub score: f64,
    pub timeline: Vec<SentimentPoint>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SentimentPoint {
    pub timestamp: f64,
    pub sentiment: String,
    pub score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Topic {
    pub topic: String,
    pub relevance: f64,
    pub keywords: Vec<String>,
    pub category: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ViralityPrediction {
    pub score: i32,
    pub confidence: f64,
    pub factors: Vec<ViralityFactor>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ViralityFactor {
    pub factor: String,
    pub impact: String,
    pub weight: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformOptimization {
    pub platform: String,
    pub recommendations: PlatformRecommendations,
    pub score: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformRecommendations {
    pub ideal_length: i32,
    pub aspect_ratio: String,
    pub hashtags: Vec<String>,
    pub best_posting_times: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoQualityMetrics {
    pub resolution: String,
    pub fps: f64,
    pub bitrate: i64,
    pub codec: String,
    pub has_audio: bool,
    pub issues: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioQualityMetrics {
    pub sample_rate: i32,
    pub bitrate: i32,
    pub channels: i32,
    pub codec: String,
    pub volume_levels: VolumeLevels,
    pub issues: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolumeLevels {
    pub average: f64,
    pub peak: f64,
    pub silent_segments: Vec<(f64, f64)>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisOptions {
    pub enable_transcription: bool,
    pub enable_scene_detection: bool,
    pub enable_object_detection: bool,
    pub enable_face_detection: bool,
    pub enable_sentiment_analysis: bool,
    pub enable_platform_optimization: bool,
    pub target_platforms: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TranscriptionResult {
    pub segments: Vec<TranscriptSegment>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneDetectionResult {
    pub scenes: Vec<SceneAnalysis>,
}