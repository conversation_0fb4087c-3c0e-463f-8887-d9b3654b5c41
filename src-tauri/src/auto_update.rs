use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::io::AsyncWriteExt;
use chrono::{DateTime, Utc};

/// Professional auto-update system for desktop applications
pub struct AutoUpdateSystem {
    config: UpdateConfig,
    current_version: semver::Version,
    update_channel: UpdateChannel,
    state: Arc<Mutex<UpdateState>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateConfig {
    pub enabled: bool,
    pub check_interval_hours: u64,
    pub endpoint: String,
    pub public_key: String, // For signature verification
    pub force_update_below: Option<String>, // Force update if version is below this
    pub silent_download: bool,
    pub auto_install: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum UpdateChannel {
    Stable,
    Beta,
    Nightly,
    Enterprise, // Custom updates for enterprise customers
}

#[derive(Debug, <PERSON>lone)]
enum UpdateState {
    Idle,
    Checking,
    Downloading { progress: f32 },
    Downloaded { update: UpdateInfo },
    Installing,
    Failed { error: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateInfo {
    pub version: String,
    pub release_notes: String,
    pub download_url: String,
    pub signature: String,
    pub size: u64,
    pub mandatory: bool,
    pub min_os_version: Option<String>,
    pub published_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize)]
pub struct UpdateStatus {
    pub available: bool,
    pub current_version: String,
    pub latest_version: Option<String>,
    pub download_progress: Option<f32>,
    pub ready_to_install: bool,
    pub mandatory: bool,
}

impl AutoUpdateSystem {
    pub fn new(config: UpdateConfig, current_version: &str, channel: UpdateChannel) -> Result<Self, String> {
        let version = semver::Version::parse(current_version)
            .map_err(|e| format!("Invalid version: {}", e))?;
        
        let system = Self {
            config: config.clone(),
            current_version: version,
            update_channel: channel,
            state: Arc::new(Mutex::new(UpdateState::Idle)),
        };
        
        // Start background check loop
        if config.enabled {
            let system_clone = system.clone();
            tokio::spawn(async move {
                system_clone.check_loop().await;
            });
        }
        
        Ok(system)
    }

    pub async fn check_for_updates(&self) -> Result<Option<UpdateInfo>, String> {
        // Set state to checking
        *self.state.lock().await = UpdateState::Checking;
        
        // Build update check request
        let check_request = UpdateCheckRequest {
            current_version: self.current_version.to_string(),
            channel: self.update_channel.clone(),
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            license_key: self.get_license_key().await,
        };
        
        // Make request to update server
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;
        
        let response = client
            .post(&self.config.endpoint)
            .json(&check_request)
            .send()
            .await
            .map_err(|e| format!("Update check failed: {}", e))?;
        
        if !response.status().is_success() {
            *self.state.lock().await = UpdateState::Failed {
                error: format!("Server returned: {}", response.status()),
            };
            return Ok(None);
        }
        
        let update_info: Option<UpdateInfo> = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse update response: {}", e))?;
        
        // Check if update is newer
        if let Some(mut info) = update_info {
            let latest_version = semver::Version::parse(&info.version)
                .map_err(|e| format!("Invalid version from server: {}", e))?;
            
            if latest_version > self.current_version {
                // Check if it's a forced update
                if let Some(force_below) = &self.config.force_update_below {
                    if let Ok(force_version) = semver::Version::parse(force_below) {
                        if self.current_version < force_version {
                            info.mandatory = true;
                        }
                    }
                }
                
                *self.state.lock().await = UpdateState::Idle;
                return Ok(Some(info));
            }
        }
        
        *self.state.lock().await = UpdateState::Idle;
        Ok(None)
    }

    pub async fn download_update(&self, update_info: UpdateInfo) -> Result<(), String> {
        // Verify signature before downloading
        self.verify_signature(&update_info)?;
        
        let client = reqwest::Client::new();
        let response = client
            .get(&update_info.download_url)
            .send()
            .await
            .map_err(|e| format!("Failed to download update: {}", e))?;
        
        let total_size = response.content_length().unwrap_or(update_info.size);
        
        // Create temporary file for download
        let temp_dir = tempfile::tempdir()
            .map_err(|e| format!("Failed to create temp dir: {}", e))?;
        let temp_path = temp_dir.path().join(format!("update-{}.exe", update_info.version));
        
        let mut file = tokio::fs::File::create(&temp_path).await
            .map_err(|e| format!("Failed to create temp file: {}", e))?;
        
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        
        use futures_util::StreamExt;
        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| format!("Download error: {}", e))?;
            
            file.write_all(&chunk).await
                .map_err(|e| format!("Failed to write update file: {}", e))?;
            
            downloaded += chunk.len() as u64;
            let progress = (downloaded as f32 / total_size as f32) * 100.0;
            
            *self.state.lock().await = UpdateState::Downloading { progress };
            
            // Emit progress event
            self.emit_progress(progress).await;
        }
        
        // Verify downloaded file
        self.verify_download(&temp_path, &update_info).await?;
        
        // Move to final location
        let final_path = self.get_update_path(&update_info.version);
        tokio::fs::rename(&temp_path, &final_path).await
            .map_err(|e| format!("Failed to save update: {}", e))?;
        
        *self.state.lock().await = UpdateState::Downloaded { update: update_info };
        
        Ok(())
    }

    pub async fn install_update(&self) -> Result<(), String> {
        let update_info = match &*self.state.lock().await {
            UpdateState::Downloaded { update } => update.clone(),
            _ => return Err("No update downloaded".to_string()),
        };
        
        *self.state.lock().await = UpdateState::Installing;
        
        // Platform-specific installation
        #[cfg(target_os = "windows")]
        {
            self.install_windows_update(&update_info).await?;
        }
        
        #[cfg(target_os = "macos")]
        {
            self.install_macos_update(&update_info).await?;
        }
        
        #[cfg(target_os = "linux")]
        {
            self.install_linux_update(&update_info).await?;
        }
        
        Ok(())
    }

    async fn install_windows_update(&self, update_info: &UpdateInfo) -> Result<(), String> {
        let update_path = self.get_update_path(&update_info.version);
        
        // Create a batch script to replace the running executable
        let batch_script = format!(
            r#"
@echo off
echo Updating FlowDownload...
timeout /t 2 /nobreak > nul
taskkill /F /IM flowdownload.exe > nul 2>&1
timeout /t 1 /nobreak > nul
move /Y "{}" "%LOCALAPPDATA%\FlowDownload\flowdownload.exe"
start "" "%LOCALAPPDATA%\FlowDownload\flowdownload.exe"
del "%~f0"
"#,
            update_path.display()
        );
        
        let batch_path = tempfile::NamedTempFile::new()
            .map_err(|e| format!("Failed to create batch file: {}", e))?
            .into_temp_path();
        
        tokio::fs::write(&batch_path, batch_script).await
            .map_err(|e| format!("Failed to write batch script: {}", e))?;
        
        // Execute the batch script
        std::process::Command::new("cmd")
            .args(&["/C", batch_path.to_str().unwrap()])
            .spawn()
            .map_err(|e| format!("Failed to execute update: {}", e))?;
        
        // The application will be terminated by the batch script
        Ok(())
    }

    async fn install_macos_update(&self, _update_info: &UpdateInfo) -> Result<(), String> {
        // macOS update logic
        // This would typically involve:
        // 1. Mounting the DMG
        // 2. Copying the new app bundle
        // 3. Restarting the application
        todo!("macOS update installation")
    }

    async fn install_linux_update(&self, _update_info: &UpdateInfo) -> Result<(), String> {
        // Linux update logic
        // This would typically involve:
        // 1. Extracting the AppImage/deb/rpm
        // 2. Replacing the binary
        // 3. Restarting the application
        todo!("Linux update installation")
    }

    fn verify_signature(&self, update_info: &UpdateInfo) -> Result<(), String> {
        // Verify the update signature using the public key
        // This ensures the update hasn't been tampered with
        // In production, use proper cryptographic verification
        
        if update_info.signature.is_empty() {
            return Err("Update signature is missing".to_string());
        }
        
        // TODO: Implement actual signature verification
        Ok(())
    }

    async fn verify_download(&self, path: &std::path::Path, update_info: &UpdateInfo) -> Result<(), String> {
        // Verify the downloaded file
        // 1. Check file size
        // 2. Verify checksum
        // 3. Check signature
        
        let metadata = tokio::fs::metadata(path).await
            .map_err(|e| format!("Failed to read update file: {}", e))?;
        
        if metadata.len() != update_info.size {
            return Err(format!("Download size mismatch: expected {}, got {}", 
                update_info.size, metadata.len()));
        }
        
        // TODO: Implement checksum verification
        
        Ok(())
    }

    async fn check_loop(&self) {
        let interval = std::time::Duration::from_secs(self.config.check_interval_hours * 3600);
        
        loop {
            tokio::time::sleep(interval).await;
            
            if let Ok(Some(update)) = self.check_for_updates().await {
                log::info!("Update available: {}", update.version);
                
                if self.config.silent_download {
                    if let Err(e) = self.download_update(update.clone()).await {
                        log::error!("Failed to download update: {}", e);
                    } else if self.config.auto_install && !update.mandatory {
                        // Schedule installation for next restart
                        self.schedule_update_on_restart().await;
                    }
                }
            }
        }
    }

    async fn schedule_update_on_restart(&self) {
        // Write a marker file that tells the app to install update on next start
        if let Some(config_dir) = dirs::config_local_dir() {
            let marker_path = config_dir.join("FlowDownload").join(".pending_update");
            let _ = tokio::fs::write(&marker_path, b"pending").await;
        }
    }

    fn get_update_path(&self, version: &str) -> std::path::PathBuf {
        let cache_dir = dirs::cache_dir().unwrap_or_else(|| std::path::PathBuf::from("."));
        cache_dir.join("FlowDownload").join("updates").join(format!("update-{}", version))
    }

    async fn get_license_key(&self) -> Option<String> {
        // Retrieve license key from secure storage
        // TODO: Implement secure license storage
        None
    }

    async fn emit_progress(&self, _progress: f32) {
        // Emit progress to UI
        // This would use Tauri's event system
    }

    pub async fn get_status(&self) -> UpdateStatus {
        let state = self.state.lock().await;
        
        match &*state {
            UpdateState::Downloaded { update } => UpdateStatus {
                available: true,
                current_version: self.current_version.to_string(),
                latest_version: Some(update.version.clone()),
                download_progress: Some(100.0),
                ready_to_install: true,
                mandatory: update.mandatory,
            },
            UpdateState::Downloading { progress } => UpdateStatus {
                available: true,
                current_version: self.current_version.to_string(),
                latest_version: None,
                download_progress: Some(*progress),
                ready_to_install: false,
                mandatory: false,
            },
            _ => UpdateStatus {
                available: false,
                current_version: self.current_version.to_string(),
                latest_version: None,
                download_progress: None,
                ready_to_install: false,
                mandatory: false,
            },
        }
    }
}

impl Clone for AutoUpdateSystem {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            current_version: self.current_version.clone(),
            update_channel: self.update_channel.clone(),
            state: self.state.clone(),
        }
    }
}

#[derive(Debug, Serialize)]
struct UpdateCheckRequest {
    current_version: String,
    channel: UpdateChannel,
    os: String,
    arch: String,
    license_key: Option<String>,
}

// Update commands for Tauri
#[tauri::command]
pub async fn check_for_app_updates() -> Result<UpdateStatus, String> {
    let auto_update = crate::AUTO_UPDATE.lock().await;
    match auto_update.as_ref() {
        Some(update_system) => Ok(update_system.get_status().await),
        None => Err("Update system not initialized".to_string()),
    }
}

#[tauri::command]
pub async fn download_app_update() -> Result<(), String> {
    let auto_update = crate::AUTO_UPDATE.lock().await;
    match auto_update.as_ref() {
        Some(update_system) => {
            if let Some(update_info) = update_system.check_for_updates().await? {
                update_system.download_update(update_info).await
            } else {
                Err("No update available".to_string())
            }
        }
        None => Err("Update system not initialized".to_string()),
    }
}

#[tauri::command]
pub async fn install_app_update() -> Result<(), String> {
    let auto_update = crate::AUTO_UPDATE.lock().await;
    match auto_update.as_ref() {
        Some(update_system) => update_system.install_update().await,
        None => Err("Update system not initialized".to_string()),
    }
}