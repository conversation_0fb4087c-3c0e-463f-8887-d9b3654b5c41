use sqlx::{SqlitePool, Row};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadRecord {
    pub id: String,
    pub file_path: String,
    pub file_name: String,
    pub file_size: i64,
    pub platform: String,
    pub status: UploadStatus,
    pub progress: f32,
    
    // Upload configuration
    pub title: String,
    pub description: Option<String>,
    pub tags: Vec<String>,
    pub privacy: String,
    pub category: Option<String>,
    pub playlist_id: Option<String>,
    pub thumbnail_path: Option<String>,
    
    // Platform-specific data
    pub platform_id: Option<String>,
    pub platform_url: Option<String>,
    
    // Timestamps
    pub created_at: DateTime<Utc>,
    pub started_at: Option<DateTime<Utc>>,
    pub completed_at: Option<DateTime<Utc>>,
    
    // Error tracking
    pub error_message: Option<String>,
    pub retry_count: u32,
    
    // Resume data
    pub upload_url: Option<String>,
    pub upload_token: Option<String>,
    pub bytes_uploaded: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum UploadStatus {
    Queued,
    Preparing,
    Uploading,
    Processing,
    Completed,
    Failed,
    Cancelled,
    Paused,
}

impl ToString for UploadStatus {
    fn to_string(&self) -> String {
        match self {
            UploadStatus::Queued => "queued",
            UploadStatus::Preparing => "preparing",
            UploadStatus::Uploading => "uploading",
            UploadStatus::Processing => "processing",
            UploadStatus::Completed => "completed",
            UploadStatus::Failed => "failed",
            UploadStatus::Cancelled => "cancelled",
            UploadStatus::Paused => "paused",
        }.to_string()
    }
}

impl From<String> for UploadStatus {
    fn from(s: String) -> Self {
        match s.as_str() {
            "queued" => UploadStatus::Queued,
            "preparing" => UploadStatus::Preparing,
            "uploading" => UploadStatus::Uploading,
            "processing" => UploadStatus::Processing,
            "completed" => UploadStatus::Completed,
            "failed" => UploadStatus::Failed,
            "cancelled" => UploadStatus::Cancelled,
            "paused" => UploadStatus::Paused,
            _ => UploadStatus::Failed,
        }
    }
}

#[derive(Clone)]
pub struct UploadHistoryDb {
    pool: SqlitePool,
}

impl UploadHistoryDb {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    pub async fn create_upload(&self, record: &UploadRecord) -> Result<(), String> {
        let tags_json = serde_json::to_string(&record.tags)
            .map_err(|e| format!("Failed to serialize tags: {}", e))?;
        
        sqlx::query(
            r#"
            INSERT INTO upload_history (
                id, file_path, file_name, file_size, platform, status, progress,
                title, description, tags, privacy, category, playlist_id, thumbnail_path,
                created_at, retry_count, bytes_uploaded
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&record.id)
        .bind(&record.file_path)
        .bind(&record.file_name)
        .bind(record.file_size)
        .bind(&record.platform)
        .bind(record.status.to_string())
        .bind(record.progress)
        .bind(&record.title)
        .bind(&record.description)
        .bind(&tags_json)
        .bind(&record.privacy)
        .bind(&record.category)
        .bind(&record.playlist_id)
        .bind(&record.thumbnail_path)
        .bind(record.created_at.to_rfc3339())
        .bind(record.retry_count as i32)
        .bind(record.bytes_uploaded)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Failed to create upload record: {}", e))?;
        
        Ok(())
    }

    pub async fn update_status(
        &self, 
        id: &str, 
        status: UploadStatus,
        error_message: Option<String>
    ) -> Result<(), String> {
        let now = Utc::now().to_rfc3339();
        let status_str = status.to_string();
        
        match status {
            UploadStatus::Uploading => {
                sqlx::query(
                    "UPDATE upload_history SET status = ?, started_at = ? WHERE id = ?"
                )
                .bind(&status_str)
                .bind(&now)
                .bind(id)
                .execute(&self.pool)
                .await
                .map_err(|e| format!("Failed to update status: {}", e))?;
            },
            UploadStatus::Completed => {
                sqlx::query(
                    "UPDATE upload_history SET status = ?, completed_at = ? WHERE id = ?"
                )
                .bind(&status_str)
                .bind(&now)
                .bind(id)
                .execute(&self.pool)
                .await
                .map_err(|e| format!("Failed to update status: {}", e))?;
            },
            UploadStatus::Failed | UploadStatus::Cancelled => {
                sqlx::query(
                    "UPDATE upload_history SET status = ?, error_message = ? WHERE id = ?"
                )
                .bind(&status_str)
                .bind(&error_message)
                .bind(id)
                .execute(&self.pool)
                .await
                .map_err(|e| format!("Failed to update status: {}", e))?;
            },
            _ => {
                sqlx::query(
                    "UPDATE upload_history SET status = ? WHERE id = ?"
                )
                .bind(&status_str)
                .bind(id)
                .execute(&self.pool)
                .await
                .map_err(|e| format!("Failed to update status: {}", e))?;
            }
        };
        
        Ok(())
    }

    pub async fn update_progress(
        &self,
        id: &str,
        progress: f32,
        bytes_uploaded: i64
    ) -> Result<(), String> {
        sqlx::query(
            "UPDATE upload_history SET progress = ?, bytes_uploaded = ? WHERE id = ?"
        )
        .bind(progress)
        .bind(bytes_uploaded)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Failed to update progress: {}", e))?;
        
        Ok(())
    }

    pub async fn update_platform_data(
        &self,
        id: &str,
        platform_id: &str,
        platform_url: &str
    ) -> Result<(), String> {
        sqlx::query(
            "UPDATE upload_history SET platform_id = ?, platform_url = ? WHERE id = ?"
        )
        .bind(platform_id)
        .bind(platform_url)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Failed to update platform data: {}", e))?;
        
        Ok(())
    }

    pub async fn save_resume_data(
        &self,
        id: &str,
        upload_url: &str,
        upload_token: Option<&str>
    ) -> Result<(), String> {
        sqlx::query(
            "UPDATE upload_history SET upload_url = ?, upload_token = ? WHERE id = ?"
        )
        .bind(upload_url)
        .bind(upload_token)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| format!("Failed to save resume data: {}", e))?;
        
        Ok(())
    }

    pub async fn increment_retry_count(&self, id: &str) -> Result<u32, String> {
        let result = sqlx::query(
            "UPDATE upload_history SET retry_count = retry_count + 1 WHERE id = ? RETURNING retry_count"
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| format!("Failed to increment retry count: {}", e))?;
        
        Ok(result.try_get::<i32, _>("retry_count")
            .map_err(|e| format!("Failed to get retry count: {}", e))? as u32)
    }

    pub async fn get_upload(&self, id: &str) -> Result<Option<UploadRecord>, String> {
        let row = sqlx::query(
            "SELECT * FROM upload_history WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| format!("Failed to get upload: {}", e))?;
        
        if let Some(row) = row {
            let tags_json: Option<String> = row.try_get("tags").ok();
            let tags: Vec<String> = if let Some(tags_json) = tags_json {
                serde_json::from_str(&tags_json).unwrap_or_default()
            } else {
                vec![]
            };
            
            Ok(Some(UploadRecord {
                id: row.try_get("id").map_err(|e| format!("Failed to get id: {}", e))?,
                file_path: row.try_get("file_path").map_err(|e| format!("Failed to get file_path: {}", e))?,
                file_name: row.try_get("file_name").map_err(|e| format!("Failed to get file_name: {}", e))?,
                file_size: row.try_get("file_size").map_err(|e| format!("Failed to get file_size: {}", e))?,
                platform: row.try_get("platform").map_err(|e| format!("Failed to get platform: {}", e))?,
                status: UploadStatus::from(row.try_get::<String, _>("status").map_err(|e| format!("Failed to get status: {}", e))?),
                progress: row.try_get("progress").map_err(|e| format!("Failed to get progress: {}", e))?,
                title: row.try_get("title").map_err(|e| format!("Failed to get title: {}", e))?,
                description: row.try_get("description").ok(),
                tags,
                privacy: row.try_get("privacy").map_err(|e| format!("Failed to get privacy: {}", e))?,
                category: row.try_get("category").ok(),
                playlist_id: row.try_get("playlist_id").ok(),
                thumbnail_path: row.try_get("thumbnail_path").ok(),
                platform_id: row.try_get("platform_id").ok(),
                platform_url: row.try_get("platform_url").ok(),
                created_at: DateTime::parse_from_rfc3339(&row.try_get::<String, _>("created_at").map_err(|e| format!("Failed to get created_at: {}", e))?)
                    .unwrap()
                    .with_timezone(&Utc),
                started_at: row.try_get::<Option<String>, _>("started_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                completed_at: row.try_get::<Option<String>, _>("completed_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                error_message: row.try_get("error_message").ok(),
                retry_count: row.try_get::<i32, _>("retry_count").map_err(|e| format!("Failed to get retry_count: {}", e))? as u32,
                upload_url: row.try_get("upload_url").ok(),
                upload_token: row.try_get("upload_token").ok(),
                bytes_uploaded: row.try_get::<Option<i64>, _>("bytes_uploaded").ok().flatten().unwrap_or(0),
            }))
        } else {
            Ok(None)
        }
    }

    pub async fn get_recent_uploads(&self, limit: i64) -> Result<Vec<UploadRecord>, String> {
        let rows = sqlx::query(
            "SELECT * FROM upload_history ORDER BY created_at DESC LIMIT ?"
        )
        .bind(limit)
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Failed to get recent uploads: {}", e))?;
        
        let mut uploads = Vec::new();
        for row in rows {
            let tags_json: Option<String> = row.try_get("tags").ok();
            let tags: Vec<String> = if let Some(tags_json) = tags_json {
                serde_json::from_str(&tags_json).unwrap_or_default()
            } else {
                vec![]
            };
            
            uploads.push(UploadRecord {
                id: row.try_get("id").map_err(|e| format!("Failed to get id: {}", e))?,
                file_path: row.try_get("file_path").map_err(|e| format!("Failed to get file_path: {}", e))?,
                file_name: row.try_get("file_name").map_err(|e| format!("Failed to get file_name: {}", e))?,
                file_size: row.try_get("file_size").map_err(|e| format!("Failed to get file_size: {}", e))?,
                platform: row.try_get("platform").map_err(|e| format!("Failed to get platform: {}", e))?,
                status: UploadStatus::from(row.try_get::<String, _>("status").map_err(|e| format!("Failed to get status: {}", e))?),
                progress: row.try_get("progress").map_err(|e| format!("Failed to get progress: {}", e))?,
                title: row.try_get("title").map_err(|e| format!("Failed to get title: {}", e))?,
                description: row.try_get("description").ok(),
                tags,
                privacy: row.try_get("privacy").map_err(|e| format!("Failed to get privacy: {}", e))?,
                category: row.try_get("category").ok(),
                playlist_id: row.try_get("playlist_id").ok(),
                thumbnail_path: row.try_get("thumbnail_path").ok(),
                platform_id: row.try_get("platform_id").ok(),
                platform_url: row.try_get("platform_url").ok(),
                created_at: DateTime::parse_from_rfc3339(&row.try_get::<String, _>("created_at").map_err(|e| format!("Failed to get created_at: {}", e))?)
                    .unwrap()
                    .with_timezone(&Utc),
                started_at: row.try_get::<Option<String>, _>("started_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                completed_at: row.try_get::<Option<String>, _>("completed_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                error_message: row.try_get("error_message").ok(),
                retry_count: row.try_get::<i32, _>("retry_count").map_err(|e| format!("Failed to get retry_count: {}", e))? as u32,
                upload_url: row.try_get("upload_url").ok(),
                upload_token: row.try_get("upload_token").ok(),
                bytes_uploaded: row.try_get::<Option<i64>, _>("bytes_uploaded").ok().flatten().unwrap_or(0),
            });
        }
        
        Ok(uploads)
    }

    pub async fn get_resumable_uploads(&self) -> Result<Vec<UploadRecord>, String> {
        let rows = sqlx::query(
            r#"
            SELECT * FROM upload_history 
            WHERE status IN ('uploading', 'paused', 'failed') 
            AND upload_url IS NOT NULL
            ORDER BY created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| format!("Failed to get resumable uploads: {}", e))?;
        
        let mut uploads = Vec::new();
        for row in rows {
            let tags_json: Option<String> = row.try_get("tags").ok();
            let tags: Vec<String> = if let Some(tags_json) = tags_json {
                serde_json::from_str(&tags_json).unwrap_or_default()
            } else {
                vec![]
            };
            
            uploads.push(UploadRecord {
                id: row.try_get("id").map_err(|e| format!("Failed to get id: {}", e))?,
                file_path: row.try_get("file_path").map_err(|e| format!("Failed to get file_path: {}", e))?,
                file_name: row.try_get("file_name").map_err(|e| format!("Failed to get file_name: {}", e))?,
                file_size: row.try_get("file_size").map_err(|e| format!("Failed to get file_size: {}", e))?,
                platform: row.try_get("platform").map_err(|e| format!("Failed to get platform: {}", e))?,
                status: UploadStatus::from(row.try_get::<String, _>("status").map_err(|e| format!("Failed to get status: {}", e))?),
                progress: row.try_get("progress").map_err(|e| format!("Failed to get progress: {}", e))?,
                title: row.try_get("title").map_err(|e| format!("Failed to get title: {}", e))?,
                description: row.try_get("description").ok(),
                tags,
                privacy: row.try_get("privacy").map_err(|e| format!("Failed to get privacy: {}", e))?,
                category: row.try_get("category").ok(),
                playlist_id: row.try_get("playlist_id").ok(),
                thumbnail_path: row.try_get("thumbnail_path").ok(),
                platform_id: row.try_get("platform_id").ok(),
                platform_url: row.try_get("platform_url").ok(),
                created_at: DateTime::parse_from_rfc3339(&row.try_get::<String, _>("created_at").map_err(|e| format!("Failed to get created_at: {}", e))?)
                    .unwrap()
                    .with_timezone(&Utc),
                started_at: row.try_get::<Option<String>, _>("started_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                completed_at: row.try_get::<Option<String>, _>("completed_at").ok().flatten()
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                error_message: row.try_get("error_message").ok(),
                retry_count: row.try_get::<i32, _>("retry_count").map_err(|e| format!("Failed to get retry_count: {}", e))? as u32,
                upload_url: row.try_get("upload_url").ok(),
                upload_token: row.try_get("upload_token").ok(),
                bytes_uploaded: row.try_get::<Option<i64>, _>("bytes_uploaded").ok().flatten().unwrap_or(0),
            });
        }
        
        Ok(uploads)
    }

    pub async fn delete_upload(&self, id: &str) -> Result<(), String> {
        sqlx::query("DELETE FROM upload_history WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await
            .map_err(|e| format!("Failed to delete upload: {}", e))?;
        
        Ok(())
    }
}