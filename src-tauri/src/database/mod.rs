pub mod schema;
pub mod upload_history;
pub mod downloads;

use sqlx::{SqlitePool, sqlite::SqlitePoolOptions};
use std::path::PathBuf;

pub async fn init_database(app_data_dir: PathBuf) -> Result<SqlitePool, String> {
    // Create database directory if it doesn't exist
    let db_dir = app_data_dir.join("data");
    std::fs::create_dir_all(&db_dir)
        .map_err(|e| format!("Failed to create database directory: {}", e))?;
    
    let db_path = db_dir.join("flowdownload.db");
    let db_url = format!("sqlite:{}?mode=rwc", db_path.to_string_lossy());
    
    // Create connection pool with automatic creation
    let pool = SqlitePoolOptions::new()
        .max_connections(5)
        .connect(&db_url)
        .await
        .map_err(|e| format!("Failed to connect to database: {}", e))?;
    
    // Run migrations
    schema::run_migrations(&pool).await?;
    
    Ok(pool)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_init_database() {
        let temp_dir = TempDir::new().unwrap();
        let pool = init_database(temp_dir.path().to_path_buf()).await.unwrap();
        
        // Test connection
        let result = sqlx::query("SELECT 1 as test")
            .fetch_one(&pool)
            .await;
        
        assert!(result.is_ok());
    }
}