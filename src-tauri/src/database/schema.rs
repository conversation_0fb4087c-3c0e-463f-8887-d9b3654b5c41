use sqlx::SqlitePool;

pub async fn run_migrations(pool: &SqlitePool) -> Result<(), String> {
    // Create upload_history table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS upload_history (
            id TEXT PRIMARY KEY,
            file_path TEXT NOT NULL,
            file_name TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            platform TEXT NOT NULL,
            status TEXT NOT NULL,
            progress REAL NOT NULL DEFAULT 0.0,
            
            -- Upload configuration
            title TEXT NOT NULL,
            description TEXT,
            tags TEXT, -- JSON array
            privacy TEXT NOT NULL,
            category TEXT,
            playlist_id TEXT,
            thumbnail_path TEXT,
            
            -- Platform-specific data
            platform_id TEXT, -- YouTube video ID, TikTok share ID, etc.
            platform_url TEXT,
            
            -- Timestamps
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            started_at TEXT,
            completed_at TEXT,
            
            -- Error tracking
            error_message TEXT,
            retry_count INTEGER NOT NULL DEFAULT 0,
            
            -- Resume data
            upload_url TEXT, -- For resumable uploads
            upload_token TEXT, -- Platform-specific resume token
            bytes_uploaded INTEGER DEFAULT 0
        )
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create upload_history table: {}", e))?;

    // Create index for faster queries
    sqlx::query(
        r#"
        CREATE INDEX IF NOT EXISTS idx_upload_history_status 
        ON upload_history(status)
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create status index: {}", e))?;

    sqlx::query(
        r#"
        CREATE INDEX IF NOT EXISTS idx_upload_history_created_at 
        ON upload_history(created_at DESC)
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create created_at index: {}", e))?;

    // Create upload_chunks table for tracking chunked uploads
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS upload_chunks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            upload_id TEXT NOT NULL,
            chunk_number INTEGER NOT NULL,
            chunk_size INTEGER NOT NULL,
            uploaded BOOLEAN NOT NULL DEFAULT 0,
            uploaded_at TEXT,
            
            FOREIGN KEY (upload_id) REFERENCES upload_history(id) ON DELETE CASCADE,
            UNIQUE(upload_id, chunk_number)
        )
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create upload_chunks table: {}", e))?;

    // Create platform_tokens table for storing refresh tokens
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS platform_tokens (
            platform TEXT PRIMARY KEY,
            access_token TEXT NOT NULL,
            refresh_token TEXT,
            expires_at TEXT,
            user_id TEXT,
            user_name TEXT,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create platform_tokens table: {}", e))?;

    // Create upload_analytics table for tracking performance
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS upload_analytics (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            upload_id TEXT NOT NULL,
            platform TEXT NOT NULL,
            views INTEGER DEFAULT 0,
            likes INTEGER DEFAULT 0,
            comments INTEGER DEFAULT 0,
            shares INTEGER DEFAULT 0,
            watch_time_minutes REAL DEFAULT 0.0,
            revenue_cents INTEGER DEFAULT 0,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (upload_id) REFERENCES upload_history(id) ON DELETE CASCADE
        )
        "#
    )
    .execute(pool)
    .await
    .map_err(|e| format!("Failed to create upload_analytics table: {}", e))?;

    // Create downloads table
    crate::database::downloads::create_downloads_table(pool).await
        .map_err(|e| format!("Failed to create downloads table: {}", e))?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::sqlite::SqlitePoolOptions;

    #[tokio::test]
    async fn test_migrations() {
        // Create in-memory database
        let pool = SqlitePoolOptions::new()
            .connect("sqlite::memory:")
            .await
            .unwrap();
        
        // Run migrations
        let result = run_migrations(&pool).await;
        assert!(result.is_ok());
        
        // Verify tables exist
        let tables = sqlx::query(
            "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        )
        .fetch_all(&pool)
        .await
        .unwrap();
        
        let table_names: Vec<String> = tables.iter()
            .map(|r| r.try_get::<String, _>("name").unwrap())
            .collect();
        
        assert!(table_names.contains(&"upload_history".to_string()));
        assert!(table_names.contains(&"upload_chunks".to_string()));
        assert!(table_names.contains(&"platform_tokens".to_string()));
        assert!(table_names.contains(&"upload_analytics".to_string()));
    }
}