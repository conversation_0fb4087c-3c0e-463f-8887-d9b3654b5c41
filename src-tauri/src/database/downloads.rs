use sqlx::{SqlitePool, Result};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, sqlx::FromRow)]
pub struct Download {
    pub id: String,
    pub url: String,
    pub file_name: String,
    pub file_path: String,
    pub file_size: i64,
    pub downloaded_size: i64,
    pub status: String,
    pub quality: String,
    pub format: String,
    pub speed: f64,
    pub eta: Option<i64>,
    pub error: Option<String>,
    pub created_at: String,
    pub updated_at: String,
    pub completed_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Paused,
    Completed,
    Failed,
    Cancelled,
}

impl std::fmt::Display for DownloadStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DownloadStatus::Pending => write!(f, "pending"),
            DownloadStatus::Downloading => write!(f, "downloading"),
            DownloadStatus::Paused => write!(f, "paused"),
            DownloadStatus::Completed => write!(f, "completed"),
            DownloadStatus::Failed => write!(f, "failed"),
            DownloadStatus::Cancelled => write!(f, "cancelled"),
        }
    }
}

impl std::str::FromStr for DownloadStatus {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "pending" => Ok(DownloadStatus::Pending),
            "downloading" => Ok(DownloadStatus::Downloading),
            "paused" => Ok(DownloadStatus::Paused),
            "completed" => Ok(DownloadStatus::Completed),
            "failed" => Ok(DownloadStatus::Failed),
            "cancelled" => Ok(DownloadStatus::Cancelled),
            _ => Err(format!("Unknown download status: {}", s)),
        }
    }
}

pub async fn create_downloads_table(pool: &SqlitePool) -> Result<()> {
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS downloads (
            id TEXT PRIMARY KEY,
            url TEXT NOT NULL,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER NOT NULL DEFAULT 0,
            downloaded_size INTEGER NOT NULL DEFAULT 0,
            status TEXT NOT NULL,
            quality TEXT NOT NULL,
            format TEXT NOT NULL,
            speed REAL NOT NULL DEFAULT 0.0,
            eta INTEGER,
            error TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            completed_at TEXT
        )
        "#
    )
    .execute(pool)
    .await?;

    // Create indices for faster queries
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_downloads_status ON downloads(status)")
        .execute(pool)
        .await?;
    
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_downloads_created_at ON downloads(created_at)")
        .execute(pool)
        .await?;

    Ok(())
}

pub async fn insert_download(pool: &SqlitePool, download: &Download) -> Result<()> {
    sqlx::query(
        r#"
        INSERT INTO downloads (
            id, url, file_name, file_path, file_size, downloaded_size,
            status, quality, format, speed, eta, error,
            created_at, updated_at, completed_at
        ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15)
        "#
    )
    .bind(&download.id)
    .bind(&download.url)
    .bind(&download.file_name)
    .bind(&download.file_path)
    .bind(download.file_size)
    .bind(download.downloaded_size)
    .bind(&download.status)
    .bind(&download.quality)
    .bind(&download.format)
    .bind(download.speed)
    .bind(download.eta)
    .bind(&download.error)
    .bind(&download.created_at)
    .bind(&download.updated_at)
    .bind(&download.completed_at)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn update_download(pool: &SqlitePool, download: &Download) -> Result<()> {
    sqlx::query(
        r#"
        UPDATE downloads SET
            file_size = ?1,
            downloaded_size = ?2,
            status = ?3,
            speed = ?4,
            eta = ?5,
            error = ?6,
            updated_at = ?7,
            completed_at = ?8
        WHERE id = ?9
        "#
    )
    .bind(download.file_size)
    .bind(download.downloaded_size)
    .bind(&download.status)
    .bind(download.speed)
    .bind(download.eta)
    .bind(&download.error)
    .bind(&download.updated_at)
    .bind(&download.completed_at)
    .bind(&download.id)
    .execute(pool)
    .await?;
    
    Ok(())
}

pub async fn get_download(pool: &SqlitePool, id: &str) -> Result<Option<Download>> {
    let download = sqlx::query_as::<_, Download>(
        r#"
        SELECT id, url, file_name, file_path, file_size, downloaded_size,
                status, quality, format, speed, eta, error,
                created_at, updated_at, completed_at
         FROM downloads WHERE id = ?1
        "#
    )
    .bind(id)
    .fetch_optional(pool)
    .await?;

    Ok(download)
}

pub async fn get_all_downloads(pool: &SqlitePool) -> Result<Vec<Download>> {
    let downloads = sqlx::query_as::<_, Download>(
        r#"
        SELECT id, url, file_name, file_path, file_size, downloaded_size,
                status, quality, format, speed, eta, error,
                created_at, updated_at, completed_at
         FROM downloads
         ORDER BY created_at DESC
        "#
    )
    .fetch_all(pool)
    .await?;

    Ok(downloads)
}

pub async fn get_active_downloads(pool: &SqlitePool) -> Result<Vec<Download>> {
    let downloads = sqlx::query_as::<_, Download>(
        r#"
        SELECT id, url, file_name, file_path, file_size, downloaded_size,
                status, quality, format, speed, eta, error,
                created_at, updated_at, completed_at
         FROM downloads
         WHERE status IN ('downloading', 'pending', 'paused')
         ORDER BY created_at DESC
        "#
    )
    .fetch_all(pool)
    .await?;

    Ok(downloads)
}

pub async fn delete_download(pool: &SqlitePool, id: &str) -> Result<()> {
    sqlx::query("DELETE FROM downloads WHERE id = ?1")
        .bind(id)
        .execute(pool)
        .await?;
    
    Ok(())
}

pub async fn get_downloads_by_status(pool: &SqlitePool, status: &str) -> Result<Vec<Download>> {
    let downloads = sqlx::query_as::<_, Download>(
        r#"
        SELECT id, url, file_name, file_path, file_size, downloaded_size,
                status, quality, format, speed, eta, error,
                created_at, updated_at, completed_at
         FROM downloads
         WHERE status = ?1
         ORDER BY created_at DESC
        "#
    )
    .bind(status)
    .fetch_all(pool)
    .await?;

    Ok(downloads)
}