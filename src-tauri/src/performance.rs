use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DownloadMetrics {
    pub domain: String,
    pub file_size: u64,
    pub download_time: Duration,
    pub average_speed: f64, // bytes per second
    pub peak_speed: f64,
    pub fragments_used: u8,
    pub retry_count: u32,
    pub success: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct BandwidthInfo {
    pub current_speed: f64,
    pub average_speed: f64,
    pub peak_speed: f64,
    pub total_downloaded: u64,
    pub active_downloads: u32,
}

pub struct PerformanceMonitor {
    metrics: Arc<Mutex<Vec<DownloadMetrics>>>,
    bandwidth_history: Arc<Mutex<Vec<(Instant, f64)>>>,
    domain_stats: Arc<Mutex<HashMap<String, DomainStats>>>,
}

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult)]
struct DomainStats {
    total_downloads: u32,
    successful_downloads: u32,
    total_bytes: u64,
    average_speed: f64,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(Mutex::new(Vec::new())),
            bandwidth_history: Arc::new(Mutex::new(Vec::new())),
            domain_stats: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn record_download(&self, metrics: DownloadMetrics) {
        // Update metrics
        let mut all_metrics = self.metrics.lock().await;
        all_metrics.push(metrics.clone());
        
        // Keep only last 1000 metrics
        if all_metrics.len() > 1000 {
            all_metrics.drain(0..100);
        }
        
        // Update domain stats
        let mut domain_stats = self.domain_stats.lock().await;
        let stats = domain_stats.entry(metrics.domain.clone()).or_default();
        stats.total_downloads += 1;
        if metrics.success {
            stats.successful_downloads += 1;
        }
        stats.total_bytes += metrics.file_size;
        
        // Update average speed
        let total_speed = stats.average_speed * (stats.total_downloads - 1) as f64 + metrics.average_speed;
        stats.average_speed = total_speed / stats.total_downloads as f64;
    }

    pub async fn update_bandwidth(&self, current_speed: f64) {
        let mut history = self.bandwidth_history.lock().await;
        history.push((Instant::now(), current_speed));
        
        // Keep only last 5 minutes of data
        let cutoff = Instant::now() - Duration::from_secs(300);
        history.retain(|(time, _)| *time > cutoff);
    }

    pub async fn get_bandwidth_info(&self) -> BandwidthInfo {
        let history = self.bandwidth_history.lock().await;
        
        if history.is_empty() {
            return BandwidthInfo {
                current_speed: 0.0,
                average_speed: 0.0,
                peak_speed: 0.0,
                total_downloaded: 0,
                active_downloads: 0,
            };
        }
        
        let current_speed = history.last().map(|(_, speed)| *speed).unwrap_or(0.0);
        let peak_speed = history.iter().map(|(_, speed)| *speed).fold(0.0, f64::max);
        let average_speed = history.iter().map(|(_, speed)| *speed).sum::<f64>() / history.len() as f64;
        
        let stats = self.domain_stats.lock().await;
        let total_downloaded = stats.values().map(|s| s.total_bytes).sum();
        
        BandwidthInfo {
            current_speed,
            average_speed,
            peak_speed,
            total_downloaded,
            active_downloads: 0, // This would be tracked elsewhere
        }
    }

    pub async fn get_optimal_fragment_count(&self, file_size: u64, domain: &str) -> u8 {
        let bandwidth_info = self.get_bandwidth_info().await;
        let domain_stats = self.domain_stats.lock().await;
        
        // Get domain-specific stats
        let domain_speed = domain_stats
            .get(domain)
            .map(|s| s.average_speed)
            .unwrap_or(bandwidth_info.average_speed);
        
        // Calculate optimal fragments based on file size and available bandwidth
        let fragments = if file_size < 10 * 1024 * 1024 {
            // Small files (< 10MB): 2 fragments
            2
        } else if file_size < 100 * 1024 * 1024 {
            // Medium files (< 100MB): 2-4 fragments
            if domain_speed > 10_000_000.0 { 4 } else { 2 }
        } else if file_size < 1024 * 1024 * 1024 {
            // Large files (< 1GB): 4-6 fragments
            if domain_speed > 20_000_000.0 { 6 } else { 4 }
        } else {
            // Very large files (> 1GB): 6-8 fragments
            if domain_speed > 50_000_000.0 { 8 } else { 6 }
        };
        
        fragments.min(8).max(2)
    }

    pub async fn get_success_rate(&self, domain: Option<&str>) -> f64 {
        let domain_stats = self.domain_stats.lock().await;
        
        if let Some(domain) = domain {
            domain_stats
                .get(domain)
                .map(|s| s.successful_downloads as f64 / s.total_downloads as f64 * 100.0)
                .unwrap_or(0.0)
        } else {
            // Overall success rate
            let total = domain_stats.values().map(|s| s.total_downloads).sum::<u32>();
            let successful = domain_stats.values().map(|s| s.successful_downloads).sum::<u32>();
            
            if total > 0 {
                successful as f64 / total as f64 * 100.0
            } else {
                0.0
            }
        }
    }
}

// Global performance monitor instance
lazy_static::lazy_static! {
    pub static ref PERFORMANCE_MONITOR: PerformanceMonitor = PerformanceMonitor::new();
}

// Command to get performance stats
#[tauri::command]
pub async fn get_performance_stats() -> Result<serde_json::Value, String> {
    let bandwidth_info = PERFORMANCE_MONITOR.get_bandwidth_info().await;
    let overall_success_rate = PERFORMANCE_MONITOR.get_success_rate(None).await;
    
    Ok(serde_json::json!({
        "bandwidth": bandwidth_info,
        "successRate": overall_success_rate,
        "timestamp": chrono::Utc::now().to_rfc3339(),
    }))
}