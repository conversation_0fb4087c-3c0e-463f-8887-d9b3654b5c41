use sentry::ClientOptions;
use std::env;
use std::sync::Arc;

pub fn init_sentry() {
    let dsn = env::var("SENTRY_DSN").unwrap_or_else(|_| {
        println!("SENTRY_DSN not set. Sentry error tracking disabled.");
        String::new()
    });

    if dsn.is_empty() {
        return;
    }

    let _guard = sentry::init((dsn, ClientOptions {
        release: sentry::release_name!(),
        environment: Some(if cfg!(debug_assertions) {
            "development".into()
        } else {
            "production".into()
        }),
        sample_rate: 1.0,
        traces_sample_rate: if cfg!(debug_assertions) { 1.0 } else { 0.1 },
        attach_stacktrace: true,
        send_default_pii: false,
        before_send: Some(Arc::new(|mut event: sentry::protocol::Event<'static>| -> Option<sentry::protocol::Event<'static>> {
            // Filter out sensitive information
            if let Some(ref mut user) = event.user {
                // Removed ip_address setting as field may not exist
            }
            
            // Add custom tags
            event.tags.insert("app".to_string(), "creator-os".to_string());
            event.tags.insert("platform".to_string(), std::env::consts::OS.to_string());
            
            Some(event)
        }) as Arc<dyn Fn(sentry::protocol::Event<'static>) -> Option<sentry::protocol::Event<'static>> + Send + Sync>),
        ..Default::default()
    }));

    // Set up panic handler
    let panic_handler = std::panic::take_hook();
    std::panic::set_hook(Box::new(move |panic_info| {
        sentry::capture_event(sentry::protocol::Event {
            message: Some(panic_info.to_string()),
            level: sentry::Level::Fatal,
            ..Default::default()
        });
        panic_handler(panic_info);
    }));

    println!("Sentry initialized successfully");
}

/// Capture an error with additional context
pub fn capture_error(error: &dyn std::error::Error, context: Option<&str>) {
    sentry::capture_error(error);
    
    if let Some(ctx) = context {
        sentry::configure_scope(|scope| {
            scope.set_tag("error_context", ctx);
        });
    }
}

/// Add breadcrumb for tracking user actions
pub fn add_breadcrumb(message: &str, category: &str) {
    sentry::add_breadcrumb(sentry::Breadcrumb {
        message: Some(message.to_string()),
        category: Some(category.to_string()),
        ..Default::default()
    });
}

/// Set user context
pub fn set_user_context(user_id: Option<String>, email: Option<String>) {
    sentry::configure_scope(|scope| {
        scope.set_user(Some(sentry::User {
            id: user_id,
            email,
            ..Default::default()
        }));
    });
}

/// Clear user context
pub fn clear_user_context() {
    sentry::configure_scope(|scope| {
        scope.set_user(None);
    });
}

/// Capture a custom message
pub fn capture_message(message: &str, level: sentry::Level) {
    sentry::capture_message(message, level);
}

/// Create a transaction for performance monitoring
pub fn start_transaction(name: &str, operation: &str) -> sentry::TransactionContext {
    sentry::TransactionContext::new(name, operation)
}