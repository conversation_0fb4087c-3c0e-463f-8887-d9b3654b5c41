use sha2::{Sha256, Digest};
use std::collections::HashMap;
use std::path::Path;
use std::time::Duration;
use tokio::fs::File;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_certificate_pinning: bool,
    pub verify_content_integrity: bool,
    pub scan_for_malware: bool,
    pub quarantine_suspicious_files: bool,
    pub audit_logging: bool,
}

#[derive(Debug, Clone)]
pub struct CertificatePin {
    pub domain: String,
    pub pin_sha256: Vec<String>, // Multiple pins for rotation
    pub backup_pins: Vec<String>,
}

pub struct SecurityManager {
    config: SecurityConfig,
    certificate_pins: HashMap<String, CertificatePin>,
    audit_log: Vec<AuditEntry>,
}

#[derive(Debug, <PERSON><PERSON>, Ser<PERSON>ize)]
struct AuditEntry {
    timestamp: chrono::DateTime<chrono::Utc>,
    action: String,
    url: String,
    result: String,
    details: Option<String>,
}

impl SecurityManager {
    pub fn new(config: SecurityConfig) -> Self {
        let mut certificate_pins = HashMap::new();
        
        // Add known certificate pins for major platforms
        certificate_pins.insert("youtube.com".to_string(), CertificatePin {
            domain: "youtube.com".to_string(),
            pin_sha256: vec![
                // Google's current certificate pins (these should be updated regularly)
                "hxqRlPTu1bMS/0DITB1SSu0vd4u/8l8TjPgfaAp63Gc=".to_string(),
                "Vjs8r4z+80wjNcr1YKepWQboSIRi63WsWXhIMN+eWys=".to_string(),
            ],
            backup_pins: vec![
                "58qRu/uxh4gFezqAcERupSkRYBlBAvfcw7mEjGPLnNU=".to_string(),
            ],
        });
        
        certificate_pins.insert("tiktok.com".to_string(), CertificatePin {
            domain: "tiktok.com".to_string(),
            pin_sha256: vec![
                // TikTok's certificate pins (example - should be verified)
                "k2v657xBsOVe1PQRwOsHsw3bsGT2VzIqz5K+59sNQws=".to_string(),
            ],
            backup_pins: vec![],
        });
        
        Self {
            config,
            certificate_pins,
            audit_log: Vec::new(),
        }
    }

    pub fn sanitize_url(&self, url: &str) -> Result<String, String> {
        // Remove any potentially dangerous characters or patterns
        let sanitized = url
            .trim()
            .replace('\0', "") // Null bytes
            .replace('\r', "") // Carriage returns
            .replace('\n', ""); // Newlines
        
        // Check for command injection attempts
        let dangerous_patterns = [";", "|", "&", "$", "`", "\\", "\"", "'", "<", ">"];
        for pattern in &dangerous_patterns {
            if sanitized.contains(pattern) && !sanitized.starts_with("http") {
                return Err(format!("URL contains potentially dangerous pattern: {}", pattern));
            }
        }
        
        // Validate URL format
        if !sanitized.starts_with("http://") && !sanitized.starts_with("https://") {
            // Allow non-URL inputs (like search terms) but mark them
            return Ok(format!("search:{}", sanitized));
        }
        
        Ok(sanitized)
    }

    pub async fn verify_certificate(&self, domain: &str, certificate_data: &[u8]) -> Result<(), String> {
        if !self.config.enable_certificate_pinning {
            return Ok(());
        }
        
        if let Some(pins) = self.certificate_pins.get(domain) {
            let mut hasher = Sha256::new();
            hasher.update(certificate_data);
            let result = hasher.finalize();
            use base64::Engine;
            let pin = base64::engine::general_purpose::STANDARD.encode(result);
            
            if pins.pin_sha256.contains(&pin) || pins.backup_pins.contains(&pin) {
                Ok(())
            } else {
                Err(format!("Certificate pin mismatch for domain: {}", domain))
            }
        } else {
            // No pins configured for this domain, allow
            Ok(())
        }
    }

    pub async fn verify_file_integrity(&self, file_path: &Path, expected_hash: Option<&str>) -> Result<(), String> {
        if !self.config.verify_content_integrity {
            return Ok(());
        }
        
        let mut file = File::open(file_path).await
            .map_err(|e| format!("Failed to open file for integrity check: {}", e))?;
        
        let mut hasher = Sha256::new();
        let mut buffer = vec![0; 8192];
        
        loop {
            let bytes_read = file.read(&mut buffer).await
                .map_err(|e| format!("Failed to read file: {}", e))?;
            
            if bytes_read == 0 {
                break;
            }
            
            hasher.update(&buffer[..bytes_read]);
        }
        
        let result = hasher.finalize();
        let calculated_hash = hex::encode(result);
        
        if let Some(expected) = expected_hash {
            if calculated_hash != expected {
                return Err("File integrity check failed: hash mismatch".to_string());
            }
        }
        
        Ok(())
    }

    pub async fn scan_for_malware(&self, _file_path: &Path) -> Result<(), String> {
        if !self.config.scan_for_malware {
            return Ok(());
        }
        
        // This would integrate with a malware scanning service
        // For now, we'll do basic checks
        
        // Check file extension against known dangerous types
        let dangerous_extensions = [".exe", ".scr", ".vbs", ".com", ".pif", ".cmd", ".bat"];
        if let Some(extension) = _file_path.extension() {
            let ext = extension.to_string_lossy().to_lowercase();
            if dangerous_extensions.iter().any(|&danger| ext.ends_with(danger)) {
                if self.config.quarantine_suspicious_files {
                    // Move to quarantine
                    return Err(format!("Suspicious file type detected: {}", ext));
                }
            }
        }
        
        Ok(())
    }

    pub async fn audit_download(&mut self, action: &str, url: &str, result: &str, details: Option<String>) {
        if !self.config.audit_logging {
            return;
        }
        
        let entry = AuditEntry {
            timestamp: chrono::Utc::now(),
            action: action.to_string(),
            url: url.to_string(),
            result: result.to_string(),
            details,
        };
        
        self.audit_log.push(entry.clone());
        
        // Keep only last 10000 entries
        if self.audit_log.len() > 10000 {
            self.audit_log.drain(0..1000);
        }
        
        // Also write to file for persistence
        if let Some(log_dir) = dirs::data_local_dir() {
            let log_file = log_dir.join("FlowDownload").join("audit.log");
            if let Ok(mut file) = tokio::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(&log_file)
                .await
            {
                let log_line = format!("{}\n", serde_json::to_string(&entry).unwrap_or_default());
                let _ = file.write_all(log_line.as_bytes()).await;
            }
        }
    }

    pub fn check_domain_whitelist(&self, domain: &str, allowed_domains: &[String]) -> bool {
        if allowed_domains.is_empty() {
            // No restrictions
            return true;
        }
        
        allowed_domains.iter().any(|allowed| {
            domain == allowed || domain.ends_with(&format!(".{}", allowed))
        })
    }
}

// Smart retry mechanism with exponential backoff
pub struct RetryStrategy {
    max_attempts: u32,
    base_delay: Duration,
    max_delay: Duration,
    current_attempt: u32,
}

impl RetryStrategy {
    pub fn new() -> Self {
        Self {
            max_attempts: 3,
            base_delay: Duration::from_secs(1),
            max_delay: Duration::from_secs(60),
            current_attempt: 0,
        }
    }
    
    pub fn should_retry(&self) -> bool {
        self.current_attempt < self.max_attempts
    }
    
    pub fn next_delay(&mut self) -> Duration {
        self.current_attempt += 1;
        
        // Exponential backoff with jitter
        let delay = self.base_delay * 2u32.pow(self.current_attempt - 1);
        let jitter = rand::random::<f32>() * 0.3; // 0-30% jitter
        let delay_with_jitter = delay.mul_f32(1.0 + jitter);
        
        delay_with_jitter.min(self.max_delay)
    }
    
    pub fn reset(&mut self) {
        self.current_attempt = 0;
    }
}

// Global security instance
lazy_static::lazy_static! {
    pub static ref SECURITY_MANAGER: tokio::sync::Mutex<SecurityManager> = {
        let config = SecurityConfig {
            enable_certificate_pinning: true,
            verify_content_integrity: true,
            scan_for_malware: false,
            quarantine_suspicious_files: true,
            audit_logging: true,
        };
        tokio::sync::Mutex::new(SecurityManager::new(config))
    };
}

// Command to get security audit log
#[tauri::command]
pub async fn get_security_audit_log(limit: Option<usize>) -> Result<Vec<serde_json::Value>, String> {
    let security = SECURITY_MANAGER.lock().await;
    let limit = limit.unwrap_or(100);
    
    let logs: Vec<serde_json::Value> = security.audit_log
        .iter()
        .rev()
        .take(limit)
        .map(|entry| serde_json::to_value(entry).unwrap_or(serde_json::Value::Null))
        .collect();
    
    Ok(logs)
}