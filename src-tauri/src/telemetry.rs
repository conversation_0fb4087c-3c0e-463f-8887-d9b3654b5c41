use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

/// Production-grade telemetry system
pub struct TelemetrySystem {
    events: Arc<Mutex<Vec<TelemetryEvent>>>,
    metrics: Arc<Mutex<MetricsCollector>>,
    session: SessionInfo,
    config: TelemetryConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryConfig {
    pub enabled: bool,
    pub anonymous: bool,
    pub sample_rate: f32, // 0.0 to 1.0
    pub batch_size: usize,
    pub flush_interval_secs: u64,
    pub endpoint: Option<String>,
}

impl Default for TelemetryConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            anonymous: true,
            sample_rate: 0.1, // 10% sampling in production
            batch_size: 100,
            flush_interval_secs: 300, // 5 minutes
            endpoint: None,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize)]
struct SessionInfo {
    session_id: String,
    user_id: Option<String>,
    app_version: String,
    os: String,
    arch: String,
    locale: String,
    started_at: DateTime<Utc>,
    license_type: String,
}

#[derive(Debug, Clone, Serialize)]
pub struct TelemetryEvent {
    event_type: EventType,
    timestamp: DateTime<Utc>,
    properties: HashMap<String, serde_json::Value>,
    duration_ms: Option<u64>,
}

#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "snake_case")]
pub enum EventType {
    // User actions
    DownloadStarted,
    DownloadCompleted,
    DownloadFailed,
    UploadStarted,
    UploadCompleted,
    UploadFailed,
    
    // Feature usage
    FeatureUsed { feature: String },
    SettingChanged { setting: String },
    
    // Performance
    PerformanceMetric { metric: String },
    
    // Business events
    LicenseActivated,
    SubscriptionStarted,
    SubscriptionCancelled,
    
    // Errors
    ErrorOccurred { severity: String },
    CrashDetected,
}

/// Metrics collector for performance monitoring
pub struct MetricsCollector {
    counters: HashMap<String, u64>,
    gauges: HashMap<String, f64>,
    histograms: HashMap<String, Vec<f64>>,
    timers: HashMap<String, Vec<std::time::Duration>>,
}

impl MetricsCollector {
    pub fn new() -> Self {
        Self {
            counters: HashMap::new(),
            gauges: HashMap::new(),
            histograms: HashMap::new(),
            timers: HashMap::new(),
        }
    }

    pub fn increment(&mut self, name: &str, value: u64) {
        *self.counters.entry(name.to_string()).or_insert(0) += value;
    }

    pub fn gauge(&mut self, name: &str, value: f64) {
        self.gauges.insert(name.to_string(), value);
    }

    pub fn histogram(&mut self, name: &str, value: f64) {
        self.histograms.entry(name.to_string()).or_insert_with(Vec::new).push(value);
    }

    pub fn time(&mut self, name: &str, duration: std::time::Duration) {
        self.timers.entry(name.to_string()).or_insert_with(Vec::new).push(duration);
    }

    pub fn get_summary(&self) -> MetricsSummary {
        let mut summary = MetricsSummary::default();
        
        // Calculate percentiles for histograms
        for (name, values) in &self.histograms {
            if !values.is_empty() {
                let mut sorted = values.clone();
                sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());
                
                summary.percentiles.insert(name.clone(), Percentiles {
                    p50: sorted[sorted.len() / 2],
                    p90: sorted[sorted.len() * 9 / 10],
                    p99: sorted[sorted.len() * 99 / 100],
                });
            }
        }
        
        summary.counters = self.counters.clone();
        summary.gauges = self.gauges.clone();
        
        summary
    }
}

#[derive(Debug, Clone, Serialize, Default)]
pub struct MetricsSummary {
    counters: HashMap<String, u64>,
    gauges: HashMap<String, f64>,
    percentiles: HashMap<String, Percentiles>,
}

#[derive(Debug, Clone, Serialize)]
struct Percentiles {
    p50: f64,
    p90: f64,
    p99: f64,
}

impl TelemetrySystem {
    pub fn new(config: TelemetryConfig) -> Self {
        let session = SessionInfo {
            session_id: uuid::Uuid::new_v4().to_string(),
            user_id: None, // Set after authentication
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            locale: "en-US".to_string(), // TODO: Detect actual locale
            started_at: Utc::now(),
            license_type: "trial".to_string(),
        };
        
        let system = Self {
            events: Arc::new(Mutex::new(Vec::new())),
            metrics: Arc::new(Mutex::new(MetricsCollector::new())),
            session,
            config: config.clone(),
        };
        
        // Start background flush task
        if config.enabled {
            let system_clone = system.clone();
            tokio::spawn(async move {
                system_clone.flush_loop().await;
            });
        }
        
        system
    }

    pub async fn track(&self, event_type: EventType, properties: HashMap<String, serde_json::Value>) {
        if !self.config.enabled {
            return;
        }
        
        // Sample events based on configuration
        if rand::random::<f32>() > self.config.sample_rate {
            return;
        }
        
        let event = TelemetryEvent {
            event_type,
            timestamp: Utc::now(),
            properties,
            duration_ms: None,
        };
        
        let mut events = self.events.lock().await;
        events.push(event);
        
        // Flush if we've reached batch size
        if events.len() >= self.config.batch_size {
            drop(events);
            self.flush().await;
        }
    }

    pub async fn track_timing(&self, event_type: EventType, duration: std::time::Duration, properties: HashMap<String, serde_json::Value>) {
        if !self.config.enabled {
            return;
        }
        
        let event = TelemetryEvent {
            event_type,
            timestamp: Utc::now(),
            properties,
            duration_ms: Some(duration.as_millis() as u64),
        };
        
        let mut events = self.events.lock().await;
        events.push(event);
    }

    pub async fn increment_counter(&self, name: &str, value: u64) {
        let mut metrics = self.metrics.lock().await;
        metrics.increment(name, value);
    }

    pub async fn record_gauge(&self, name: &str, value: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.gauge(name, value);
    }

    async fn flush(&self) {
        let events: Vec<TelemetryEvent> = {
            let mut events = self.events.lock().await;
            std::mem::take(&mut *events)
        };
        
        if events.is_empty() {
            return;
        }
        
        let metrics_summary = {
            let metrics = self.metrics.lock().await;
            metrics.get_summary()
        };
        
        let payload = TelemetryPayload {
            session: self.session.clone(),
            events,
            metrics: metrics_summary,
            timestamp: Utc::now(),
        };
        
        // In production, send to telemetry endpoint
        if let Some(endpoint) = &self.config.endpoint {
            self.send_telemetry(endpoint, payload).await;
        } else {
            // For development, just log
            log::debug!("Telemetry payload: {} events", payload.events.len());
        }
    }

    async fn flush_loop(&self) {
        let interval = std::time::Duration::from_secs(self.config.flush_interval_secs);
        
        loop {
            tokio::time::sleep(interval).await;
            self.flush().await;
        }
    }

    async fn send_telemetry(&self, _endpoint: &str, _payload: TelemetryPayload) {
        // Implement actual sending logic
        // This would use reqwest to POST to the telemetry service
        // with retry logic and error handling
    }

    pub fn set_user_id(&mut self, user_id: String) {
        self.session.user_id = Some(user_id);
    }

    pub fn set_license_type(&mut self, license_type: String) {
        self.session.license_type = license_type;
    }
}

impl Clone for TelemetrySystem {
    fn clone(&self) -> Self {
        Self {
            events: self.events.clone(),
            metrics: self.metrics.clone(),
            session: self.session.clone(),
            config: self.config.clone(),
        }
    }
}

#[derive(Debug, Serialize)]
struct TelemetryPayload {
    session: SessionInfo,
    events: Vec<TelemetryEvent>,
    metrics: MetricsSummary,
    timestamp: DateTime<Utc>,
}

/// Performance timer for automatic timing
pub struct PerfTimer {
    name: String,
    start: std::time::Instant,
    telemetry: Option<TelemetrySystem>,
}

impl PerfTimer {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            start: std::time::Instant::now(),
            telemetry: None,
        }
    }

    pub fn with_telemetry(name: &str, telemetry: TelemetrySystem) -> Self {
        Self {
            name: name.to_string(),
            start: std::time::Instant::now(),
            telemetry: Some(telemetry),
        }
    }
}

impl Drop for PerfTimer {
    fn drop(&mut self) {
        let duration = self.start.elapsed();
        log::debug!("{} took {:?}", self.name, duration);
        
        if let Some(telemetry) = &self.telemetry {
            let telemetry = telemetry.clone();
            let name = self.name.clone();
            tokio::spawn(async move {
                let mut metrics = telemetry.metrics.lock().await;
                metrics.time(&name, duration);
            });
        }
    }
}

// Global telemetry instance
lazy_static::lazy_static! {
    pub static ref TELEMETRY: TelemetrySystem = {
        let config = TelemetryConfig::default();
        TelemetrySystem::new(config)
    };
}

/// Helper macros for easy telemetry
#[macro_export]
macro_rules! track_event {
    ($event:expr) => {
        TELEMETRY.track($event, std::collections::HashMap::new()).await
    };
    ($event:expr, $($key:expr => $value:expr),*) => {{
        let mut props = std::collections::HashMap::new();
        $(
            props.insert($key.to_string(), serde_json::json!($value));
        )*
        TELEMETRY.track($event, props).await
    }};
}

#[macro_export]
macro_rules! time_operation {
    ($name:expr, $body:expr) => {{
        let _timer = PerfTimer::with_telemetry($name, TELEMETRY.clone());
        $body
    }};
}

// Tauri commands
#[tauri::command]
pub async fn track_event(event_type: String, properties: serde_json::Value) -> Result<(), String> {
    let telemetry = crate::TELEMETRY.lock().await;
    
    // Convert string event type to enum
    let event = match event_type.as_str() {
        "download_started" => EventType::DownloadStarted,
        "download_completed" => EventType::DownloadCompleted,
        "download_failed" => EventType::DownloadFailed,
        "upload_started" => EventType::UploadStarted,
        "upload_completed" => EventType::UploadCompleted,
        "upload_failed" => EventType::UploadFailed,
        _ => EventType::FeatureUsed { feature: event_type },
    };
    
    // Convert JSON value to HashMap
    let props: HashMap<String, serde_json::Value> = match properties {
        serde_json::Value::Object(map) => {
            map.into_iter().collect()
        }
        _ => HashMap::new(),
    };
    
    telemetry.track(event, props).await;
    Ok(())
}