use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use reqwest::{Client, header};
use futures::StreamExt;
use chrono::Utc;
use uuid::Uuid;
use serde::Serialize;
use tauri::{AppHandle, Emitter};
use sqlx::SqlitePool;

use crate::database::downloads::{Download, DownloadStatus, insert_download, update_download, get_download};

#[derive(Debug, Clone, Serialize)]
pub struct DownloadProgress {
    pub id: String,
    pub downloaded: u64,
    pub total: u64,
    pub speed: f64,
    pub eta: Option<i64>,
}

#[derive(Debug)]
pub struct ActiveDownload {
    pub download: Download,
    pub cancel_token: tokio_util::sync::CancellationToken,
}

pub struct DownloadManager {
    active_downloads: Arc<RwLock<HashMap<String, ActiveDownload>>>,
    client: Client,
    app_handle: AppHandle,
    db_pool: SqlitePool,
}

impl DownloadManager {
    pub fn new(app_handle: AppHandle, db_pool: SqlitePool) -> Self {
        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .unwrap();

        Self {
            active_downloads: Arc::new(RwLock::new(HashMap::new())),
            client,
            app_handle,
            db_pool,
        }
    }

    pub async fn start_download(
        &self,
        url: String,
        file_name: String,
        download_path: String,
        quality: String,
        format: String,
    ) -> Result<String, String> {
        let id = Uuid::new_v4().to_string();
        let file_path = Path::new(&download_path).join(&file_name);
        
        // Create download record
        let download = Download {
            id: id.clone(),
            url: url.clone(),
            file_name: file_name.clone(),
            file_path: file_path.to_string_lossy().to_string(),
            file_size: 0,
            downloaded_size: 0,
            status: DownloadStatus::Pending.to_string(),
            quality,
            format,
            speed: 0.0,
            eta: None,
            error: None,
            created_at: Utc::now().to_rfc3339(),
            updated_at: Utc::now().to_rfc3339(),
            completed_at: None,
        };

        // Save to database
        insert_download(&self.db_pool, &download).await
            .map_err(|e| format!("Failed to insert download: {}", e))?;

        // Create cancellation token
        let cancel_token = tokio_util::sync::CancellationToken::new();
        
        // Add to active downloads
        {
            let mut active = self.active_downloads.write().await;
            active.insert(id.clone(), ActiveDownload {
                download: download.clone(),
                cancel_token: cancel_token.clone(),
            });
        }

        // Start download task
        let manager = self.clone();
        let download_id = id.clone();
        tokio::spawn(async move {
            if let Err(e) = manager.download_file(download_id).await {
                eprintln!("Download error: {}", e);
            }
        });

        Ok(id)
    }

    async fn download_file(&self, download_id: String) -> Result<(), String> {
        // Get download info
        let (url, file_path, cancel_token) = {
            let active = self.active_downloads.read().await;
            let active_download = active.get(&download_id).ok_or("Download not found")?;
            (
                active_download.download.url.clone(),
                active_download.download.file_path.clone(),
                active_download.cancel_token.clone(),
            )
        };

        // Update status to downloading
        self.update_download_status(&download_id, DownloadStatus::Downloading).await?;

        // Make HTTP request
        let response = self.client
            .get(&url)
            .send()
            .await
            .map_err(|e| format!("Failed to start download: {}", e))?;

        let total_size = response
            .headers()
            .get(header::CONTENT_LENGTH)
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<u64>().ok())
            .unwrap_or(0);

        // Update file size
        self.update_download_size(&download_id, total_size, 0).await?;

        // Create parent directory if it doesn't exist
        if let Some(parent) = Path::new(&file_path).parent() {
            fs::create_dir_all(parent).await
                .map_err(|e| format!("Failed to create directory: {}", e))?;
        }

        // Create file
        let mut file = fs::File::create(&file_path).await
            .map_err(|e| format!("Failed to create file: {}", e))?;

        // Download with progress tracking
        let mut stream = response.bytes_stream();
        let mut downloaded = 0u64;
        let mut last_update = std::time::Instant::now();
        let mut last_downloaded = 0u64;

        while let Some(chunk_result) = stream.next().await {
            // Check for cancellation
            if cancel_token.is_cancelled() {
                self.update_download_status(&download_id, DownloadStatus::Cancelled).await?;
                return Ok(());
            }

            let chunk = chunk_result.map_err(|e| format!("Download error: {}", e))?;
            
            file.write_all(&chunk).await
                .map_err(|e| format!("Failed to write to file: {}", e))?;
            
            downloaded += chunk.len() as u64;

            // Update progress every 100ms
            let now = std::time::Instant::now();
            if now.duration_since(last_update).as_millis() > 100 {
                let elapsed = now.duration_since(last_update).as_secs_f64();
                let bytes_downloaded = downloaded - last_downloaded;
                let speed = (bytes_downloaded as f64) / elapsed;
                
                let eta = if speed > 0.0 {
                    Some(((total_size - downloaded) as f64 / speed) as i64)
                } else {
                    None
                };

                self.update_download_progress(&download_id, downloaded, total_size, speed, eta).await?;
                
                // Emit progress event
                self.app_handle.emit("download-progress", DownloadProgress {
                    id: download_id.clone(),
                    downloaded,
                    total: total_size,
                    speed,
                    eta,
                }).unwrap();

                last_update = now;
                last_downloaded = downloaded;
            }
        }

        // Ensure file is written
        file.flush().await
            .map_err(|e| format!("Failed to flush file: {}", e))?;

        // Update status to completed
        self.update_download_status(&download_id, DownloadStatus::Completed).await?;
        
        // Remove from active downloads
        {
            let mut active = self.active_downloads.write().await;
            active.remove(&download_id);
        }

        // Emit completion event
        self.app_handle.emit("download-complete", &download_id).unwrap();

        Ok(())
    }

    pub async fn pause_download(&self, id: &str) -> Result<(), String> {
        let active = self.active_downloads.read().await;
        if let Some(active_download) = active.get(id) {
            active_download.cancel_token.cancel();
            self.update_download_status(id, DownloadStatus::Paused).await?;
        }
        Ok(())
    }

    pub async fn resume_download(&self, id: &str) -> Result<(), String> {
        // Get download from database
        let download = get_download(&self.db_pool, id).await
            .map_err(|e| format!("Failed to get download: {}", e))?
            .ok_or("Download not found")?;

        if download.status != DownloadStatus::Paused.to_string() {
            return Err("Download is not paused".to_string());
        }

        // TODO: Implement resume with range requests
        // For now, restart the download
        self.start_download(
            download.url,
            download.file_name,
            Path::new(&download.file_path).parent().unwrap().to_string_lossy().to_string(),
            download.quality,
            download.format,
        ).await?;

        Ok(())
    }

    pub async fn cancel_download(&self, id: &str) -> Result<(), String> {
        let active = self.active_downloads.read().await;
        if let Some(active_download) = active.get(id) {
            active_download.cancel_token.cancel();
            self.update_download_status(id, DownloadStatus::Cancelled).await?;
        }
        Ok(())
    }

    pub async fn get_active_downloads(&self) -> Vec<Download> {
        let active = self.active_downloads.read().await;
        active.values().map(|ad| ad.download.clone()).collect()
    }

    async fn update_download_status(&self, id: &str, status: DownloadStatus) -> Result<(), String> {
        let mut download = get_download(&self.db_pool, id).await
            .map_err(|e| format!("Failed to get download: {}", e))?
            .ok_or("Download not found")?;

        download.status = status.to_string();
        download.updated_at = Utc::now().to_rfc3339();
        
        if status == DownloadStatus::Completed {
            download.completed_at = Some(Utc::now().to_rfc3339());
        }

        update_download(&self.db_pool, &download).await
            .map_err(|e| format!("Failed to update download: {}", e))?;

        // Update in-memory state
        let mut active = self.active_downloads.write().await;
        if let Some(active_download) = active.get_mut(id) {
            active_download.download = download;
        }

        Ok(())
    }

    async fn update_download_size(&self, id: &str, total: u64, downloaded: u64) -> Result<(), String> {
        let mut download = get_download(&self.db_pool, id).await
            .map_err(|e| format!("Failed to get download: {}", e))?
            .ok_or("Download not found")?;

        download.file_size = total as i64;
        download.downloaded_size = downloaded as i64;
        download.updated_at = Utc::now().to_rfc3339();

        update_download(&self.db_pool, &download).await
            .map_err(|e| format!("Failed to update download: {}", e))?;

        // Update in-memory state
        let mut active = self.active_downloads.write().await;
        if let Some(active_download) = active.get_mut(id) {
            active_download.download = download;
        }

        Ok(())
    }

    async fn update_download_progress(
        &self,
        id: &str,
        downloaded: u64,
        total: u64,
        speed: f64,
        eta: Option<i64>,
    ) -> Result<(), String> {
        let mut download = get_download(&self.db_pool, id).await
            .map_err(|e| format!("Failed to get download: {}", e))?
            .ok_or("Download not found")?;

        download.downloaded_size = downloaded as i64;
        download.file_size = total as i64;
        download.speed = speed;
        download.eta = eta;
        download.updated_at = Utc::now().to_rfc3339();

        update_download(&self.db_pool, &download).await
            .map_err(|e| format!("Failed to update download: {}", e))?;

        // Update in-memory state
        let mut active = self.active_downloads.write().await;
        if let Some(active_download) = active.get_mut(id) {
            active_download.download = download;
        }

        Ok(())
    }
}

impl Clone for DownloadManager {
    fn clone(&self) -> Self {
        Self {
            active_downloads: self.active_downloads.clone(),
            client: self.client.clone(),
            app_handle: self.app_handle.clone(),
            db_pool: self.db_pool.clone(),
        }
    }
}