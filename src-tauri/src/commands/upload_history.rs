use tauri::{command, State};
use sqlx::SqlitePool;
use crate::database::upload_history::{UploadHistoryDb, UploadRecord, UploadStatus};

#[command]
pub async fn get_upload_history(
    db_pool: State<'_, SqlitePool>,
    limit: Option<i64>
) -> Result<Vec<UploadRecord>, String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    db.get_recent_uploads(limit.unwrap_or(50)).await
}

#[command]
pub async fn get_resumable_uploads(
    db_pool: State<'_, SqlitePool>
) -> Result<Vec<UploadRecord>, String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    db.get_resumable_uploads().await
}

#[command]
pub async fn delete_upload_record(
    db_pool: State<'_, SqlitePool>,
    upload_id: String
) -> Result<(), String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    db.delete_upload(&upload_id).await
}

#[command]
pub async fn retry_upload(
    db_pool: State<'_, SqlitePool>,
    upload_id: String
) -> Result<UploadRecord, String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    
    // Get the upload record
    let upload = db.get_upload(&upload_id).await?
        .ok_or_else(|| "Upload not found".to_string())?;
    
    // Increment retry count
    let new_retry_count = db.increment_retry_count(&upload_id).await?;
    
    // Update status to queued
    db.update_status(&upload_id, UploadStatus::Queued, None).await?;
    
    // Return updated record
    let mut updated_upload = upload;
    updated_upload.status = UploadStatus::Queued;
    updated_upload.retry_count = new_retry_count;
    
    Ok(updated_upload)
}

#[command]
pub async fn pause_upload(
    db_pool: State<'_, SqlitePool>,
    upload_id: String
) -> Result<(), String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    db.update_status(&upload_id, UploadStatus::Paused, None).await
}

#[command]
pub async fn resume_upload(
    db_pool: State<'_, SqlitePool>,
    upload_id: String
) -> Result<UploadRecord, String> {
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    
    // Get the upload record
    let upload = db.get_upload(&upload_id).await?
        .ok_or_else(|| "Upload not found".to_string())?;
    
    // Check if it has resume data
    if upload.upload_url.is_none() {
        return Err("Upload cannot be resumed - no resume data available".to_string());
    }
    
    // Update status to queued
    db.update_status(&upload_id, UploadStatus::Queued, None).await?;
    
    // Return updated record
    let mut updated_upload = upload;
    updated_upload.status = UploadStatus::Queued;
    
    Ok(updated_upload)
}