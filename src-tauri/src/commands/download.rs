use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, State, Manager};
use sqlx::SqlitePool;
use serde_json::json;
use crate::database::downloads::{
    get_all_downloads, get_active_downloads as get_active_downloads_db,
    get_download, delete_download, Download
};
use crate::download::download_manager::DownloadManager;

#[tauri::command]
pub async fn start_download(
    download_manager: State<'_, DownloadManager>,
    url: String,
    file_name: String,
    download_path: String,
    quality: String,
    format: String,
) -> Result<String, String> {
    download_manager.start_download(url, file_name, download_path, quality, format).await
}

#[tauri::command]
pub async fn pause_download(
    download_manager: State<'_, DownloadManager>,
    id: String,
) -> Result<(), String> {
    download_manager.pause_download(&id).await
}

#[tauri::command]
pub async fn resume_download(
    download_manager: State<'_, DownloadManager>,
    id: String,
) -> Result<(), String> {
    download_manager.resume_download(&id).await
}

#[tauri::command]
pub async fn cancel_download_new(
    download_manager: State<'_, DownloadManager>,
    id: String,
) -> Result<(), String> {
    download_manager.cancel_download(&id).await
}

#[tauri::command]
pub async fn get_downloads(
    db_pool: State<'_, SqlitePool>
) -> Result<Vec<Download>, String> {
    get_all_downloads(&db_pool).await
        .map_err(|e| format!("Failed to get downloads: {}", e))
}

#[tauri::command]
pub async fn get_active_downloads(
    db_pool: State<'_, SqlitePool>
) -> Result<Vec<Download>, String> {
    get_active_downloads_db(&db_pool).await
        .map_err(|e| format!("Failed to get active downloads: {}", e))
}

#[tauri::command]
pub async fn get_download_by_id(
    db_pool: State<'_, SqlitePool>,
    id: String,
) -> Result<Option<Download>, String> {
    get_download(&db_pool, &id).await
        .map_err(|e| format!("Failed to get download: {}", e))
}

#[tauri::command]
pub async fn delete_download_record(
    db_pool: State<'_, SqlitePool>,
    id: String,
) -> Result<(), String> {
    delete_download(&db_pool, &id).await
        .map_err(|e| format!("Failed to delete download: {}", e))
}

#[tauri::command]
pub async fn get_system_info(app_handle: AppHandle) -> Result<serde_json::Value, String> {
    use sysinfo::{System, SystemExt, DiskExt, CpuExt};
    
    let mut system = System::new_all();
    system.refresh_all();
    
    // Get download path
    let download_path = app_handle
        .path()
        .app_data_dir()
        .unwrap_or_else(|_| std::path::PathBuf::from("./downloads"));
    
    // Find the disk that contains the download path
    let disk = system.disks().iter()
        .find(|disk| download_path.starts_with(disk.mount_point()))
        .or_else(|| system.disks().first());
    
    let (available_space, total_space) = if let Some(disk) = disk {
        (disk.available_space(), disk.total_space())
    } else {
        (0, 0)
    };
    
    Ok(json!({
        "availableSpace": available_space,
        "totalSpace": total_space,
        "cpuUsage": system.global_cpu_info().cpu_usage(),
        "memoryUsage": system.used_memory(),
        "totalMemory": system.total_memory(),
    }))
}

#[tauri::command]
pub async fn get_download_path(app_handle: AppHandle) -> Result<String, String> {
    let download_path = app_handle
        .path()
        .app_data_dir()
        .unwrap_or_else(|_| std::path::PathBuf::from("./downloads"))
        .join("downloads");
    
    // Ensure the directory exists
    std::fs::create_dir_all(&download_path)
        .map_err(|e| format!("Failed to create download directory: {}", e))?;
    
    Ok(download_path.to_string_lossy().to_string())
}