use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tauri::{State, Manager};
use tokio::fs;
use anyhow::Result;

#[derive(Debug, Serialize, Deserialize)]
pub struct GeneratedMedia {
    pub filename: String,
    pub path: String,
    pub size: u64,
}

#[tauri::command]
pub async fn save_generated_image(
    window: tauri::Window,
    image_data: Vec<u8>,
    filename: String,
) -> Result<String, String> {
    // Get app data directory
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Create generated media directory
    let media_dir = app_data_dir.join("generated_media");
    fs::create_dir_all(&media_dir)
        .await
        .map_err(|e| format!("Failed to create media directory: {}", e))?;
    
    // Save the file
    let file_path = media_dir.join(&filename);
    fs::write(&file_path, image_data)
        .await
        .map_err(|e| format!("Failed to save image: {}", e))?;
    
    Ok(file_path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn save_generated_video(
    window: tauri::Window,
    video_data: Vec<u8>,
    filename: String,
) -> Result<String, String> {
    // Get app data directory
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Create generated media directory
    let media_dir = app_data_dir.join("generated_media").join("videos");
    fs::create_dir_all(&media_dir)
        .await
        .map_err(|e| format!("Failed to create video directory: {}", e))?;
    
    // Save the file
    let file_path = media_dir.join(&filename);
    fs::write(&file_path, video_data)
        .await
        .map_err(|e| format!("Failed to save video: {}", e))?;
    
    Ok(file_path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn list_generated_media(
    window: tauri::Window,
    media_type: Option<String>,
) -> Result<Vec<GeneratedMedia>, String> {
    // Get app data directory
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let media_dir = app_data_dir.join("generated_media");
    
    // Read directory based on media type
    let target_dir = match media_type.as_deref() {
        Some("video") => media_dir.join("videos"),
        Some("image") => media_dir.clone(),
        _ => media_dir.clone(),
    };
    
    if !target_dir.exists() {
        return Ok(vec![]);
    }
    
    let mut media_files = Vec::new();
    
    let mut entries = fs::read_dir(&target_dir)
        .await
        .map_err(|e| format!("Failed to read directory: {}", e))?;
    
    while let Some(entry) = entries.next_entry().await
        .map_err(|e| format!("Failed to read entry: {}", e))? {
        
        let path = entry.path();
        if path.is_file() {
            let metadata = entry.metadata()
                .await
                .map_err(|e| format!("Failed to read metadata: {}", e))?;
            
            media_files.push(GeneratedMedia {
                filename: path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string(),
                path: path.to_string_lossy().to_string(),
                size: metadata.len(),
            });
        }
    }
    
    // Sort by creation time (newest first)
    media_files.reverse();
    
    Ok(media_files)
}

#[tauri::command]
pub async fn delete_generated_media(
    file_path: String,
) -> Result<(), String> {
    let path = PathBuf::from(&file_path);
    
    // Security check - ensure path is within generated_media directory
    if !file_path.contains("generated_media") {
        return Err("Invalid file path".to_string());
    }
    
    fs::remove_file(&path)
        .await
        .map_err(|e| format!("Failed to delete file: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn get_generation_stats(
    window: tauri::Window,
) -> Result<serde_json::Value, String> {
    use serde_json::json;
    
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let media_dir = app_data_dir.join("generated_media");
    
    let mut total_images = 0;
    let mut total_videos = 0;
    let mut total_size = 0u64;
    
    // Count images
    if let Ok(mut entries) = fs::read_dir(&media_dir).await {
        while let Ok(Some(entry)) = entries.next_entry().await {
            if entry.path().is_file() {
                if let Ok(metadata) = entry.metadata().await {
                    total_images += 1;
                    total_size += metadata.len();
                }
            }
        }
    }
    
    // Count videos
    let video_dir = media_dir.join("videos");
    if let Ok(mut entries) = fs::read_dir(&video_dir).await {
        while let Ok(Some(entry)) = entries.next_entry().await {
            if entry.path().is_file() {
                if let Ok(metadata) = entry.metadata().await {
                    total_videos += 1;
                    total_size += metadata.len();
                }
            }
        }
    }
    
    Ok(json!({
        "totalImages": total_images,
        "totalVideos": total_videos,
        "totalSize": total_size,
        "totalSizeFormatted": format_bytes(total_size),
    }))
}

fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.2} {}", size, UNITS[unit_index])
}