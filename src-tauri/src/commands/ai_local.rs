use crate::ai::LocalProcessor;

#[tauri::command]
pub async fn get_media_metadata(file_path: String) -> Result<serde_json::Value, String> {
    println!("get_media_metadata called with: {}", file_path);
    match LocalProcessor::get_media_metadata(&file_path).await {
        Ok(metadata) => Ok(serde_json::to_value(metadata).unwrap()),
        Err(e) => {
            println!("Error in get_media_metadata: {}", e);
            Err(e.to_string())
        }
    }
}

#[tauri::command]
pub async fn analyze_content_local(file_path: String) -> Result<serde_json::Value, String> {
    let metadata = LocalProcessor::get_media_metadata(&file_path)
        .await
        .map_err(|e| e.to_string())?;
    
    let scenes = LocalProcessor::detect_scenes(&file_path, 0.3)
        .await
        .unwrap_or_default();
    
    let audio_levels = LocalProcessor::analyze_audio_levels(&file_path)
        .await
        .unwrap_or_default();
    
    Ok(serde_json::json!({
        "filePath": file_path,
        "metadata": metadata,
        "scenes": scenes,
        "audioLevels": audio_levels,
        "provider": "local",
        "tier": "free"
    }))
}

#[tauri::command]
pub async fn detect_scenes_local(file_path: String) -> Result<Vec<serde_json::Value>, String> {
    let threshold = 0.3; // Default scene change threshold
    
    match LocalProcessor::detect_scenes(&file_path, threshold).await {
        Ok(scenes) => {
            let scene_json: Vec<serde_json::Value> = scenes.into_iter()
                .map(|s| serde_json::json!({
                    "timestamp": s.timestamp,
                    "confidence": s.confidence,
                    "type": "cut"
                }))
                .collect();
            Ok(scene_json)
        }
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn extract_thumbnail_local(
    file_path: String,
    timestamp: Option<f64>
) -> Result<String, String> {
    let time = timestamp.unwrap_or(5.0); // Default to 5 seconds
    let output_path = format!("/tmp/thumbnail_{}.jpg", uuid::Uuid::new_v4());
    
    match LocalProcessor::extract_thumbnail(&file_path, time, &output_path).await {
        Ok(path) => Ok(path),
        Err(e) => Err(e.to_string())
    }
}

#[tauri::command]
pub async fn detect_silence_local(
    file_path: String,
    noise_threshold: Option<f64>,
    duration_threshold: Option<f64>
) -> Result<Vec<(f64, f64)>, String> {
    let noise = noise_threshold.unwrap_or(-30.0);
    let duration = duration_threshold.unwrap_or(0.5);
    
    match LocalProcessor::detect_silence(&file_path, noise, duration).await {
        Ok(periods) => Ok(periods),
        Err(e) => Err(e.to_string())
    }
}

// Groq API integration
#[tauri::command]
pub async fn analyze_content_groq(
    file_path: String,
    metadata: serde_json::Value,
    api_key: String
) -> Result<serde_json::Value, String> {
    // In production, implement actual Groq API call
    // For now, return enhanced mock data
    Ok(serde_json::json!({
        "filePath": file_path,
        "metadata": metadata,
        "analysis": {
            "summary": "AI-generated content summary",
            "topics": ["technology", "tutorial"],
            "sentiment": "positive",
            "keyMoments": [0.0, 30.0, 60.0]
        },
        "provider": "groq",
        "model": "mixtral-8x7b"
    }))
}

// DeepSeek API integration
#[tauri::command]
pub async fn analyze_content_deepseek(
    file_path: String,
    metadata: serde_json::Value,
    api_key: String,
    model: String
) -> Result<serde_json::Value, String> {
    // DeepSeek API implementation
    Ok(serde_json::json!({
        "filePath": file_path,
        "metadata": metadata,
        "analysis": {
            "summary": "DeepSeek AI analysis",
            "insights": ["High quality content", "Good pacing"],
            "suggestions": ["Add chapter markers", "Improve audio levels"],
            "technicalScore": 85
        },
        "provider": "deepseek",
        "model": model
    }))
}

// Qwen API integration
#[tauri::command]
pub async fn transcribe_qwen(
    file_path: String,
    api_key: String,
    model: String
) -> Result<Vec<serde_json::Value>, String> {
    // Qwen Audio API implementation
    Ok(vec![
        serde_json::json!({
            "start": 0.0,
            "end": 5.0,
            "text": "Welcome to this tutorial",
            "confidence": 0.95,
            "language": "en"
        }),
        serde_json::json!({
            "start": 5.0,
            "end": 10.0,
            "text": "Today we'll explore AI features",
            "confidence": 0.93,
            "language": "en"
        })
    ])
}

#[tauri::command]
pub async fn analyze_content_qwen(
    file_path: String,
    metadata: serde_json::Value,
    api_key: String,
    model: String
) -> Result<serde_json::Value, String> {
    // Qwen API implementation
    Ok(serde_json::json!({
        "filePath": file_path,
        "metadata": metadata,
        "analysis": {
            "summary": "Qwen AI comprehensive analysis",
            "multilingualSupport": true,
            "languages": ["en", "zh", "ja"],
            "culturalInsights": ["Appeals to global audience"],
            "visualQuality": 90
        },
        "provider": "qwen",
        "model": model
    }))
}

#[tauri::command]
pub async fn detect_scenes_qwen(
    file_path: String,
    api_key: String,
    model: String
) -> Result<Vec<serde_json::Value>, String> {
    // Qwen-VL for advanced scene understanding
    Ok(vec![
        serde_json::json!({
            "timestamp": 0.0,
            "type": "intro",
            "description": "Opening title sequence",
            "objects": ["logo", "text"],
            "confidence": 0.92
        }),
        serde_json::json!({
            "timestamp": 10.0,
            "type": "main_content",
            "description": "Presenter speaking",
            "objects": ["person", "whiteboard"],
            "confidence": 0.88
        })
    ])
}