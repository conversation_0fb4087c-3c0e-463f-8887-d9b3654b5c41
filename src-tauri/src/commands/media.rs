use std::path::Path;
use std::process::Command;
use tauri::command;
use serde::{Serialize, Deserialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct MediaInfo {
    pub file_path: String,
    pub file_name: String,
    pub file_size: u64,
    pub duration: Option<f64>,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub codec: Option<String>,
    pub bitrate: Option<u32>,
    pub fps: Option<f32>,
    pub media_type: String,
    pub thumbnail_path: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AudioInfo {
    pub sample_rate: Option<u32>,
    pub channels: Option<u32>,
    pub audio_codec: Option<String>,
}

#[command]
pub async fn get_media_info(file_path: String) -> Result<MediaInfo, String> {
    let path = Path::new(&file_path);
    
    if !path.exists() {
        return Err("File does not exist".to_string());
    }
    
    // Get file metadata
    let metadata = std::fs::metadata(&path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?;
    
    let file_name = path.file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("Unknown")
        .to_string();
    
    let file_size = metadata.len();
    
    // Determine media type from extension
    let extension = path.extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();
    
    let media_type = match extension.as_str() {
        "mp4" | "mkv" | "avi" | "mov" | "webm" | "flv" => "video",
        "jpg" | "jpeg" | "png" | "gif" | "webp" | "bmp" => "image",
        "mp3" | "wav" | "flac" | "m4a" | "aac" | "ogg" => "audio",
        _ => "unknown"
    }.to_string();
    
    // Use ffprobe to get detailed media info if available
    let ffprobe_info = get_ffprobe_info(&file_path);
    
    Ok(MediaInfo {
        file_path: file_path.clone(),
        file_name,
        file_size,
        duration: ffprobe_info.as_ref().and_then(|i| i.duration),
        width: ffprobe_info.as_ref().and_then(|i| i.width),
        height: ffprobe_info.as_ref().and_then(|i| i.height),
        codec: ffprobe_info.as_ref().and_then(|i| i.codec.clone()),
        bitrate: ffprobe_info.as_ref().and_then(|i| i.bitrate),
        fps: ffprobe_info.as_ref().and_then(|i| i.fps),
        media_type,
        thumbnail_path: None,
    })
}

#[command]
pub async fn generate_thumbnail(file_path: String, output_path: String) -> Result<String, String> {
    let input_path = Path::new(&file_path);
    let output = Path::new(&output_path);
    
    if !input_path.exists() {
        return Err("Input file does not exist".to_string());
    }
    
    // Create output directory if it doesn't exist
    if let Some(parent) = output.parent() {
        std::fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create output directory: {}", e))?;
    }
    
    // Determine media type
    let extension = input_path.extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();
    
    match extension.as_str() {
        "mp4" | "mkv" | "avi" | "mov" | "webm" | "flv" => {
            // Generate video thumbnail using ffmpeg
            generate_video_thumbnail(&file_path, &output_path)?;
        }
        "jpg" | "jpeg" | "png" | "gif" | "webp" | "bmp" => {
            // For images, just copy or resize
            generate_image_thumbnail(&file_path, &output_path)?;
        }
        "mp3" | "wav" | "flac" | "m4a" | "aac" | "ogg" => {
            // Generate audio waveform
            generate_audio_waveform(&file_path, &output_path)?;
        }
        _ => return Err("Unsupported file type".to_string()),
    }
    
    Ok(output_path)
}

#[command]
pub async fn list_downloaded_files(directory: String) -> Result<Vec<MediaInfo>, String> {
    let dir_path = Path::new(&directory);
    
    if !dir_path.exists() || !dir_path.is_dir() {
        return Err("Directory does not exist".to_string());
    }
    
    let mut files = Vec::new();
    
    // Read directory entries
    let entries = std::fs::read_dir(dir_path)
        .map_err(|e| format!("Failed to read directory: {}", e))?;
    
    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            
            // Skip directories
            if path.is_dir() {
                continue;
            }
            
            // Check if it's a media file
            if let Some(extension) = path.extension() {
                let ext = extension.to_str().unwrap_or("").to_lowercase();
                let is_media = matches!(ext.as_str(),
                    "mp4" | "mkv" | "avi" | "mov" | "webm" | "flv" |
                    "jpg" | "jpeg" | "png" | "gif" | "webp" | "bmp" |
                    "mp3" | "wav" | "flac" | "m4a" | "aac" | "ogg"
                );
                
                if is_media {
                    if let Ok(info) = get_media_info(path.to_string_lossy().to_string()).await {
                        files.push(info);
                    }
                }
            }
        }
    }
    
    // Sort by modification time (newest first)
    files.sort_by(|a, b| b.file_path.cmp(&a.file_path));
    
    Ok(files)
}

// Helper structures and functions
#[derive(Debug)]
struct FFProbeInfo {
    duration: Option<f64>,
    width: Option<u32>,
    height: Option<u32>,
    codec: Option<String>,
    bitrate: Option<u32>,
    fps: Option<f32>,
}

fn get_ffprobe_info(file_path: &str) -> Option<FFProbeInfo> {
    // Try to use ffprobe to get media information
    let output = Command::new("ffprobe")
        .args(&[
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            file_path
        ])
        .output()
        .ok()?;
    
    if !output.status.success() {
        return None;
    }
    
    // Parse JSON output (simplified for example)
    // In production, you'd use serde_json to properly parse this
    let output_str = String::from_utf8_lossy(&output.stdout);
    
    // Extract basic info from the output (simplified parsing)
    let info = FFProbeInfo {
        duration: extract_duration(&output_str),
        width: extract_dimension(&output_str, "width"),
        height: extract_dimension(&output_str, "height"),
        codec: extract_codec(&output_str),
        bitrate: extract_bitrate(&output_str),
        fps: extract_fps(&output_str),
    };
    
    Some(info)
}

fn extract_duration(json_str: &str) -> Option<f64> {
    // Simplified extraction - in production use proper JSON parsing
    if let Some(start) = json_str.find("\"duration\": \"") {
        let start = start + 13;
        if let Some(end) = json_str[start..].find("\"") {
            return json_str[start..start + end].parse().ok();
        }
    }
    None
}

fn extract_dimension(json_str: &str, dim: &str) -> Option<u32> {
    let search = format!("\"{}\": ", dim);
    if let Some(start) = json_str.find(&search) {
        let start = start + search.len();
        if let Some(end) = json_str[start..].find(",") {
            return json_str[start..start + end].parse().ok();
        }
    }
    None
}

fn extract_codec(json_str: &str) -> Option<String> {
    if let Some(start) = json_str.find("\"codec_name\": \"") {
        let start = start + 15;
        if let Some(end) = json_str[start..].find("\"") {
            return Some(json_str[start..start + end].to_string());
        }
    }
    None
}

fn extract_bitrate(json_str: &str) -> Option<u32> {
    if let Some(start) = json_str.find("\"bit_rate\": \"") {
        let start = start + 13;
        if let Some(end) = json_str[start..].find("\"") {
            return json_str[start..start + end].parse().ok();
        }
    }
    None
}

fn extract_fps(json_str: &str) -> Option<f32> {
    if let Some(start) = json_str.find("\"r_frame_rate\": \"") {
        let start = start + 17;
        if let Some(end) = json_str[start..].find("\"") {
            let fps_str = &json_str[start..start + end];
            // Handle fraction format like "30/1"
            if let Some(slash) = fps_str.find("/") {
                let num: f32 = fps_str[..slash].parse().ok()?;
                let den: f32 = fps_str[slash + 1..].parse().ok()?;
                return Some(num / den);
            }
        }
    }
    None
}

fn generate_video_thumbnail(input: &str, output: &str) -> Result<(), String> {
    // Use ffmpeg to extract a frame from the video
    let status = Command::new("ffmpeg")
        .args(&[
            "-i", input,
            "-ss", "00:00:01", // Seek to 1 second
            "-vframes", "1",    // Extract 1 frame
            "-vf", "scale=320:-1", // Scale to 320px width
            "-y",               // Overwrite output
            output
        ])
        .status()
        .map_err(|e| format!("Failed to run ffmpeg: {}", e))?;
    
    if status.success() {
        Ok(())
    } else {
        Err("Failed to generate video thumbnail".to_string())
    }
}

fn generate_image_thumbnail(input: &str, output: &str) -> Result<(), String> {
    // For now, just copy the image (in production, you'd resize it)
    std::fs::copy(input, output)
        .map_err(|e| format!("Failed to copy image: {}", e))?;
    Ok(())
}

fn generate_audio_waveform(input: &str, output: &str) -> Result<(), String> {
    // Use ffmpeg to generate a waveform image
    let status = Command::new("ffmpeg")
        .args(&[
            "-i", input,
            "-filter_complex", "showwavespic=s=320x120:colors=blue",
            "-frames:v", "1",
            "-y",
            output
        ])
        .status()
        .map_err(|e| format!("Failed to run ffmpeg: {}", e))?;
    
    if status.success() {
        Ok(())
    } else {
        Err("Failed to generate audio waveform".to_string())
    }
}