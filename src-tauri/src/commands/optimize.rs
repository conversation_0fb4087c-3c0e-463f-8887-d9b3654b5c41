use serde::{Deserialize, Serialize};
use tauri::command;
use std::process::Command;

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct OptimizationPreset {
    pub platform: String,
    pub name: String,
    pub description: String,
    pub video: VideoSettings,
    pub audio: AudioSettings,
    pub output: OutputSettings,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct VideoSettings {
    pub width: u32,
    pub height: u32,
    pub fps: f32,
    pub bitrate: String,
    pub codec: String,
    pub aspect_ratio: String,
    pub max_duration: Option<u32>, // in seconds
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AudioSettings {
    pub bitrate: String,
    pub sample_rate: u32,
    pub channels: u32,
    pub codec: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OutputSettings {
    pub format: String,
    pub max_file_size: Option<u64>, // in bytes
    pub naming_pattern: String,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
pub struct OptimizationResult {
    pub input_path: String,
    pub output_path: String,
    pub platform: String,
    pub duration: f64,
    pub file_size: u64,
    pub optimization_time: f64,
}

// Platform-specific presets
pub fn get_platform_presets() -> Vec<OptimizationPreset> {
    vec![
        // YouTube Presets
        OptimizationPreset {
            platform: "youtube".to_string(),
            name: "YouTube 4K".to_string(),
            description: "Ultra HD quality for YouTube".to_string(),
            video: VideoSettings {
                width: 3840,
                height: 2160,
                fps: 60.0,
                bitrate: "35M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "16:9".to_string(),
                max_duration: None,
            },
            audio: AudioSettings {
                bitrate: "384k".to_string(),
                sample_rate: 48000,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(128 * 1024 * 1024 * 1024), // 128GB
                naming_pattern: "{name}_youtube_4k".to_string(),
            },
        },
        OptimizationPreset {
            platform: "youtube".to_string(),
            name: "YouTube 1080p".to_string(),
            description: "Full HD quality for YouTube".to_string(),
            video: VideoSettings {
                width: 1920,
                height: 1080,
                fps: 30.0,
                bitrate: "8M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "16:9".to_string(),
                max_duration: None,
            },
            audio: AudioSettings {
                bitrate: "192k".to_string(),
                sample_rate: 48000,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(128 * 1024 * 1024 * 1024),
                naming_pattern: "{name}_youtube_1080p".to_string(),
            },
        },
        
        // TikTok Presets
        OptimizationPreset {
            platform: "tiktok".to_string(),
            name: "TikTok Feed".to_string(),
            description: "Optimized for TikTok feed (9:16)".to_string(),
            video: VideoSettings {
                width: 1080,
                height: 1920,
                fps: 30.0,
                bitrate: "6M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "9:16".to_string(),
                max_duration: Some(60), // 60 seconds max
            },
            audio: AudioSettings {
                bitrate: "128k".to_string(),
                sample_rate: 44100,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(287 * 1024 * 1024), // 287MB limit
                naming_pattern: "{name}_tiktok".to_string(),
            },
        },
        
        // Instagram Presets
        OptimizationPreset {
            platform: "instagram".to_string(),
            name: "Instagram Reels".to_string(),
            description: "Vertical video for Instagram Reels".to_string(),
            video: VideoSettings {
                width: 1080,
                height: 1920,
                fps: 30.0,
                bitrate: "5M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "9:16".to_string(),
                max_duration: Some(90), // 90 seconds max
            },
            audio: AudioSettings {
                bitrate: "128k".to_string(),
                sample_rate: 44100,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(1024 * 1024 * 1024), // 1GB
                naming_pattern: "{name}_instagram_reels".to_string(),
            },
        },
        OptimizationPreset {
            platform: "instagram".to_string(),
            name: "Instagram Post".to_string(),
            description: "Square video for Instagram feed".to_string(),
            video: VideoSettings {
                width: 1080,
                height: 1080,
                fps: 30.0,
                bitrate: "3.5M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "1:1".to_string(),
                max_duration: Some(60),
            },
            audio: AudioSettings {
                bitrate: "128k".to_string(),
                sample_rate: 44100,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(100 * 1024 * 1024), // 100MB
                naming_pattern: "{name}_instagram_post".to_string(),
            },
        },
        
        // Twitter/X Presets
        OptimizationPreset {
            platform: "twitter".to_string(),
            name: "Twitter Video".to_string(),
            description: "Optimized for Twitter/X timeline".to_string(),
            video: VideoSettings {
                width: 1280,
                height: 720,
                fps: 30.0,
                bitrate: "2M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "16:9".to_string(),
                max_duration: Some(140), // 2:20 max
            },
            audio: AudioSettings {
                bitrate: "128k".to_string(),
                sample_rate: 44100,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(512 * 1024 * 1024), // 512MB
                naming_pattern: "{name}_twitter".to_string(),
            },
        },
        
        // LinkedIn Presets
        OptimizationPreset {
            platform: "linkedin".to_string(),
            name: "LinkedIn Video".to_string(),
            description: "Professional video for LinkedIn".to_string(),
            video: VideoSettings {
                width: 1920,
                height: 1080,
                fps: 30.0,
                bitrate: "5M".to_string(),
                codec: "h264".to_string(),
                aspect_ratio: "16:9".to_string(),
                max_duration: Some(600), // 10 minutes max
            },
            audio: AudioSettings {
                bitrate: "192k".to_string(),
                sample_rate: 48000,
                channels: 2,
                codec: "aac".to_string(),
            },
            output: OutputSettings {
                format: "mp4".to_string(),
                max_file_size: Some(5 * 1024 * 1024 * 1024), // 5GB
                naming_pattern: "{name}_linkedin".to_string(),
            },
        },
    ]
}

#[command]
pub async fn get_optimization_presets() -> Result<Vec<OptimizationPreset>, String> {
    Ok(get_platform_presets())
}

#[command]
pub async fn optimize_for_platform(
    input_path: String,
    platform: String,
    preset_name: String,
    output_dir: String,
) -> Result<OptimizationResult, String> {
    // Validate and canonicalize input path
    let input_path_obj = std::path::Path::new(&input_path);
    let canonical_input = input_path_obj.canonicalize()
        .map_err(|_| "Invalid input file path".to_string())?;
    
    // Validate file exists and is a file
    let input_metadata = std::fs::metadata(&canonical_input)
        .map_err(|_| "Input file does not exist".to_string())?;
    
    if !input_metadata.is_file() {
        return Err("Input path is not a file".to_string());
    }
    
    // Validate and canonicalize output directory
    let output_dir_path = std::path::Path::new(&output_dir);
    let canonical_output_dir = output_dir_path.canonicalize()
        .map_err(|_| "Invalid output directory path".to_string())?;
    
    // Ensure output directory exists and is a directory
    if !canonical_output_dir.is_dir() {
        return Err("Output path is not a directory".to_string());
    }
    
    // Find the preset
    let presets = get_platform_presets();
    let preset = presets.iter()
        .find(|p| p.platform == platform && p.name == preset_name)
        .ok_or("Preset not found")?;

    // Generate safe output filename
    let stem = canonical_input.file_stem()
        .and_then(|s| s.to_str())
        .unwrap_or("output");
    
    // Sanitize filename - remove any path separators or dangerous characters
    let safe_stem = stem
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_' || *c == '.')
        .collect::<String>();
    
    let output_filename = preset.output.naming_pattern
        .replace("{name}", &safe_stem);
    
    // Ensure output filename is safe
    let safe_output_filename = output_filename
        .chars()
        .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_')
        .collect::<String>();
    
    let output_path = canonical_output_dir
        .join(format!("{}.{}", safe_output_filename, preset.output.format));

    println!("🎬 Optimizing {} for {}", input_path, platform);
    
    let start_time = std::time::Instant::now();

    // Build FFmpeg command with safe paths
    let mut ffmpeg_cmd = Command::new("ffmpeg");
    ffmpeg_cmd
        .arg("-i").arg(canonical_input.to_str().ok_or("Invalid input path encoding")?)
        .arg("-y"); // Overwrite output

    // Video settings
    ffmpeg_cmd
        .arg("-vf").arg(format!("scale={}:{}", preset.video.width, preset.video.height))
        .arg("-r").arg(preset.video.fps.to_string())
        .arg("-c:v").arg(&preset.video.codec)
        .arg("-b:v").arg(&preset.video.bitrate);

    // Audio settings
    ffmpeg_cmd
        .arg("-c:a").arg(&preset.audio.codec)
        .arg("-b:a").arg(&preset.audio.bitrate)
        .arg("-ar").arg(preset.audio.sample_rate.to_string())
        .arg("-ac").arg(preset.audio.channels.to_string());

    // Duration limit if specified
    if let Some(max_duration) = preset.video.max_duration {
        ffmpeg_cmd.arg("-t").arg(max_duration.to_string());
    }

    // Output file
    ffmpeg_cmd.arg(output_path.to_str().unwrap());

    // Execute FFmpeg
    let output = ffmpeg_cmd.output()
        .map_err(|e| format!("Failed to run FFmpeg: {}", e))?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("FFmpeg failed: {}", stderr));
    }

    let optimization_time = start_time.elapsed().as_secs_f64();

    // Get output file info
    let metadata = std::fs::metadata(&output_path)
        .map_err(|e| format!("Failed to get output file metadata: {}", e))?;

    Ok(OptimizationResult {
        input_path,
        output_path: output_path.to_string_lossy().to_string(),
        platform,
        duration: 0.0, // TODO: Extract from FFmpeg output
        file_size: metadata.len(),
        optimization_time,
    })
}

#[command]
pub async fn batch_optimize(
    input_paths: Vec<String>,
    platforms: Vec<String>,
    output_dir: String,
) -> Result<Vec<OptimizationResult>, String> {
    let mut results = Vec::new();

    for input_path in input_paths {
        for platform in &platforms {
            // Get default preset for platform
            let presets = get_platform_presets();
            let preset = presets.iter()
                .find(|p| p.platform == *platform)
                .ok_or(format!("No preset found for platform: {}", platform))?;

            match optimize_for_platform(
                input_path.clone(),
                platform.clone(),
                preset.name.clone(),
                output_dir.clone(),
            ).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    println!("❌ Failed to optimize {} for {}: {}", input_path, platform, e);
                    // Continue with other optimizations
                }
            }
        }
    }

    Ok(results)
}

#[command]
pub async fn estimate_optimization_time(
    file_path: String,
    platforms: Vec<String>,
) -> Result<f64, String> {
    // Get file size
    let metadata = std::fs::metadata(&file_path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?;
    
    let file_size_mb = metadata.len() as f64 / (1024.0 * 1024.0);
    
    // Rough estimation: 1 second per 10MB per platform
    let estimated_seconds = (file_size_mb / 10.0) * platforms.len() as f64;
    
    Ok(estimated_seconds)
}