use serde::{Deserialize, Serialize};
use tauri::{command, Emitter, State, Manager};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use sqlx::SqlitePool;
use crate::database::upload_history::{UploadHistoryDb, UploadRecord, UploadStatus as DbUploadStatus};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadConfig {
    pub platform: Platform,
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub privacy: PrivacyLevel,
    pub scheduled_time: Option<String>,
    pub thumbnail_path: Option<String>,
    pub category: Option<String>,
    pub playlist_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Platform {
    YouTube,
    TikTok,
    Instagram,
    Twitter,
    LinkedIn,
    Facebook,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PrivacyLevel {
    Public,
    Private,
    Unlisted,
    FriendsOnly,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UploadProgress {
    pub upload_id: String,
    pub platform: Platform,
    pub progress: f32,
    pub status: UploadStatus,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum UploadStatus {
    Queued,
    Preparing,
    Uploading,
    Processing,
    Published,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UploadResult {
    pub upload_id: String,
    pub platform: Platform,
    pub url: String,
    pub platform_id: String,
    pub published_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformAuth {
    pub platform: Platform,
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: Option<i64>,
    pub user_info: HashMap<String, String>,
}

// Thread-safe upload queue
pub type UploadQueue = Arc<Mutex<HashMap<String, (String, UploadConfig)>>>;

#[command]
pub async fn queue_upload(
    upload_queue: State<'_, UploadQueue>,
    db_pool: State<'_, SqlitePool>,
    file_path: String,
    config: UploadConfig,
) -> Result<String, String> {
    // Validate file path - prevent path traversal
    let path = std::path::Path::new(&file_path);
    let canonical_path = path.canonicalize()
        .map_err(|_| "Invalid file path".to_string())?;
    
    // Validate file exists and get metadata
    let metadata = std::fs::metadata(&canonical_path)
        .map_err(|_| "File does not exist".to_string())?;
    
    // Check file size limit (1GB max)
    const MAX_FILE_SIZE: u64 = 1024 * 1024 * 1024; // 1GB
    if metadata.len() > MAX_FILE_SIZE {
        return Err(format!("File too large. Maximum size is 1GB"));
    }
    
    // Validate it's a file, not a directory
    if !metadata.is_file() {
        return Err("Path is not a file".to_string());
    }

    // Generate unique upload ID
    let upload_id = format!("upload_{}_{}", 
        chrono::Utc::now().timestamp_millis(),
        uuid::Uuid::new_v4().to_string()
    );

    // Add to thread-safe queue
    let mut queue = upload_queue.lock().await;
    
    // Limit queue size to prevent memory exhaustion
    const MAX_QUEUE_SIZE: usize = 100;
    if queue.len() >= MAX_QUEUE_SIZE {
        return Err("Upload queue is full. Please wait for some uploads to complete.".to_string());
    }
    
    queue.insert(upload_id.clone(), (canonical_path.to_string_lossy().to_string(), config.clone()));

    // Save to database
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    let record = UploadRecord {
        id: upload_id.clone(),
        file_path: canonical_path.to_string_lossy().to_string(),
        file_name: path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown")
            .to_string(),
        file_size: metadata.len() as i64,
        platform: format!("{:?}", config.platform),
        status: DbUploadStatus::Queued,
        progress: 0.0,
        title: config.title.clone(),
        description: Some(config.description.clone()),
        tags: config.tags.clone(),
        privacy: format!("{:?}", config.privacy),
        category: config.category.clone(),
        playlist_id: config.playlist_id.clone(),
        thumbnail_path: config.thumbnail_path.clone(),
        platform_id: None,
        platform_url: None,
        created_at: chrono::Utc::now(),
        started_at: None,
        completed_at: None,
        error_message: None,
        retry_count: 0,
        upload_url: None,
        upload_token: None,
        bytes_uploaded: 0,
    };
    
    db.create_upload(&record).await?;

    println!("📤 Queued upload: {} for {:?}", upload_id, config.platform);
    
    Ok(upload_id)
}

#[command]
pub async fn start_upload(
    window: tauri::Window,
    upload_queue: State<'_, UploadQueue>,
    db_pool: State<'_, SqlitePool>,
    upload_id: String,
) -> Result<UploadResult, String> {
    // Get config from queue
    let queue = upload_queue.lock().await;
    let (file_path, config) = queue.get(&upload_id)
        .ok_or("Upload not found in queue")?
        .clone();
    drop(queue); // Release lock early

    // Emit progress update
    let _ = window.emit("upload-progress", UploadProgress {
        upload_id: upload_id.clone(),
        platform: config.platform.clone(),
        progress: 0.0,
        status: UploadStatus::Preparing,
        message: "Preparing upload...".to_string(),
    });

    // Update database status
    let db = UploadHistoryDb::new(db_pool.inner().clone());
    db.update_status(&upload_id, DbUploadStatus::Preparing, None).await?;
    
    // Route to appropriate platform handler
    match config.platform {
        Platform::YouTube => upload_to_youtube(window, upload_id, file_path, config, db_pool.inner().clone()).await,
        Platform::TikTok => upload_to_tiktok(window, upload_id, file_path, config, db_pool.inner().clone()).await,
        Platform::Instagram => upload_to_instagram(window, upload_id, file_path, config, db_pool.inner().clone()).await,
        _ => Err("Platform not yet supported".to_string()),
    }
}

#[command]
pub async fn cancel_upload(
    upload_queue: State<'_, UploadQueue>,
    upload_id: String
) -> Result<(), String> {
    // Remove from queue
    let mut queue = upload_queue.lock().await;
    queue.remove(&upload_id);
    
    // TODO: Cancel ongoing upload if in progress
    
    Ok(())
}

#[command]
pub async fn get_upload_queue(
    upload_queue: State<'_, UploadQueue>
) -> Result<Vec<(String, UploadConfig)>, String> {
    let queue = upload_queue.lock().await;
    Ok(queue.iter()
        .map(|(k, (_, config))| (k.clone(), config.clone()))
        .collect())
}

#[command]
pub async fn authenticate_platform(
    _window: tauri::Window,
    platform: Platform,
) -> Result<String, String> {
    use crate::auth::{YouTubeAuth, YouTubeConfig};
    
    // Generate OAuth URL for platform
    match platform {
        Platform::YouTube => {
            // Load YouTube configuration
            let config = YouTubeConfig::from_env()
                .map_err(|e| format!("Failed to load YouTube config: {}", e))?;
            
            // Check if properly configured
            if config.client_id == "YOUR_CLIENT_ID" {
                return Err("YouTube authentication not configured. Please set YOUTUBE_CLIENT_ID and YOUTUBE_CLIENT_SECRET environment variables.".to_string());
            }
            
            // Create auth instance
            let auth = YouTubeAuth::new(config);
            
            // Generate auth URL
            let (auth_url, _state) = auth.initiate_auth().await?;
            
            Ok(auth_url)
        },
        Platform::TikTok => {
            use crate::auth::{TikTokAuth, TikTokConfig};
            
            // Load TikTok configuration
            let config = TikTokConfig::from_env()
                .map_err(|e| format!("Failed to load TikTok config: {}", e))?;
            
            // Check if properly configured
            if config.client_key == "YOUR_CLIENT_KEY" {
                return Err("TikTok authentication not configured. Please set TIKTOK_CLIENT_KEY and TIKTOK_CLIENT_SECRET environment variables.".to_string());
            }
            
            // Create auth instance
            let auth = TikTokAuth::new(config);
            
            // Generate auth URL
            let (auth_url, _state) = auth.initiate_auth().await?;
            
            Ok(auth_url)
        },
        Platform::Instagram => {
            use crate::auth::{InstagramAuth, generate_state};
            
            // Get Instagram app credentials from environment
            let client_id = std::env::var("INSTAGRAM_CLIENT_ID")
                .map_err(|_| "INSTAGRAM_CLIENT_ID environment variable not set".to_string())?;
            let client_secret = std::env::var("INSTAGRAM_CLIENT_SECRET")
                .map_err(|_| "INSTAGRAM_CLIENT_SECRET environment variable not set".to_string())?;
            
            // Check if properly configured
            if client_id == "YOUR_CLIENT_ID" {
                return Err("Instagram authentication not configured. Please set INSTAGRAM_CLIENT_ID and INSTAGRAM_CLIENT_SECRET environment variables.".to_string());
            }
            
            // Create auth instance
            let auth = InstagramAuth::new(
                client_id,
                client_secret,
                "http://localhost:3000/auth/instagram/callback".to_string(),
            );
            
            // Generate state for CSRF protection
            let state = generate_state();
            
            // Generate auth URL
            let auth_url = auth.get_auth_url(&state)
                .map_err(|e| e.to_string())?;
            
            Ok(auth_url)
        },
        _ => Err("Platform authentication not yet implemented".to_string()),
    }
}

#[command]
pub async fn handle_auth_callback(
    window: tauri::Window,
    platform: Platform,
    code: String,
    state: String,
) -> Result<PlatformAuth, String> {
    use crate::auth::{YouTubeAuth, YouTubeConfig, TokenStorage};
    
    // Exchange authorization code for access token
    match platform {
        Platform::YouTube => {
            // Load YouTube configuration
            let config = YouTubeConfig::from_env()
                .map_err(|e| format!("Failed to load YouTube config: {}", e))?;
            
            // Create auth instance
            let auth = YouTubeAuth::new(config);
            
            // Exchange code for token
            let token = auth.exchange_code(code, state).await?;
            
            // Get app data directory for token storage
            let app_data_dir = window.app_handle().path()
                .app_data_dir()
                .map_err(|e| format!("Failed to get app data dir: {}", e))?;
            
            let token_storage = TokenStorage::new(app_data_dir);
            
            // Save the token
            token_storage.save_youtube_token(token.clone()).await?;
            
            // Convert to PlatformAuth
            Ok(PlatformAuth {
                platform: Platform::YouTube,
                access_token: token.access_token,
                refresh_token: token.refresh_token,
                expires_at: Some(token.expires_at.timestamp()),
                user_info: std::collections::HashMap::new(),
            })
        },
        Platform::TikTok => {
            use crate::auth::{TikTokAuth, TikTokConfig, TokenStorage};
            
            // Load TikTok configuration
            let config = TikTokConfig::from_env()
                .map_err(|e| format!("Failed to load TikTok config: {}", e))?;
            
            // Create auth instance
            let auth = TikTokAuth::new(config);
            
            // Exchange code for token
            let token = auth.exchange_code(code, state).await?;
            
            // Get app data directory for token storage
            let app_data_dir = window.app_handle().path()
                .app_data_dir()
                .map_err(|e| format!("Failed to get app data dir: {}", e))?;
            
            let token_storage = TokenStorage::new(app_data_dir);
            
            // Save the token
            token_storage.save_tiktok_token(token.clone()).await?;
            
            // Convert to PlatformAuth
            Ok(PlatformAuth {
                platform: Platform::TikTok,
                access_token: token.access_token,
                refresh_token: Some(token.refresh_token),
                expires_at: Some(token.expires_at.timestamp()),
                user_info: {
                    let mut info = std::collections::HashMap::new();
                    info.insert("open_id".to_string(), token.open_id);
                    info
                },
            })
        },
        Platform::Instagram => {
            use crate::auth::{InstagramAuth, TokenStorage};
            
            // Get Instagram app credentials from environment
            let client_id = std::env::var("INSTAGRAM_CLIENT_ID")
                .map_err(|_| "INSTAGRAM_CLIENT_ID environment variable not set".to_string())?;
            let client_secret = std::env::var("INSTAGRAM_CLIENT_SECRET")
                .map_err(|_| "INSTAGRAM_CLIENT_SECRET environment variable not set".to_string())?;
            
            // Create auth instance
            let auth = InstagramAuth::new(
                client_id,
                client_secret,
                "http://localhost:3000/auth/instagram/callback".to_string(),
            );
            
            // Exchange code for token
            let token_response = auth.exchange_code(&code).await
                .map_err(|e| e.to_string())?;
            
            // Get user info
            let user_info = auth.get_user_info(&token_response.access_token).await
                .map_err(|e| e.to_string())?;
            
            // Exchange for long-lived token (60 days)
            let long_lived_token = auth.exchange_for_long_lived_token(&token_response.access_token).await
                .map_err(|e| e.to_string())?;
            
            // Get app data directory for token storage
            let app_data_dir = window.app_handle().path()
                .app_data_dir()
                .map_err(|e| format!("Failed to get app data dir: {}", e))?;
            
            let token_storage = TokenStorage::new(app_data_dir);
            
            // Create token struct for storage
            let instagram_token = crate::auth::StoredToken {
                access_token: long_lived_token.access_token.clone(),
                refresh_token: None, // Instagram doesn't use refresh tokens
                expires_at: chrono::Utc::now() + chrono::Duration::days(60), // 60 days for long-lived token
                user_id: user_info.id.clone(),
                username: Some(user_info.username.clone()),
            };
            
            // Save the token
            token_storage.save_instagram_token(instagram_token).await?;
            
            // Convert to PlatformAuth
            Ok(PlatformAuth {
                platform: Platform::Instagram,
                access_token: long_lived_token.access_token,
                refresh_token: None,
                expires_at: Some((chrono::Utc::now() + chrono::Duration::days(60)).timestamp()),
                user_info: {
                    let mut info = std::collections::HashMap::new();
                    info.insert("user_id".to_string(), user_info.id);
                    info.insert("username".to_string(), user_info.username);
                    info.insert("account_type".to_string(), user_info.account_type);
                    info.insert("media_count".to_string(), user_info.media_count.to_string());
                    info
                },
            })
        },
        _ => Err("Platform not supported".to_string()),
    }
}

// Platform-specific upload implementations
async fn upload_to_youtube(
    window: tauri::Window,
    upload_id: String,
    file_path: String,
    config: UploadConfig,
    db_pool: SqlitePool,
) -> Result<UploadResult, String> {
    use crate::auth::TokenStorage;
    use crate::upload::{YouTubeResumableUploader, ResumableUpload};
    use crate::upload::youtube_upload::{VideoMetadata, PrivacyStatus as YTPrivacy};
    
    // Get app data directory for token storage
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let token_storage = TokenStorage::new(app_data_dir);
    
    // Check for valid token
    let token = token_storage.get_valid_youtube_token().await?
        .ok_or_else(|| "No valid YouTube token found. Please authenticate first.".to_string())?;
    
    // Convert our config to YouTube metadata
    let privacy_status = match config.privacy {
        PrivacyLevel::Public => YTPrivacy::Public,
        PrivacyLevel::Private => YTPrivacy::Private,
        PrivacyLevel::Unlisted => YTPrivacy::Unlisted,
        PrivacyLevel::FriendsOnly => YTPrivacy::Private, // YouTube doesn't have friends only
    };
    
    let metadata = VideoMetadata {
        title: config.title.clone(),
        description: config.description.clone(),
        tags: config.tags.clone(),
        category_id: config.category.unwrap_or_else(|| "22".to_string()), // Default to People & Blogs
        privacy_status,
        thumbnail_path: config.thumbnail_path.clone(),
        notify_subscribers: true,
        scheduled_time: config.scheduled_time.and_then(|s| chrono::DateTime::parse_from_rfc3339(&s).ok())
            .map(|dt| dt.with_timezone(&chrono::Utc)),
        playlist_id: config.playlist_id.clone(),
    };
    
    // Update status to uploading
    let db = UploadHistoryDb::new(db_pool.clone());
    db.update_status(&upload_id, DbUploadStatus::Uploading, None).await?;
    
    // Check if this is a resume attempt
    let upload_record = db.get_upload(&upload_id).await?
        .ok_or_else(|| "Upload record not found".to_string())?;
    
    // Create resumable upload
    let mut resumable_upload = ResumableUpload::new(
        upload_id.clone(),
        file_path.clone(),
        upload_record.file_size as u64,
        "youtube".to_string(),
    );
    
    // Restore resume data if available
    if let Some(upload_url) = upload_record.upload_url {
        resumable_upload.set_resume_data(upload_url, upload_record.upload_token);
        resumable_upload.update_progress(upload_record.bytes_uploaded as u64);
    }
    
    // Create uploader
    let uploader = YouTubeResumableUploader::new(token.access_token);
    
    // Upload with progress tracking
    let upload_id_clone = upload_id.clone();
    let window_clone = window.clone();
    let db_clone = db.clone();
    
    let video_id = uploader.resume_upload(
        &mut resumable_upload,
        &metadata,
        move |progress| {
            // Update database progress
            let _ = tauri::async_runtime::block_on(async {
                db_clone.update_progress(
                    &upload_id_clone,
                    progress.percentage as f32,
                    progress.bytes_uploaded as i64
                ).await
            });
            
            let _ = window_clone.emit("upload-progress", UploadProgress {
                upload_id: upload_id_clone.clone(),
                platform: Platform::YouTube,
                progress: progress.percentage as f32,
                status: if progress.percentage >= 100.0 {
                    UploadStatus::Processing
                } else {
                    UploadStatus::Uploading
                },
                message: progress.status.clone(),
            });
        },
    ).await
    .map_err(|e| {
        // Update database on error
        let _ = tauri::async_runtime::block_on(async {
            db.update_status(&upload_id, DbUploadStatus::Failed, Some(e.clone())).await
        });
        e
    })?;
    
    // Save resume data if available
    if let Some(url) = resumable_upload.upload_url {
        db.save_resume_data(&upload_id, &url, resumable_upload.upload_token.as_deref()).await?;
    }
    
    // Final success event
    let _ = window.emit("upload-progress", UploadProgress {
        upload_id: upload_id.clone(),
        platform: Platform::YouTube,
        progress: 100.0,
        status: UploadStatus::Published,
        message: "Video published successfully!".to_string(),
    });
    
    let result = UploadResult {
        upload_id: upload_id.clone(),
        platform: Platform::YouTube,
        url: format!("https://www.youtube.com/watch?v={}", video_id),
        platform_id: video_id.clone(),
        published_at: chrono::Utc::now().to_rfc3339(),
    };
    
    // Update database with completion
    db.update_status(&upload_id, DbUploadStatus::Completed, None).await?;
    db.update_platform_data(&upload_id, &video_id, &result.url).await?;
    
    Ok(result)
}

async fn upload_to_tiktok(
    window: tauri::Window,
    upload_id: String,
    file_path: String,
    config: UploadConfig,
    _db_pool: SqlitePool,
) -> Result<UploadResult, String> {
    use crate::auth::TokenStorage;
    use crate::upload::tiktok_upload::{TikTokUploader, VideoMetadata as TikTokVideoMetadata, PrivacyLevel as TikTokPrivacy};
    
    // Get app data directory for token storage
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let token_storage = TokenStorage::new(app_data_dir);
    
    // Check for valid token
    let tokens = token_storage.load_tokens().await?;
    let tiktok_token = tokens.tiktok
        .ok_or_else(|| "No TikTok token found. Please authenticate first.".to_string())?;
    
    // Check if token is still valid
    if tiktok_token.expires_at < chrono::Utc::now() {
        return Err("TikTok token has expired. Please re-authenticate.".to_string());
    }
    
    // Convert our config to TikTok metadata
    let privacy_level = match config.privacy {
        PrivacyLevel::Public => TikTokPrivacy::PublicToEveryone,
        PrivacyLevel::Private => TikTokPrivacy::SelfOnly,
        PrivacyLevel::Unlisted => TikTokPrivacy::MutualFollowFriends,
        PrivacyLevel::FriendsOnly => TikTokPrivacy::MutualFollowFriends,
    };
    
    let metadata = TikTokVideoMetadata {
        title: config.title.clone(),
        privacy_level,
        allow_comments: true,
        allow_duet: true,
        allow_stitch: true,
        video_cover_timestamp_ms: 1000, // Default to 1 second
    };
    
    // Create uploader
    let uploader = TikTokUploader::new(tiktok_token.access_token);
    
    // Upload with progress tracking
    let share_id = uploader.upload_video(
        &file_path,
        metadata,
        |progress| {
            let _ = window.emit("upload-progress", UploadProgress {
                upload_id: upload_id.clone(),
                platform: Platform::TikTok,
                progress: progress.percentage as f32,
                status: if progress.percentage >= 100.0 {
                    UploadStatus::Processing
                } else {
                    UploadStatus::Uploading
                },
                message: progress.status.clone(),
            });
        },
    ).await?;
    
    // Final success event
    let _ = window.emit("upload-progress", UploadProgress {
        upload_id: upload_id.clone(),
        platform: Platform::TikTok,
        progress: 100.0,
        status: UploadStatus::Published,
        message: "Video published successfully!".to_string(),
    });
    
    Ok(UploadResult {
        upload_id,
        platform: Platform::TikTok,
        url: format!("https://www.tiktok.com/@{}/video/{}", tiktok_token.open_id, share_id),
        platform_id: share_id,
        published_at: chrono::Utc::now().to_rfc3339(),
    })
}

async fn upload_to_instagram(
    window: tauri::Window,
    upload_id: String,
    file_path: String,
    config: UploadConfig,
    db_pool: SqlitePool,
) -> Result<UploadResult, String> {
    use crate::auth::TokenStorage;
    use crate::upload::instagram_upload::{InstagramUploader, InstagramUploadConfig, UserTag};
    use crate::database::upload_history::{UploadHistoryDb, UploadStatus as DbUploadStatus};
    
    // Get app data directory for token storage
    let app_data_dir = window.app_handle().path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let token_storage = TokenStorage::new(app_data_dir);
    
    // Check for valid token
    let tokens = token_storage.load_tokens().await?;
    let instagram_token = tokens.instagram
        .ok_or_else(|| "No Instagram token found. Please authenticate first.".to_string())?;
    
    // Check if token is still valid
    if instagram_token.expires_at < chrono::Utc::now() {
        return Err("Instagram token has expired. Please re-authenticate.".to_string());
    }
    
    // Update database status
    let db = UploadHistoryDb::new(db_pool.clone());
    db.update_status(&upload_id, DbUploadStatus::Uploading, None).await?;
    
    // Create Instagram upload config
    let instagram_config = InstagramUploadConfig {
        caption: format!("{}\n\n{}", config.title, config.description),
        location_id: None,
        user_tags: vec![],
        product_tags: vec![],
        cover_url: config.thumbnail_path.clone(),
        share_to_feed: true,
    };
    
    // Create uploader
    let uploader = InstagramUploader::new(
        instagram_token.access_token.clone(),
        instagram_token.user_id.clone(),
    );
    
    // Emit progress updates
    let _ = window.emit("upload-progress", UploadProgress {
        upload_id: upload_id.clone(),
        platform: Platform::Instagram,
        progress: 10.0,
        status: UploadStatus::Uploading,
        message: "Preparing Instagram upload...".to_string(),
    });
    
    // Determine file type
    let path = std::path::Path::new(&file_path);
    let is_video = path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| matches!(ext.to_lowercase().as_str(), "mp4" | "mov"))
        .unwrap_or(false);
    
    // Upload with progress tracking
    let upload_id_clone = upload_id.clone();
    let window_clone = window.clone();
    let db_clone = db.clone();
    
    let media_id = if is_video {
        uploader.upload_video(
            path,
            instagram_config,
            move |progress| {
                // Update database progress
                let _ = tauri::async_runtime::block_on(async {
                    db_clone.update_progress(
                        &upload_id_clone,
                        progress.progress,
                        0  // We don't have bytes_uploaded anymore
                    ).await
                });
                
                let _ = window_clone.emit("upload-progress", progress.clone());
            },
        ).await
        .map_err(|e| {
            // Update database on error
            let _ = tauri::async_runtime::block_on(async {
                db.update_status(&upload_id, DbUploadStatus::Failed, Some(e.to_string())).await
            });
            e.to_string()
        })?
    } else {
        uploader.upload_image(
            path,
            instagram_config,
            move |progress| {
                // Update database progress
                let _ = tauri::async_runtime::block_on(async {
                    db_clone.update_progress(
                        &upload_id_clone,
                        progress.progress,
                        0  // We don't have bytes_uploaded anymore
                    ).await
                });
                
                let _ = window_clone.emit("upload-progress", progress.clone());
            },
        ).await
        .map_err(|e| {
            // Update database on error
            let _ = tauri::async_runtime::block_on(async {
                db.update_status(&upload_id, DbUploadStatus::Failed, Some(e.to_string())).await
            });
            e.to_string()
        })?
    };
    
    // Get media info for the URL
    let media_info = uploader.get_media_info(&media_id).await
        .map_err(|e| e.to_string())?;
    
    // Final success event
    let _ = window.emit("upload-progress", UploadProgress {
        upload_id: upload_id.clone(),
        platform: Platform::Instagram,
        progress: 100.0,
        status: UploadStatus::Published,
        message: "Posted to Instagram successfully!".to_string(),
    });
    
    let result = UploadResult {
        upload_id: upload_id.clone(),
        platform: Platform::Instagram,
        url: media_info.permalink,
        platform_id: media_id.clone(),
        published_at: chrono::Utc::now().to_rfc3339(),
    };
    
    // Update database with completion
    db.update_status(&upload_id, DbUploadStatus::Completed, None).await?;
    db.update_platform_data(&upload_id, &media_id, &result.url).await?;
    
    Ok(result)
}