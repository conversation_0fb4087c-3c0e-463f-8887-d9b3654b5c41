use std::fmt;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};

/// Professional error handling for production environments
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AppError {
    // Download errors
    DownloadFailed { id: String, reason: String, recoverable: bool },
    NetworkTimeout { url: String, timeout_ms: u64 },
    InsufficientDiskSpace { required: u64, available: u64 },
    CorruptedDownload { file_path: String, expected_hash: Option<String> },
    
    // Upload errors  
    UploadFailed { platform: String, reason: String, retry_after: Option<u64> },
    AuthenticationExpired { platform: String },
    QuotaExceeded { platform: String, reset_time: Option<DateTime<Utc>> },
    
    // System errors
    DependencyMissing { tool: String, install_url: Option<String> },
    PermissionDenied { resource: String },
    ConfigurationError { setting: String, reason: String },
    
    // Business logic errors
    LicenseInvalid { reason: String },
    FeatureNotAvailable { feature: String, required_tier: String },
    RateLimitExceeded { limit: u32, reset_after_seconds: u64 },
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::DownloadFailed { id, reason, recoverable } => {
                write!(f, "Download {} failed: {}. {}", 
                    id, reason, 
                    if *recoverable { "This can be retried." } else { "This cannot be recovered." }
                )
            },
            AppError::NetworkTimeout { url, timeout_ms } => {
                write!(f, "Network timeout after {}ms for URL: {}", timeout_ms, url)
            },
            AppError::InsufficientDiskSpace { required, available } => {
                write!(f, "Insufficient disk space. Required: {} bytes, Available: {} bytes", required, available)
            },
            AppError::DependencyMissing { tool, install_url } => {
                write!(f, "{} is not installed. {}", 
                    tool,
                    install_url.as_ref().map(|u| format!("Install from: {}", u)).unwrap_or_default()
                )
            },
            _ => write!(f, "{:?}", self),
        }
    }
}

impl std::error::Error for AppError {}

/// Error recovery strategies
#[derive(Debug, Clone)]
pub struct ErrorRecovery {
    max_retries: u32,
    backoff_multiplier: f64,
    circuit_breaker_threshold: u32,
    circuit_breaker_timeout: std::time::Duration,
}

impl Default for ErrorRecovery {
    fn default() -> Self {
        Self {
            max_retries: 3,
            backoff_multiplier: 2.0,
            circuit_breaker_threshold: 5,
            circuit_breaker_timeout: std::time::Duration::from_secs(300),
        }
    }
}

/// Circuit breaker pattern for external services
pub struct CircuitBreaker {
    failures: Arc<Mutex<u32>>,
    last_failure: Arc<Mutex<Option<std::time::Instant>>>,
    state: Arc<Mutex<CircuitState>>,
    threshold: u32,
    timeout: std::time::Duration,
}

#[derive(Debug, Clone, PartialEq)]
enum CircuitState {
    Closed,
    Open,
    HalfOpen,
}

impl CircuitBreaker {
    pub fn new(threshold: u32, timeout: std::time::Duration) -> Self {
        Self {
            failures: Arc::new(Mutex::new(0)),
            last_failure: Arc::new(Mutex::new(None)),
            state: Arc::new(Mutex::new(CircuitState::Closed)),
            threshold,
            timeout,
        }
    }

    pub async fn call<F, T, E>(&self, f: F) -> Result<T, AppError>
    where
        F: std::future::Future<Output = Result<T, E>>,
        E: Into<AppError>,
    {
        let current_state = self.state.lock().await.clone();
        
        match current_state {
            CircuitState::Open => {
                // Check if we should transition to half-open
                if let Some(last_failure) = *self.last_failure.lock().await {
                    if last_failure.elapsed() > self.timeout {
                        *self.state.lock().await = CircuitState::HalfOpen;
                    } else {
                        return Err(AppError::NetworkTimeout {
                            url: "Circuit breaker is open".to_string(),
                            timeout_ms: self.timeout.as_millis() as u64,
                        });
                    }
                }
            }
            _ => {}
        }
        
        match f.await {
            Ok(result) => {
                // Reset on success
                *self.failures.lock().await = 0;
                *self.state.lock().await = CircuitState::Closed;
                Ok(result)
            }
            Err(e) => {
                let error = e.into();
                let mut failures = self.failures.lock().await;
                *failures += 1;
                
                if *failures >= self.threshold {
                    *self.state.lock().await = CircuitState::Open;
                    *self.last_failure.lock().await = Some(std::time::Instant::now());
                }
                
                Err(error)
            }
        }
    }
}

/// Global error reporter for production telemetry
pub struct ErrorReporter {
    errors: Arc<Mutex<Vec<ErrorReport>>>,
    crash_handler: Option<Box<dyn Fn(&AppError) + Send + Sync>>,
}

#[derive(Debug, Clone, Serialize)]
struct ErrorReport {
    error: AppError,
    timestamp: DateTime<Utc>,
    context: ErrorContext,
    stack_trace: Option<String>,
}

#[derive(Debug, Clone, Serialize)]
struct ErrorContext {
    user_id: Option<String>,
    session_id: String,
    app_version: String,
    os: String,
    arch: String,
    locale: String,
}

impl ErrorReporter {
    pub fn new() -> Self {
        Self {
            errors: Arc::new(Mutex::new(Vec::new())),
            crash_handler: None,
        }
    }

    pub async fn report(&self, error: AppError, context: ErrorContext) {
        let report = ErrorReport {
            error: error.clone(),
            timestamp: Utc::now(),
            context,
            stack_trace: std::backtrace::Backtrace::capture().to_string().into(),
        };
        
        let mut errors = self.errors.lock().await;
        errors.push(report.clone());
        
        // Keep only last 1000 errors in memory
        if errors.len() > 1000 {
            errors.drain(0..100);
        }
        
        // Send to telemetry service (if configured)
        self.send_telemetry(report).await;
        
        // Check if this is a critical error that needs immediate attention
        if self.is_critical_error(&error) {
            if let Some(handler) = &self.crash_handler {
                handler(&error);
            }
        }
    }

    async fn send_telemetry(&self, report: ErrorReport) {
        // In production, this would send to Sentry, Datadog, etc.
        // For now, we'll just log it
        log::error!("Error Report: {:?}", report);
    }

    fn is_critical_error(&self, error: &AppError) -> bool {
        matches!(error, 
            AppError::LicenseInvalid { .. } |
            AppError::ConfigurationError { .. } |
            AppError::PermissionDenied { .. }
        )
    }
}

/// Graceful degradation handler
pub struct GracefulDegradation {
    feature_flags: Arc<Mutex<std::collections::HashMap<String, bool>>>,
    fallback_strategies: Arc<Mutex<std::collections::HashMap<String, FallbackStrategy>>>,
}

#[derive(Debug, Clone)]
enum FallbackStrategy {
    UseCache,
    ReduceQuality,
    DisableFeature,
    ShowWarning,
}

impl GracefulDegradation {
    pub async fn handle_failure(&self, feature: &str, error: &AppError) -> Result<(), AppError> {
        let strategies = self.fallback_strategies.lock().await;
        
        if let Some(strategy) = strategies.get(feature) {
            match strategy {
                FallbackStrategy::UseCache => {
                    log::warn!("Using cached data for feature: {}", feature);
                    Ok(())
                }
                FallbackStrategy::ReduceQuality => {
                    log::warn!("Reducing quality for feature: {}", feature);
                    Ok(())
                }
                FallbackStrategy::DisableFeature => {
                    let mut flags = self.feature_flags.lock().await;
                    flags.insert(feature.to_string(), false);
                    log::warn!("Disabled feature due to error: {}", feature);
                    Ok(())
                }
                FallbackStrategy::ShowWarning => {
                    log::warn!("Feature degraded: {}", feature);
                    Ok(())
                }
            }
        } else {
            Err(error.clone())
        }
    }
}

// Global instances
lazy_static::lazy_static! {
    pub static ref ERROR_REPORTER: ErrorReporter = ErrorReporter::new();
    pub static ref YOUTUBE_CIRCUIT_BREAKER: CircuitBreaker = CircuitBreaker::new(5, std::time::Duration::from_secs(300));
    pub static ref TIKTOK_CIRCUIT_BREAKER: CircuitBreaker = CircuitBreaker::new(5, std::time::Duration::from_secs(300));
}

/// Automatic error recovery macro
#[macro_export]
macro_rules! with_recovery {
    ($expr:expr, $recovery:expr) => {{
        let mut attempts = 0;
        let mut last_error = None;
        
        loop {
            match $expr {
                Ok(result) => break Ok(result),
                Err(e) => {
                    attempts += 1;
                    last_error = Some(e);
                    
                    if attempts >= $recovery.max_retries {
                        break Err(last_error.unwrap());
                    }
                    
                    let backoff = std::time::Duration::from_secs(
                        (attempts as f64 * $recovery.backoff_multiplier) as u64
                    );
                    tokio::time::sleep(backoff).await;
                }
            }
        }
    }};
}