use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use sha2::{Sha256, Digest};

/// Enterprise-grade licensing system
pub struct LicensingSystem {
    config: LicenseConfig,
    current_license: Arc<RwLock<Option<License>>>,
    hardware_id: String,
    validation_cache: Arc<RwLock<ValidationCache>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseConfig {
    pub validation_endpoint: String,
    pub public_key: String,
    pub offline_grace_period_days: i64,
    pub validation_interval_hours: u64,
    pub enable_hardware_lock: bool,
    pub allow_vm: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct License {
    pub key: String,
    pub type_: LicenseType,
    pub status: LicenseStatus,
    pub email: String,
    pub company: Option<String>,
    pub issued_at: DateTime<Utc>,
    pub expires_at: Option<DateTime<Utc>>,
    pub features: Vec<String>,
    pub max_devices: u32,
    pub metadata: serde_json::Value,
    pub signature: String,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LicenseType {
    Trial,
    Personal,
    Professional,
    Team,
    Enterprise,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum LicenseStatus {
    Active,
    Expired,
    Suspended,
    Revoked,
}

#[derive(Debug, Clone)]
struct ValidationCache {
    last_online_check: Option<DateTime<Utc>>,
    last_result: Option<ValidationResult>,
    offline_uses: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct ValidationResult {
    valid: bool,
    license: Option<License>,
    message: Option<String>,
    server_time: DateTime<Utc>,
}

impl LicensingSystem {
    pub fn new(config: LicenseConfig) -> Self {
        let hardware_id = Self::generate_hardware_id();
        
        Self {
            config,
            current_license: Arc::new(RwLock::new(None)),
            hardware_id,
            validation_cache: Arc::new(RwLock::new(ValidationCache {
                last_online_check: None,
                last_result: None,
                offline_uses: 0,
            })),
        }
    }

    /// Generate unique hardware ID for license binding
    fn generate_hardware_id() -> String {
        let mut hasher = Sha256::new();
        
        // Combine multiple hardware identifiers
        #[cfg(target_os = "windows")]
        {
            // Windows: Use WMI to get motherboard serial
            if let Ok(output) = std::process::Command::new("wmic")
                .args(&["baseboard", "get", "serialnumber"])
                .output()
            {
                hasher.update(&output.stdout);
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            // macOS: Use system profiler
            if let Ok(output) = std::process::Command::new("system_profiler")
                .args(&["SPHardwareDataType"])
                .output()
            {
                hasher.update(&output.stdout);
            }
        }
        
        #[cfg(target_os = "linux")]
        {
            // Linux: Use machine-id
            if let Ok(machine_id) = std::fs::read_to_string("/etc/machine-id") {
                hasher.update(machine_id.trim().as_bytes());
            }
        }
        
        // Add MAC addresses
        if let Ok(interfaces) = get_if_addrs::get_if_addrs() {
            for interface in interfaces {
                // Use debug representation for IfAddr
                hasher.update(format!("{:?}", interface.addr).as_bytes());
            }
        }
        
        hex::encode(hasher.finalize())
    }

    pub async fn activate_license(&self, license_key: &str) -> Result<License, String> {
        // Validate license format
        if !self.validate_license_format(license_key) {
            return Err("Invalid license key format".to_string());
        }
        
        // Online activation
        let activation_request = ActivationRequest {
            license_key: license_key.to_string(),
            hardware_id: self.hardware_id.clone(),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            os: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            timezone: "UTC".to_string(), // Use UTC for simplicity
        };
        
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;
        
        let response = client
            .post(&format!("{}/activate", self.config.validation_endpoint))
            .json(&activation_request)
            .send()
            .await
            .map_err(|e| format!("Activation request failed: {}", e))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Activation failed: {}", error_text));
        }
        
        let activation_response: ActivationResponse = response
            .json()
            .await
            .map_err(|e| format!("Invalid activation response: {}", e))?;
        
        // Verify license signature
        if !self.verify_license_signature(&activation_response.license) {
            return Err("License signature verification failed".to_string());
        }
        
        // Store license
        *self.current_license.write().await = Some(activation_response.license.clone());
        
        // Save to secure storage
        self.save_license(&activation_response.license).await?;
        
        // Update validation cache
        let mut cache = self.validation_cache.write().await;
        cache.last_online_check = Some(Utc::now());
        cache.last_result = Some(ValidationResult {
            valid: true,
            license: Some(activation_response.license.clone()),
            message: None,
            server_time: Utc::now(),
        });
        
        Ok(activation_response.license)
    }

    pub async fn validate_license(&self) -> Result<bool, String> {
        let license = match self.current_license.read().await.clone() {
            Some(lic) => lic,
            None => {
                // Try to load from storage
                match self.load_license().await {
                    Ok(lic) => {
                        *self.current_license.write().await = Some(lic.clone());
                        lic
                    }
                    Err(_) => return Ok(false),
                }
            }
        };
        
        // Check basic validity
        if license.status != LicenseStatus::Active {
            return Ok(false);
        }
        
        // Check expiration
        if let Some(expires_at) = license.expires_at {
            if Utc::now() > expires_at {
                return Ok(false);
            }
        }
        
        // Check if online validation is needed
        let mut cache = self.validation_cache.write().await;
        let should_validate_online = match cache.last_online_check {
            Some(last_check) => {
                let hours_since_check = (Utc::now() - last_check).num_hours();
                hours_since_check >= self.config.validation_interval_hours as i64
            }
            None => true,
        };
        
        if should_validate_online {
            // Try online validation
            match self.validate_online(&license).await {
                Ok(result) => {
                    cache.last_online_check = Some(Utc::now());
                    cache.last_result = Some(result.clone());
                    cache.offline_uses = 0;
                    
                    if !result.valid {
                        // License is invalid, update status
                        let mut license_mut = self.current_license.write().await;
                        if let Some(lic) = license_mut.as_mut() {
                            lic.status = LicenseStatus::Revoked;
                        }
                        return Ok(false);
                    }
                    
                    return Ok(result.valid);
                }
                Err(_) => {
                    // Offline validation fallback
                    cache.offline_uses += 1;
                    
                    // Check offline grace period
                    if let Some(last_check) = cache.last_online_check {
                        let days_offline = (Utc::now() - last_check).num_days();
                        if days_offline > self.config.offline_grace_period_days {
                            return Err("License validation required. Please connect to the internet.".to_string());
                        }
                    }
                    
                    // Allow offline use within grace period
                    return Ok(true);
                }
            }
        }
        
        Ok(true)
    }

    async fn validate_online(&self, license: &License) -> Result<ValidationResult, String> {
        let validation_request = ValidationRequest {
            license_key: license.key.clone(),
            hardware_id: self.hardware_id.clone(),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
        };
        
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
            .map_err(|e| format!("Failed to create HTTP client: {}", e))?;
        
        let response = client
            .post(&format!("{}/validate", self.config.validation_endpoint))
            .json(&validation_request)
            .send()
            .await
            .map_err(|e| format!("Validation request failed: {}", e))?;
        
        if !response.status().is_success() {
            return Err("Validation request failed".to_string());
        }
        
        let result: ValidationResult = response
            .json()
            .await
            .map_err(|e| format!("Invalid validation response: {}", e))?;
        
        Ok(result)
    }

    pub async fn get_license_info(&self) -> Option<LicenseInfo> {
        let license = self.current_license.read().await;
        license.as_ref().map(|lic| LicenseInfo {
            type_: lic.type_.clone(),
            email: lic.email.clone(),
            company: lic.company.clone(),
            expires_at: lic.expires_at,
            features: lic.features.clone(),
            days_remaining: lic.expires_at.map(|exp| (exp - Utc::now()).num_days()),
        })
    }

    pub async fn check_feature(&self, feature: &str) -> bool {
        let license = self.current_license.read().await;
        match license.as_ref() {
            Some(lic) => {
                // Check if feature is included in license
                lic.features.contains(&feature.to_string()) ||
                // Enterprise licenses have all features
                lic.type_ == LicenseType::Enterprise ||
                // Check type-based features
                match feature {
                    "batch_download" => matches!(lic.type_, LicenseType::Professional | LicenseType::Team | LicenseType::Enterprise),
                    "api_access" => matches!(lic.type_, LicenseType::Team | LicenseType::Enterprise),
                    "priority_support" => matches!(lic.type_, LicenseType::Enterprise),
                    _ => false,
                }
            }
            None => false,
        }
    }

    fn validate_license_format(&self, key: &str) -> bool {
        // License format: XXXX-XXXX-XXXX-XXXX
        let parts: Vec<&str> = key.split('-').collect();
        parts.len() == 4 && parts.iter().all(|part| part.len() == 4)
    }

    fn verify_license_signature(&self, license: &License) -> bool {
        // Verify license hasn't been tampered with
        // In production, use proper RSA/ECDSA signature verification
        !license.signature.is_empty()
    }

    async fn save_license(&self, license: &License) -> Result<(), String> {
        // Save to secure storage (OS keychain)
        // For now, save to encrypted file
        let config_dir = dirs::config_local_dir()
            .ok_or("Failed to get config directory")?;
        let license_file = config_dir.join("FlowDownload").join(".license");
        
        // Create directory if it doesn't exist
        if let Some(parent) = license_file.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| format!("Failed to create config directory: {}", e))?;
        }
        
        // Serialize and encrypt license
        let license_data = serde_json::to_vec(license)
            .map_err(|e| format!("Failed to serialize license: {}", e))?;
        
        // Simple XOR encryption (use proper encryption in production)
        let encrypted = license_data.iter()
            .enumerate()
            .map(|(i, &b)| b ^ (self.hardware_id.as_bytes()[i % self.hardware_id.len()]))
            .collect::<Vec<u8>>();
        
        tokio::fs::write(&license_file, encrypted).await
            .map_err(|e| format!("Failed to save license: {}", e))?;
        
        Ok(())
    }

    async fn load_license(&self) -> Result<License, String> {
        let config_dir = dirs::config_local_dir()
            .ok_or("Failed to get config directory")?;
        let license_file = config_dir.join("FlowDownload").join(".license");
        
        let encrypted = tokio::fs::read(&license_file).await
            .map_err(|e| format!("Failed to read license file: {}", e))?;
        
        // Decrypt
        let decrypted = encrypted.iter()
            .enumerate()
            .map(|(i, &b)| b ^ (self.hardware_id.as_bytes()[i % self.hardware_id.len()]))
            .collect::<Vec<u8>>();
        
        let license: License = serde_json::from_slice(&decrypted)
            .map_err(|e| format!("Failed to parse license: {}", e))?;
        
        Ok(license)
    }

    pub async fn start_trial(&self) -> Result<License, String> {
        // Create a trial license
        let trial_request = TrialRequest {
            hardware_id: self.hardware_id.clone(),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            os: std::env::consts::OS.to_string(),
        };
        
        let client = reqwest::Client::new();
        let response = client
            .post(&format!("{}/trial", self.config.validation_endpoint))
            .json(&trial_request)
            .send()
            .await
            .map_err(|e| format!("Trial request failed: {}", e))?;
        
        if !response.status().is_success() {
            return Err("Failed to start trial".to_string());
        }
        
        let trial_license: License = response
            .json()
            .await
            .map_err(|e| format!("Invalid trial response: {}", e))?;
        
        // Save trial license
        *self.current_license.write().await = Some(trial_license.clone());
        self.save_license(&trial_license).await?;
        
        Ok(trial_license)
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct LicenseInfo {
    pub type_: LicenseType,
    pub email: String,
    pub company: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub features: Vec<String>,
    pub days_remaining: Option<i64>,
}

#[derive(Debug, Serialize)]
struct ActivationRequest {
    license_key: String,
    hardware_id: String,
    app_version: String,
    os: String,
    arch: String,
    timezone: String,
}

#[derive(Debug, Deserialize)]
struct ActivationResponse {
    license: License,
    message: Option<String>,
}

#[derive(Debug, Serialize)]
struct ValidationRequest {
    license_key: String,
    hardware_id: String,
    app_version: String,
}

#[derive(Debug, Serialize)]
struct TrialRequest {
    hardware_id: String,
    app_version: String,
    os: String,
}

// Tauri commands
#[tauri::command]
pub async fn activate_license_key(key: String) -> Result<LicenseInfo, String> {
    let licensing = crate::LICENSING.lock().await;
    let license = licensing.activate_license(&key).await?;
    Ok(LicenseInfo {
        type_: license.type_.clone(),
        email: license.email.clone(),
        company: license.company.clone(),
        expires_at: license.expires_at,
        features: license.features.clone(),
        days_remaining: license.expires_at.map(|exp| (exp - Utc::now()).num_days()),
    })
}

#[tauri::command]
pub async fn get_license_status() -> Result<Option<LicenseInfo>, String> {
    let licensing = crate::LICENSING.lock().await;
    Ok(licensing.get_license_info().await)
}

#[tauri::command]
pub async fn start_free_trial() -> Result<LicenseInfo, String> {
    let licensing = crate::LICENSING.lock().await;
    let license = licensing.start_trial().await?;
    Ok(LicenseInfo {
        type_: license.type_.clone(),
        email: license.email.clone(),
        company: license.company.clone(),
        expires_at: license.expires_at,
        features: license.features.clone(),
        days_remaining: license.expires_at.map(|exp| (exp - Utc::now()).num_days()),
    })
}