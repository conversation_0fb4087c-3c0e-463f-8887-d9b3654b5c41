use anyhow::{Context, Result};
use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine};
use rand::Rng;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use url::Url;

#[derive(Debug, <PERSON>lone)]
pub struct InstagramAuth {
    client_id: String,
    client_secret: String,
    redirect_uri: String,
    client: Client,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InstagramTokenResponse {
    pub access_token: String,
    pub user_id: String,
    pub permissions: Vec<String>,
    #[serde(default)]
    pub expires_in: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InstagramUserInfo {
    pub id: String,
    pub username: String,
    pub account_type: String,
    pub media_count: i32,
}

impl InstagramAuth {
    pub fn new(client_id: String, client_secret: String, redirect_uri: String) -> Self {
        Self {
            client_id,
            client_secret,
            redirect_uri,
            client: Client::new(),
        }
    }

    /// Generate OAuth authorization URL for Instagram
    pub fn get_auth_url(&self, state: &str) -> Result<String> {
        let mut auth_url = Url::parse("https://api.instagram.com/oauth/authorize")
            .context("Failed to parse Instagram auth URL")?;

        auth_url
            .query_pairs_mut()
            .append_pair("client_id", &self.client_id)
            .append_pair("redirect_uri", &self.redirect_uri)
            .append_pair("scope", "user_profile,user_media,instagram_graph_user_profile,instagram_graph_user_media,instagram_content_publish")
            .append_pair("response_type", "code")
            .append_pair("state", state);

        Ok(auth_url.to_string())
    }

    /// Exchange authorization code for access token
    pub async fn exchange_code(&self, code: &str) -> Result<InstagramTokenResponse> {
        let params = [
            ("client_id", &self.client_id),
            ("client_secret", &self.client_secret),
            ("grant_type", &"authorization_code".to_string()),
            ("redirect_uri", &self.redirect_uri),
            ("code", &code.to_string()),
        ];

        let response = self
            .client
            .post("https://api.instagram.com/oauth/access_token")
            .form(&params)
            .send()
            .await
            .context("Failed to exchange Instagram auth code")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Instagram token exchange failed: {}", error_text);
        }

        let token_response: InstagramTokenResponse = response
            .json()
            .await
            .context("Failed to parse Instagram token response")?;

        Ok(token_response)
    }

    /// Get user information
    pub async fn get_user_info(&self, access_token: &str) -> Result<InstagramUserInfo> {
        let url = format!(
            "https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token={}",
            access_token
        );

        let response = self
            .client
            .get(&url)
            .send()
            .await
            .context("Failed to get Instagram user info")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to get Instagram user info: {}", error_text);
        }

        let user_info: InstagramUserInfo = response
            .json()
            .await
            .context("Failed to parse Instagram user info")?;

        Ok(user_info)
    }

    /// Exchange short-lived token for long-lived token (60 days)
    pub async fn exchange_for_long_lived_token(&self, access_token: &str) -> Result<InstagramTokenResponse> {
        let params = [
            ("grant_type", "ig_exchange_token"),
            ("client_secret", &self.client_secret),
            ("access_token", access_token),
        ];

        let response = self
            .client
            .get("https://graph.instagram.com/access_token")
            .query(&params)
            .send()
            .await
            .context("Failed to exchange for long-lived token")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to exchange for long-lived token: {}", error_text);
        }

        let token_response: InstagramTokenResponse = response
            .json()
            .await
            .context("Failed to parse long-lived token response")?;

        Ok(token_response)
    }

    /// Refresh long-lived token (call before expiration)
    pub async fn refresh_long_lived_token(&self, access_token: &str) -> Result<InstagramTokenResponse> {
        let params = [
            ("grant_type", "ig_refresh_token"),
            ("access_token", access_token),
        ];

        let response = self
            .client
            .get("https://graph.instagram.com/refresh_access_token")
            .query(&params)
            .send()
            .await
            .context("Failed to refresh long-lived token")?;

        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            anyhow::bail!("Failed to refresh long-lived token: {}", error_text);
        }

        let token_response: InstagramTokenResponse = response
            .json()
            .await
            .context("Failed to parse refreshed token response")?;

        Ok(token_response)
    }
}

/// Generate a secure random state parameter for OAuth
pub fn generate_state() -> String {
    let mut rng = rand::thread_rng();
    let random_bytes: Vec<u8> = (0..32).map(|_| rng.gen()).collect();
    URL_SAFE_NO_PAD.encode(random_bytes)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_state() {
        let state1 = generate_state();
        let state2 = generate_state();
        
        assert_ne!(state1, state2);
        assert!(state1.len() > 20);
        assert!(state2.len() > 20);
    }

    #[test]
    fn test_auth_url_generation() {
        let auth = InstagramAuth::new(
            "test_client_id".to_string(),
            "test_client_secret".to_string(),
            "http://localhost:3000/callback".to_string(),
        );

        let state = "test_state";
        let url = auth.get_auth_url(state).unwrap();

        assert!(url.contains("client_id=test_client_id"));
        assert!(url.contains("redirect_uri=http"));
        assert!(url.contains("state=test_state"));
        assert!(url.contains("scope="));
        assert!(url.contains("response_type=code"));
    }
}