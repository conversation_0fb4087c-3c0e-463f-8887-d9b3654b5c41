use super::YouTubeToken;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tokio::fs;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct StoredTokens {
    pub youtube: Option<YouTubeToken>,
    pub tiktok: Option<TikTokToken>,
    pub instagram: Option<InstagramToken>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct TikTokToken {
    pub access_token: String,
    pub open_id: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct InstagramToken {
    pub access_token: String,
    pub user_id: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StoredToken {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub user_id: String,
    pub username: Option<String>,
}

pub struct TokenStorage {
    storage_path: PathBuf,
}

impl TokenStorage {
    pub fn new(app_data_dir: PathBuf) -> Self {
        let storage_path = app_data_dir.join("auth_tokens.json");
        Self { storage_path }
    }

    pub async fn load_tokens(&self) -> Result<StoredTokens, String> {
        if !self.storage_path.exists() {
            return Ok(StoredTokens {
                youtube: None,
                tiktok: None,
                instagram: None,
            });
        }

        let contents = fs::read_to_string(&self.storage_path)
            .await
            .map_err(|e| format!("Failed to read tokens: {}", e))?;

        // In production, decrypt the contents here
        let tokens: StoredTokens = serde_json::from_str(&contents)
            .map_err(|e| format!("Failed to parse tokens: {}", e))?;

        Ok(tokens)
    }

    pub async fn save_youtube_token(&self, token: YouTubeToken) -> Result<(), String> {
        let mut tokens = self.load_tokens().await.unwrap_or_default();
        tokens.youtube = Some(token);
        self.save_tokens(&tokens).await
    }

    pub async fn save_tiktok_token(&self, token: super::tiktok_auth::TikTokToken) -> Result<(), String> {
        let mut tokens = self.load_tokens().await.unwrap_or_default();
        tokens.tiktok = Some(TikTokToken {
            access_token: token.access_token,
            open_id: token.open_id,
            expires_at: token.expires_at,
        });
        self.save_tokens(&tokens).await
    }

    pub async fn save_instagram_token(&self, token: StoredToken) -> Result<(), String> {
        let mut tokens = self.load_tokens().await.unwrap_or_default();
        tokens.instagram = Some(InstagramToken {
            access_token: token.access_token,
            user_id: token.user_id,
            expires_at: token.expires_at,
        });
        self.save_tokens(&tokens).await
    }

    pub async fn save_tokens(&self, tokens: &StoredTokens) -> Result<(), String> {
        // Ensure directory exists
        if let Some(parent) = self.storage_path.parent() {
            fs::create_dir_all(parent)
                .await
                .map_err(|e| format!("Failed to create directory: {}", e))?;
        }

        // In production, encrypt the tokens here
        let contents = serde_json::to_string_pretty(tokens)
            .map_err(|e| format!("Failed to serialize tokens: {}", e))?;

        fs::write(&self.storage_path, contents)
            .await
            .map_err(|e| format!("Failed to write tokens: {}", e))?;

        // Set appropriate file permissions (Unix-like systems)
        #[cfg(unix)]
        {
            use std::os::unix::fs::PermissionsExt;
            let permissions = std::fs::Permissions::from_mode(0o600); // Read/write for owner only
            std::fs::set_permissions(&self.storage_path, permissions)
                .map_err(|e| format!("Failed to set permissions: {}", e))?;
        }

        Ok(())
    }

    pub async fn clear_youtube_token(&self) -> Result<(), String> {
        let mut tokens = self.load_tokens().await?;
        tokens.youtube = None;
        self.save_tokens(&tokens).await
    }

    pub async fn get_valid_youtube_token(&self) -> Result<Option<YouTubeToken>, String> {
        let tokens = self.load_tokens().await?;
        
        if let Some(token) = tokens.youtube {
            // Check if token is still valid (with 5 minute buffer)
            let now = chrono::Utc::now();
            let buffer = chrono::Duration::minutes(5);
            
            if token.expires_at > now + buffer {
                Ok(Some(token))
            } else {
                Ok(None) // Token expired
            }
        } else {
            Ok(None)
        }
    }
}

impl Default for StoredTokens {
    fn default() -> Self {
        Self {
            youtube: None,
            tiktok: None,
            instagram: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_token_storage() {
        let temp_dir = TempDir::new().unwrap();
        let storage = TokenStorage::new(temp_dir.path().to_path_buf());

        // Test saving YouTube token
        let token = YouTubeToken {
            access_token: "test_access".to_string(),
            refresh_token: Some("test_refresh".to_string()),
            expires_at: chrono::Utc::now() + chrono::Duration::hours(1),
            scope: "youtube.upload".to_string(),
        };

        storage.save_youtube_token(token.clone()).await.unwrap();

        // Test loading token
        let loaded = storage.get_valid_youtube_token().await.unwrap();
        assert!(loaded.is_some());
        assert_eq!(loaded.unwrap().access_token, "test_access");

        // Test clearing token
        storage.clear_youtube_token().await.unwrap();
        let cleared = storage.get_valid_youtube_token().await.unwrap();
        assert!(cleared.is_none());
    }
}