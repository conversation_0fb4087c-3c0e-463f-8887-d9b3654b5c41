use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct YouTubeConfig {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_uri: String,
    pub token_uri: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct YouTubeToken {
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: DateTime<Utc>,
    pub scope: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthState {
    pub csrf_token: String,
    pub pkce_verifier: String,
    pub created_at: DateTime<Utc>,
}

impl YouTubeConfig {
    pub fn from_env() -> Result<Self, String> {
        // For development, we'll use placeholder values
        // In production, these should come from environment variables
        Ok(Self {
            client_id: std::env::var("YOUTUBE_CLIENT_ID")
                .unwrap_or_else(|_| "YOUR_CLIENT_ID".to_string()),
            client_secret: std::env::var("YOUTUBE_CLIENT_SECRET")
                .unwrap_or_else(|_| "YOUR_CLIENT_SECRET".to_string()),
            redirect_uri: "http://localhost:1420/auth/youtube/callback".to_string(),
            auth_uri: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_uri: "https://oauth2.googleapis.com/token".to_string(),
        })
    }

    pub fn generate_auth_url(&self, state: &str) -> String {
        let scopes = vec![
            "https://www.googleapis.com/auth/youtube.upload",
            "https://www.googleapis.com/auth/youtube",
            "https://www.googleapis.com/auth/youtube.readonly",
        ];
        
        let scope_str = scopes.join(" ");

        let params = vec![
            ("client_id", self.client_id.as_str()),
            ("redirect_uri", self.redirect_uri.as_str()),
            ("response_type", "code"),
            ("scope", scope_str.as_str()),
            ("access_type", "offline"),
            ("state", state),
            ("prompt", "consent"),
        ];

        let query = params.iter()
            .map(|(k, v)| format!("{}={}", k, urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&");

        format!("{}?{}", self.auth_uri, query)
    }
}

pub struct YouTubeAuth {
    config: YouTubeConfig,
    client: reqwest::Client,
    // In production, use proper storage instead of in-memory
    auth_states: std::sync::Arc<tokio::sync::Mutex<HashMap<String, AuthState>>>,
}

impl YouTubeAuth {
    pub fn new(config: YouTubeConfig) -> Self {
        Self {
            config,
            client: reqwest::Client::new(),
            auth_states: std::sync::Arc::new(tokio::sync::Mutex::new(HashMap::new())),
        }
    }

    pub async fn initiate_auth(&self) -> Result<(String, String), String> {
        // Generate CSRF token
        let csrf_token = uuid::Uuid::new_v4().to_string();
        
        // Generate PKCE verifier (would use proper PKCE in production)
        let pkce_verifier = uuid::Uuid::new_v4().to_string();
        
        // Store auth state
        let state = AuthState {
            csrf_token: csrf_token.clone(),
            pkce_verifier: pkce_verifier.clone(),
            created_at: Utc::now(),
        };
        
        let mut states = self.auth_states.lock().await;
        states.insert(csrf_token.clone(), state);
        
        // Clean up old states (older than 10 minutes)
        let cutoff = Utc::now() - chrono::Duration::minutes(10);
        states.retain(|_, v| v.created_at > cutoff);
        
        let auth_url = self.config.generate_auth_url(&csrf_token);
        
        Ok((auth_url, csrf_token))
    }

    pub async fn exchange_code(
        &self,
        code: String,
        state: String,
    ) -> Result<YouTubeToken, String> {
        // Verify state
        let mut states = self.auth_states.lock().await;
        let _auth_state = states.remove(&state)
            .ok_or_else(|| "Invalid or expired state".to_string())?;
        
        // Exchange code for token
        let params = [
            ("client_id", self.config.client_id.as_str()),
            ("client_secret", self.config.client_secret.as_str()),
            ("code", code.as_str()),
            ("redirect_uri", self.config.redirect_uri.as_str()),
            ("grant_type", "authorization_code"),
        ];
        
        let response = self.client
            .post(&self.config.token_uri)
            .form(&params)
            .send()
            .await
            .map_err(|e| format!("Failed to exchange code: {}", e))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Token exchange failed: {}", error_text));
        }
        
        let token_response: TokenResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse token response: {}", e))?;
        
        Ok(YouTubeToken {
            access_token: token_response.access_token,
            refresh_token: token_response.refresh_token,
            expires_at: Utc::now() + chrono::Duration::seconds(token_response.expires_in as i64),
            scope: token_response.scope.unwrap_or_default(),
        })
    }

    pub async fn refresh_token(&self, refresh_token: &str) -> Result<YouTubeToken, String> {
        let params = [
            ("client_id", self.config.client_id.as_str()),
            ("client_secret", self.config.client_secret.as_str()),
            ("refresh_token", refresh_token),
            ("grant_type", "refresh_token"),
        ];
        
        let response = self.client
            .post(&self.config.token_uri)
            .form(&params)
            .send()
            .await
            .map_err(|e| format!("Failed to refresh token: {}", e))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Token refresh failed: {}", error_text));
        }
        
        let token_response: TokenResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse refresh response: {}", e))?;
        
        Ok(YouTubeToken {
            access_token: token_response.access_token,
            refresh_token: Some(refresh_token.to_string()), // Keep the same refresh token
            expires_at: Utc::now() + chrono::Duration::seconds(token_response.expires_in as i64),
            scope: token_response.scope.unwrap_or_default(),
        })
    }
}

#[derive(Debug, Deserialize)]
struct TokenResponse {
    access_token: String,
    refresh_token: Option<String>,
    expires_in: u64,
    scope: Option<String>,
    token_type: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_auth_url_generation() {
        let config = YouTubeConfig {
            client_id: "test_client".to_string(),
            client_secret: "test_secret".to_string(),
            redirect_uri: "http://localhost:1420/callback".to_string(),
            auth_uri: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_uri: "https://oauth2.googleapis.com/token".to_string(),
        };
        
        let url = config.generate_auth_url("test_state");
        
        assert!(url.contains("client_id=test_client"));
        assert!(url.contains("response_type=code"));
        assert!(url.contains("state=test_state"));
        assert!(url.contains("youtube.upload"));
    }
}