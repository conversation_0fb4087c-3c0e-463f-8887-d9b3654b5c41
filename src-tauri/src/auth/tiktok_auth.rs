use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, Duration};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TikTokConfig {
    pub client_key: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_url: String,
    pub token_url: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TikTokToken {
    pub access_token: String,
    pub open_id: String,
    pub refresh_token: String,
    pub expires_in: i64,
    pub refresh_expires_in: i64,
    pub scope: String,
    #[serde(skip)]
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthState {
    pub csrf_token: String,
    pub created_at: DateTime<Utc>,
}

impl TikTokConfig {
    pub fn from_env() -> Result<Self, String> {
        Ok(Self {
            client_key: std::env::var("TIKTOK_CLIENT_KEY")
                .unwrap_or_else(|_| "YOUR_CLIENT_KEY".to_string()),
            client_secret: std::env::var("TIKTOK_CLIENT_SECRET")
                .unwrap_or_else(|_| "YOUR_CLIENT_SECRET".to_string()),
            redirect_uri: "http://localhost:1420/auth/tiktok/callback".to_string(),
            auth_url: "https://www.tiktok.com/auth/authorize/".to_string(),
            token_url: "https://open-api.tiktok.com/oauth/access_token/".to_string(),
        })
    }

    pub fn generate_auth_url(&self, state: &str) -> String {
        let scopes = vec![
            "user.info.basic",
            "video.upload", 
            "video.publish",
        ];
        
        let scope_str = scopes.join(",");

        let params = vec![
            ("client_key", self.client_key.as_str()),
            ("scope", scope_str.as_str()),
            ("response_type", "code"),
            ("redirect_uri", self.redirect_uri.as_str()),
            ("state", state),
        ];

        let query = params.iter()
            .map(|(k, v)| format!("{}={}", k, urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&");

        format!("{}?{}", self.auth_url, query)
    }
}

pub struct TikTokAuth {
    config: TikTokConfig,
    client: reqwest::Client,
    auth_states: std::sync::Arc<tokio::sync::Mutex<HashMap<String, AuthState>>>,
}

impl TikTokAuth {
    pub fn new(config: TikTokConfig) -> Self {
        Self {
            config,
            client: reqwest::Client::new(),
            auth_states: std::sync::Arc::new(tokio::sync::Mutex::new(HashMap::new())),
        }
    }

    pub async fn initiate_auth(&self) -> Result<(String, String), String> {
        // Generate CSRF token
        let csrf_token = uuid::Uuid::new_v4().to_string();
        
        // Store auth state
        let state = AuthState {
            csrf_token: csrf_token.clone(),
            created_at: Utc::now(),
        };
        
        let mut states = self.auth_states.lock().await;
        states.insert(csrf_token.clone(), state);
        
        // Clean up old states (older than 10 minutes)
        let cutoff = Utc::now() - Duration::minutes(10);
        states.retain(|_, v| v.created_at > cutoff);
        
        let auth_url = self.config.generate_auth_url(&csrf_token);
        
        Ok((auth_url, csrf_token))
    }

    pub async fn exchange_code(
        &self,
        code: String,
        state: String,
    ) -> Result<TikTokToken, String> {
        // Verify state
        let mut states = self.auth_states.lock().await;
        let _auth_state = states.remove(&state)
            .ok_or_else(|| "Invalid or expired state".to_string())?;
        
        // Build the URL with query parameters
        let url = format!(
            "{}?client_key={}&client_secret={}&code={}&grant_type=authorization_code",
            self.config.token_url,
            urlencoding::encode(&self.config.client_key),
            urlencoding::encode(&self.config.client_secret),
            urlencoding::encode(&code)
        );
        
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .header("Accept", "application/json")
            .send()
            .await
            .map_err(|e| format!("Failed to exchange code: {}", e))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Token exchange failed: {}", error_text));
        }
        
        let token_response: TokenResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse token response: {}", e))?;
        
        // Check for API errors
        if let Some(error) = token_response.data.error_code {
            return Err(format!("TikTok API error {}: {}", 
                error, 
                token_response.data.description.unwrap_or_default()
            ));
        }
        
        Ok(TikTokToken {
            access_token: token_response.data.access_token.unwrap_or_default(),
            open_id: token_response.data.open_id.unwrap_or_default(),
            refresh_token: token_response.data.refresh_token.unwrap_or_default(),
            expires_in: token_response.data.expires_in.unwrap_or(0),
            refresh_expires_in: token_response.data.refresh_expires_in.unwrap_or(0),
            scope: token_response.data.scope.unwrap_or_default(),
            expires_at: Utc::now() + Duration::seconds(token_response.data.expires_in.unwrap_or(86400)),
        })
    }

    pub async fn refresh_token(&self, refresh_token: &str) -> Result<TikTokToken, String> {
        let url = format!(
            "{}?client_key={}&refresh_token={}&grant_type=refresh_token",
            self.config.token_url,
            urlencoding::encode(&self.config.client_key),
            urlencoding::encode(refresh_token)
        );
        
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .send()
            .await
            .map_err(|e| format!("Failed to refresh token: {}", e))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_default();
            return Err(format!("Token refresh failed: {}", error_text));
        }
        
        let token_response: TokenResponse = response
            .json()
            .await
            .map_err(|e| format!("Failed to parse refresh response: {}", e))?;
        
        Ok(TikTokToken {
            access_token: token_response.data.access_token.unwrap_or_default(),
            open_id: token_response.data.open_id.unwrap_or_default(),
            refresh_token: token_response.data.refresh_token.unwrap_or_default(),
            expires_in: token_response.data.expires_in.unwrap_or(0),
            refresh_expires_in: token_response.data.refresh_expires_in.unwrap_or(0),
            scope: token_response.data.scope.unwrap_or_default(),
            expires_at: Utc::now() + Duration::seconds(token_response.data.expires_in.unwrap_or(86400)),
        })
    }
}

// TikTok's response structure is nested
#[derive(Debug, Deserialize)]
struct TokenResponse {
    data: TokenData,
}

#[derive(Debug, Deserialize)]
struct TokenData {
    access_token: Option<String>,
    open_id: Option<String>,
    refresh_token: Option<String>,
    expires_in: Option<i64>,
    refresh_expires_in: Option<i64>,
    scope: Option<String>,
    error_code: Option<i32>,
    description: Option<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_auth_url_generation() {
        let config = TikTokConfig {
            client_key: "test_key".to_string(),
            client_secret: "test_secret".to_string(),
            redirect_uri: "http://localhost:1420/callback".to_string(),
            auth_url: "https://www.tiktok.com/auth/authorize/".to_string(),
            token_url: "https://open-api.tiktok.com/oauth/access_token/".to_string(),
        };
        
        let url = config.generate_auth_url("test_state");
        
        assert!(url.contains("client_key=test_key"));
        assert!(url.contains("response_type=code"));
        assert!(url.contains("state=test_state"));
        assert!(url.contains("user.info.basic"));
    }
}