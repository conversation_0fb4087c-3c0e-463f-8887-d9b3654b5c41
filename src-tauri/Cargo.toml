[package]
name = "creator-os"
version = "1.0.0"
description = "AI Operating System for Content Creators"
authors = ["CreatorOS Team"]
license = "MIT"
repository = "https://github.com/creatoros/desktop"
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.5.0", features = ["tray-icon"] }
tauri-plugin-log = "2.0.0-rc"
tauri-plugin-dialog = "2.0.0-rc"
tauri-plugin-fs = "2.0.0-rc"
tauri-plugin-shell = "2.0.0-rc"
reqwest = { version = "0.11", features = ["stream", "json", "multipart"] }
tokio = { version = "1", features = ["full"] }
dirs = "5.0.1"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4"] }
urlencoding = "2.1"
tempfile = "3.8"
sqlx = { version = "0.7", features = ["runtime-tokio-native-tls", "sqlite", "chrono"] }
lazy_static = "1.4"
sha2 = "0.10"
hex = "0.4"
base64 = "0.21"
rand = "0.8"
url = "2.5"
futures-util = "0.3"
semver = "1.0"
get_if_addrs = "0.5"
sysinfo = "0.29"
tokio-util = { version = "0.7", features = ["io"] }
futures = "0.3"
anyhow = "1.0"
# For local AI processing
whisper-rs = { version = "0.11", optional = true }
image = "0.24"
imageproc = "0.23"
sentry = { version = "0.35", features = ["anyhow", "debug-images", "log", "panic", "reqwest", "rustls"] }
