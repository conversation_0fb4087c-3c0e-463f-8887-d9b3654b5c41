// Test script to verify local AI processing works
const { execSync } = require('child_process');
const path = require('path');

console.log('Testing Local AI Processing...\n');

// Test 1: Check FFmpeg availability
try {
  const ffmpegVersion = execSync('ffmpeg -version').toString().split('\n')[0];
  console.log('✅ FFmpeg is available:', ffmpegVersion);
} catch (error) {
  console.error('❌ FFmpeg not found. Please install FFmpeg for local processing.');
  process.exit(1);
}

// Test 2: Check FFprobe availability
try {
  const ffprobeVersion = execSync('ffprobe -version').toString().split('\n')[0];
  console.log('✅ FFprobe is available:', ffprobeVersion);
} catch (error) {
  console.error('❌ FFprobe not found. Please install FFmpeg for local processing.');
  process.exit(1);
}

// Test 3: Test basic media analysis (if test file exists)
const testVideoPath = path.join(__dirname, 'test-video.mp4');
console.log('\n🎬 To test media analysis, place a video file at:', testVideoPath);

console.log('\n✨ Local AI processing is ready!');
console.log('The app will use FFmpeg for:');
console.log('  - Scene detection');
console.log('  - Audio level analysis');
console.log('  - Video metadata extraction');
console.log('  - Thumbnail generation');
console.log('\nNo API keys needed for free tier! 🎉');