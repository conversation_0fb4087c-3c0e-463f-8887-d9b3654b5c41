src/ai/__tests__/SmartEditor.test.ts(3,27): error TS6133: 'EditSuggestion' is declared but its value is never read.
src/ai/ContentAnalyzer.ts(13,3): error TS6133: 'JobStatus' is declared but its value is never read.
src/ai/gateway/UnifiedAIGateway.ts(6,73): error TS6133: 'GenerationType' is declared but its value is never read.
src/ai/gateway/UnifiedAIGateway.ts(296,28): error TS6133: 'capability' is declared but its value is never read.
src/ai/PredictionEngine.ts(6,3): error TS6133: 'PlatformOptimization' is declared but its value is never read.
src/ai/PredictionEngine.ts(530,5): error TS6133: 'analysis' is declared but its value is never read.
src/ai/PredictionEngine.ts(633,5): error TS6133: 'analysis' is declared but its value is never read.
src/ai/PredictionEngine.ts(660,31): error TS6133: 'insights' is declared but its value is never read.
src/ai/PredictionEngine.ts(660,46): error TS6133: 'category' is declared but its value is never read.
src/ai/PredictionEngine.ts(669,33): error TS6133: 'insights' is declared but its value is never read.
src/ai/PredictionEngine.ts(669,48): error TS6133: 'profile' is declared but its value is never read.
src/ai/PredictionEngine.ts(707,58): error TS6133: 'profile' is declared but its value is never read.
src/ai/providers/FluxProvider.ts(14,29): error TS6133: 'getModelConfig' is declared but its value is never read.
src/ai/providers/FluxProvider.ts(163,11): error TS6133: 'calculateCostOld' is declared but its value is never read.
src/ai/providers/OpenAIRealtimeProvider.ts(19,11): error TS6133: 'sessionId' is declared but its value is never read.
src/ai/providers/OpenAIRealtimeProvider.ts(57,5): error TS6133: 'options' is declared but its value is never read.
src/ai/providers/ReplicateProvider.ts(83,11): error TS6133: 'baseInput' is declared but its value is never read.
src/ai/providers/ReplicateProvider.ts(165,27): error TS6133: 'output' is declared but its value is never read.
src/ai/providers/ReplicateProvider.ts(183,30): error TS6133: 'model' is declared but its value is never read.
src/ai/providers/ReplicateProvider.ts(326,5): error TS6133: 'modelPath' is declared but its value is never read.
src/ai/providers/ReplicateProvider.ts(327,5): error TS6133: 'hardware' is declared but its value is never read.
src/ai/SmartEditor.ts(5,3): error TS6133: 'EditType' is declared but its value is never read.
src/ai/SmartEditor.ts(6,3): error TS6133: 'EditParameters' is declared but its value is never read.
src/ai/SmartEditor.ts(7,3): error TS6133: 'CaptionStyle' is declared but its value is never read.
src/ai/SmartEditor.ts(9,3): error TS6133: 'JobStatus' is declared but its value is never read.
src/ai/SmartEditor.ts(69,11): error TS6133: 'editHistory' is declared but its value is never read.
src/ai/SmartEditor.ts(581,5): error TS6133: 'analysis' is declared but its value is never read.
src/ai/SmartEditor.ts(729,57): error TS6133: 'index' is declared but its value is never read.
src/App.tsx(14,1): error TS6133: 'SystemStatus' is declared but its value is never read.
src/App.tsx(15,1): error TS6133: 'QuickActions' is declared but its value is never read.
src/components/ai/__tests__/AIInsightsDashboard.test.tsx(1,1): error TS6133: 'React' is declared but its value is never read.
src/components/ai/__tests__/SmartEditPanel.test.tsx(1,1): error TS6133: 'React' is declared but its value is never read.
src/components/ai/AIGenerationPanel.tsx(48,10): error TS6133: 'selectedProvider' is declared but its value is never read.
src/components/ai/AIGenerationPanel.tsx(48,28): error TS6133: 'setSelectedProvider' is declared but its value is never read.
src/components/ai/AIInsightsDashboard.tsx(12,3): error TS6133: 'CheckCircle' is declared but its value is never read.
src/components/ai/AIInsightsDashboard.tsx(14,3): error TS6133: 'ChevronRight' is declared but its value is never read.
src/components/ai/AIInsightsDashboard.tsx(19,10): error TS6133: 'colors' is declared but its value is never read.
src/components/ai/AIInsightsDashboard.tsx(67,9): error TS6133: 'toggleSection' is declared but its value is never read.
src/components/ai/SmartEditPanel.tsx(1,38): error TS6133: 'useCallback' is declared but its value is never read.
src/components/ai/SmartEditPanel.tsx(2,18): error TS6133: 'AnimatePresence' is declared but its value is never read.
src/components/ai/SmartEditPanel.tsx(9,3): error TS6133: 'Clock' is declared but its value is never read.
src/components/ai/SmartEditPanel.tsx(17,3): error TS6133: 'Settings' is declared but its value is never read.
src/components/AISettings.tsx(2,57): error TS6133: 'X' is declared but its value is never read.
src/components/AISettings.tsx(222,27): error TS6133: 'tier' is declared but its value is never read.
src/components/AIStudio.tsx(2,70): error TS6133: 'Settings' is declared but its value is never read.
src/components/AIStudio.tsx(4,1): error TS6133: 'convertFileSrc' is declared but its value is never read.
src/components/Analytics.tsx(2,53): error TS6133: 'Calendar' is declared but its value is never read.
src/components/Analytics.tsx(2,63): error TS6133: 'Globe' is declared but its value is never read.
src/components/Analytics.tsx(55,28): error TS6133: 'setSelectedPlatform' is declared but its value is never read.
src/components/Analytics.tsx(71,10): error TS6133: 'isLoading' is declared but its value is never read.
src/components/Dashboard.tsx(2,40): error TS6133: 'Users' is declared but its value is never read.
src/components/Dashboard.tsx(2,62): error TS6133: 'AlertCircle' is declared but its value is never read.
src/components/Dashboard.tsx(2,75): error TS6133: 'CheckCircle' is declared but its value is never read.
src/components/Dashboard.tsx(5,10): error TS6133: 'colors' is declared but its value is never read.
src/components/Dashboard.tsx(102,9): error TS6133: 'formatNumber' is declared but its value is never read.
src/components/Dashboard.tsx(111,9): error TS6133: 'StatCard' is declared but its value is never read.
src/components/DeviceOptimization.tsx(4,10): error TS6133: 'colors' is declared but its value is never read.
src/components/Header.tsx(5,25): error TS6133: 'TabType' is declared but its value is never read.
src/components/HeroMetrics.tsx(4,1): error TS6133: 'AnimatedButton' is declared but its value is never read.
src/components/MainContent.tsx(10,10): error TS6133: 'colors' is declared but its value is never read.
src/components/MainTabs.tsx(6,1): error TS6133: 'UploadHistory' is declared but its value is never read.
src/components/sidebar/ContentTypeSidebar.tsx(4,10): error TS6133: 'colors' is declared but its value is never read.
src/components/sidebar/RecentActivitySidebar.tsx(5,10): error TS6133: 'colors' is declared but its value is never read.
src/components/ui/GlassCard.tsx(2,18): error TS6133: 'utils' is declared but its value is never read.
src/components/UploadManager/OptimizationPresets.tsx(3,24): error TS6133: 'Loader' is declared but its value is never read.
src/components/UploadManager/OptimizationPresets.tsx(162,9): error TS6133: 'sectionClass' is declared but its value is never read.
src/components/UploadManager/OptimizationPresets.tsx(163,9): error TS6133: 'selectClass' is declared but its value is never read.
src/components/UploadManager/PlatformSelector.tsx(4,10): error TS6133: 'colors' is declared but its value is never read.
src/components/UploadManager/UploadForm.tsx(5,10): error TS6133: 'colors' is declared but its value is never read.
src/components/YtDlpDiagnostic.tsx(5,10): error TS6133: 'colors' is declared but its value is never read.
