name: Release FlowDownload

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

permissions:
  contents: write

jobs:
  create-release:
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create-release.outputs.id }}
      upload_url: ${{ steps.create-release.outputs.upload_url }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Create Release
        id: create-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: FlowDownload ${{ github.ref_name }}
          draft: true
          prerelease: false
          body: |
            # FlowDownload ${{ github.ref_name }}
            
            ## What's New
            - Enhanced AI-powered content optimization
            - Improved download performance
            - New platform integrations
            
            ## Downloads
            See assets below for your platform.
            
            ## Verification
            All releases are signed. Verify with:
            ```bash
            minisign -Vm FlowDownload.app.tar.gz -P RWRfRFYbJq061QKbzRSkw8cf/y6L8XzfpLUxtKABxPz9PBxLxiOmgpi
            ```

  build-tauri:
    needs: create-release
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest'
            args: '--target x86_64-apple-darwin'
            name: 'FlowDownload_x64.app.tar.gz'
          - platform: 'macos-latest'
            args: '--target aarch64-apple-darwin'
            name: 'FlowDownload_aarch64.app.tar.gz'
          - platform: 'ubuntu-20.04'
            args: ''
            name: 'FlowDownload_amd64.AppImage'
          - platform: 'windows-latest'
            args: ''
            name: 'FlowDownload_x64-setup.exe'

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: Install dependencies (Ubuntu)
        if: matrix.platform == 'ubuntu-20.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libayatana-appindicator3-dev librsvg2-dev

      - name: Install minisign
        run: |
          if [[ "${{ matrix.platform }}" == "ubuntu-20.04" ]]; then
            sudo apt-get install -y minisign
          elif [[ "${{ matrix.platform }}" == "macos-latest" ]]; then
            brew install minisign
          elif [[ "${{ matrix.platform }}" == "windows-latest" ]]; then
            choco install minisign
          fi

      - name: Install frontend dependencies
        run: npm install

      - name: Build Tauri app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}
        with:
          tagName: ${{ github.ref_name }}
          releaseName: 'FlowDownload ${{ github.ref_name }}'
          releaseBody: 'See the release notes for details'
          releaseDraft: true
          prerelease: false
          args: ${{ matrix.args }}

      - name: Prepare release asset (macOS)
        if: matrix.platform == 'macos-latest'
        run: |
          cd src-tauri/target/release/bundle/macos
          tar -czf ../../../../../${{ matrix.name }} *.app
          cd ../../../../../
          minisign -Sm ${{ matrix.name }} -s <(echo "${{ secrets.MINISIGN_PRIVATE_KEY }}")

      - name: Prepare release asset (Linux)
        if: matrix.platform == 'ubuntu-20.04'
        run: |
          cp src-tauri/target/release/bundle/appimage/*.AppImage ${{ matrix.name }}
          minisign -Sm ${{ matrix.name }} -s <(echo "${{ secrets.MINISIGN_PRIVATE_KEY }}")

      - name: Prepare release asset (Windows)
        if: matrix.platform == 'windows-latest'
        run: |
          Copy-Item "src-tauri\target\release\bundle\nsis\*.exe" -Destination "${{ matrix.name }}"
          echo "${{ secrets.MINISIGN_PRIVATE_KEY }}" | minisign -Sm ${{ matrix.name }}

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./${{ matrix.name }}
          asset_name: ${{ matrix.name }}
          asset_content_type: application/octet-stream

      - name: Upload Signature
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./${{ matrix.name }}.minisig
          asset_name: ${{ matrix.name }}.minisig
          asset_content_type: text/plain

  publish-release:
    needs: [create-release, build-tauri]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Publish Release
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ needs.create-release.outputs.release_id }},
              draft: false
            })

      - name: Update latest release manifest
        run: |
          echo "Updating release manifest..."
          # This would update your update server with the new release info