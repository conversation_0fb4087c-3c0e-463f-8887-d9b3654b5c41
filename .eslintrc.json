{"root": true, "env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react/jsx-runtime", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks", "react-refresh", "@typescript-eslint"], "rules": {"react-refresh/only-export-components": "warn", "@typescript-eslint/no-explicit-any": "warn", "react/prop-types": "off"}, "settings": {"react": {"version": "detect"}}}