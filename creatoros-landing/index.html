<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="CreatorOS - The AI-powered desktop app for content creators. Download, edit, optimize, and publish to all platforms from one powerful application.">
    <meta name="keywords" content="content creation, creator tools, youtube downloader, tiktok uploader, ai video editor, desktop app">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://creatoros.app/">
    <meta property="og:title" content="CreatorOS - AI-Powered Creator Desktop App">
    <meta property="og:description" content="Create content 10x faster with AI. Download, edit, and publish to all platforms from one powerful desktop app.">
    <meta property="og:image" content="assets/images/placeholder-hero.svg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://creatoros.app/">
    <meta property="twitter:title" content="CreatorOS - AI-Powered Creator Desktop App">
    <meta property="twitter:description" content="Create content 10x faster with AI. Download, edit, and publish to all platforms from one powerful desktop app.">
    <meta property="twitter:image" content="assets/images/placeholder-hero.svg">

    <title>CreatorOS - AI-Powered Desktop App for Content Creators</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/ai-brain.svg">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS v4 -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
                        'display': ['Space Grotesk', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#EEF2FF',
                            100: '#E0E7FF',
                            200: '#C7D2FE',
                            300: '#A5B4FC',
                            400: '#818CF8',
                            500: '#6366F1',
                            600: '#4F46E5',
                            700: '#4338CA',
                            800: '#3730A3',
                            900: '#312E81',
                        },
                        dark: {
                            50: '#18181B',
                            100: '#0F0F14',
                            200: '#1A1A23',
                            300: '#252533',
                            400: '#2A2A3A',
                            500: '#30303F',
                        }
                    },
                    backgroundImage: {
                        'gradient-primary': 'linear-gradient(135deg, #6B46C1 0%, #3B82F6 100%)',
                        'gradient-secondary': 'linear-gradient(135deg, #F093FB 0%, #F5576C 100%)',
                        'gradient-accent': 'linear-gradient(135deg, #4FACFE 0%, #00F2FE 100%)',
                        'gradient-mesh': `radial-gradient(at 0% 0%, hsla(253,16%,7%,1) 0, transparent 50%),
                                         radial-gradient(at 50% 0%, hsla(225,39%,30%,1) 0, transparent 50%),
                                         radial-gradient(at 100% 0%, hsla(339,49%,30%,1) 0, transparent 50%)`,
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'shimmer': 'shimmer 2s linear infinite',
                        'glow-pulse': 'glow-pulse 2s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        shimmer: {
                            '0%': { backgroundPosition: '-200% center' },
                            '100%': { backgroundPosition: '200% center' },
                        },
                        'glow-pulse': {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(139, 92, 246, 0.5)' },
                            '50%': { boxShadow: '0 0 40px rgba(139, 92, 246, 0.8)' },
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        /* Glass morphism effect */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .glass-strong {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Gradient text */
        .gradient-text {
            background: linear-gradient(135deg, #6B46C1 0%, #3B82F6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1A1A23;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom Fixes CSS -->
    <link rel="stylesheet" href="assets/css/fixes.css">
    <link rel="stylesheet" href="assets/css/testimonial-fix.css">
</head>
<body class="bg-dark-100 text-white font-sans antialiased">
    <!-- Background Gradient -->
    <div class="fixed inset-0 bg-gradient-mesh opacity-40 pointer-events-none"></div>

    <!-- Navigation -->
    <nav class="sticky top-0 z-50 glass border-b border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <a href="#" class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-xl">C</span>
                    </div>
                    <span class="font-display font-bold text-xl">CreatorOS</span>
                </a>
                
                <!-- Desktop Nav -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features" class="text-gray-300 hover:text-white transition">Features</a>
                    <a href="#platforms" class="text-gray-300 hover:text-white transition">Platforms</a>
                    <a href="#testimonials" class="text-gray-300 hover:text-white transition">Testimonials</a>
                    <a href="#pricing" class="text-gray-300 hover:text-white transition">Pricing</a>
                    <a href="#download" class="bg-gradient-primary text-white px-6 py-2 rounded-lg font-medium hover:opacity-90 transition">
                        Download Free
                    </a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden w-10 h-10 flex flex-col items-center justify-center space-y-1.5" aria-label="Toggle mobile menu">
                    <span class="w-6 h-0.5 bg-white transition-all"></span>
                    <span class="w-6 h-0.5 bg-white transition-all"></span>
                    <span class="w-6 h-0.5 bg-white transition-all"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden glass border-t border-white/10">
            <div class="px-4 py-6 space-y-4">
                <a href="#features" class="block text-gray-300 hover:text-white transition py-2" data-mobile-link>Features</a>
                <a href="#platforms" class="block text-gray-300 hover:text-white transition py-2" data-mobile-link>Platforms</a>
                <a href="#testimonials" class="block text-gray-300 hover:text-white transition py-2" data-mobile-link>Testimonials</a>
                <a href="#pricing" class="block text-gray-300 hover:text-white transition py-2" data-mobile-link>Pricing</a>
                <a href="#download" class="block bg-gradient-primary text-white px-6 py-3 rounded-lg font-medium text-center hover:opacity-90 transition" data-mobile-link>
                    Download Free
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center pt-16 pb-24 overflow-hidden">
        <!-- Floating Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-float"></div>
            <div class="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-float" style="animation-delay: 2s"></div>
            <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-pink-500/20 rounded-full blur-3xl animate-float" style="animation-delay: 4s"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Hero Text -->
                <div class="text-center lg:text-left">
                    <div class="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6" data-aos="fade-down">
                        <span class="text-2xl">🚀</span>
                        <span class="text-sm font-medium">10,000+ Creators Already Using CreatorOS</span>
                    </div>
                    
                    <h1 class="text-5xl md:text-6xl lg:text-7xl font-display font-bold leading-tight mb-6" data-aos="fade-up" data-aos-delay="100">
                        Create Content <span class="gradient-text">10x Faster</span> with AI
                    </h1>
                    
                    <p class="text-xl text-gray-300 mb-8 leading-relaxed" data-aos="fade-up" data-aos-delay="200">
                        The desktop app that handles everything from download to viral upload. 
                        Replace your entire creator toolkit with one powerful application.
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 mb-8" data-aos="fade-up" data-aos-delay="300">
                        <button class="group bg-gradient-primary text-white px-8 py-4 rounded-lg font-medium text-lg hover:opacity-90 transition flex items-center justify-center space-x-3" data-download="windows" data-download-primary>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            <div class="text-left">
                                <div>Download for Windows</div>
                                <div class="text-sm opacity-80">Free • v1.0.0 • 45MB</div>
                            </div>
                        </button>
                        
                        <button class="glass hover:bg-white/20 text-white px-8 py-4 rounded-lg font-medium text-lg transition flex items-center justify-center space-x-2" data-demo-video>
                            <span class="text-2xl">▶</span>
                            <span>Watch 2-min Demo</span>
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-6 text-sm text-gray-400" data-aos="fade-up" data-aos-delay="400">
                        <span>Also available for:</span>
                        <a href="#download" class="flex items-center space-x-2 hover:text-white transition">
                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                            </svg>
                            <span>macOS</span>
                        </a>
                        <a href="#download" class="flex items-center space-x-2 hover:text-white transition">
                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2L2 7v10c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V7l-10-5z"/>
                            </svg>
                            <span>Linux</span>
                        </a>
                    </div>
                </div>
                
                <!-- Hero Visual -->
                <div class="relative" data-aos="zoom-in" data-aos-delay="200">
                    <div class="relative mx-auto max-w-lg lg:max-w-none">
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl blur-3xl opacity-20 animate-glow-pulse"></div>
                        <img src="assets/images/screenshot-workflow.svg" alt="CreatorOS App Interface" class="relative rounded-2xl shadow-2xl border border-white/10">
                        
                        <!-- Feature Badges -->
                        <div class="absolute -top-4 -right-4 glass px-4 py-2 rounded-lg flex items-center space-x-2 animate-float">
                            <span class="text-2xl">✨</span>
                            <span class="font-medium">AI-Powered</span>
                        </div>
                        
                        <div class="absolute -bottom-4 -left-4 glass px-4 py-2 rounded-lg flex items-center space-x-2 animate-float" style="animation-delay: 1s">
                            <span class="text-2xl">⚡</span>
                            <span class="font-medium">10x Faster</span>
                        </div>
                        
                        <div class="absolute top-1/2 -right-8 glass px-4 py-2 rounded-lg flex items-center space-x-2 animate-float" style="animation-delay: 2s">
                            <span class="text-2xl">🎯</span>
                            <span class="font-medium">All Platforms</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Bar -->
    <section class="py-12 border-y border-white/10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div data-aos="fade-up">
                    <div class="text-4xl font-bold gradient-text" data-count="10000">0</div>
                    <div class="text-gray-400 mt-1">Active Creators</div>
                </div>
                <div data-aos="fade-up" data-aos-delay="100">
                    <div class="text-4xl font-bold gradient-text" data-count="2000000">0</div>
                    <div class="text-gray-400 mt-1">Videos Created</div>
                </div>
                <div data-aos="fade-up" data-aos-delay="200">
                    <div class="text-4xl font-bold gradient-text">4.9★</div>
                    <div class="text-gray-400 mt-1">Average Rating</div>
                </div>
                <div data-aos="fade-up" data-aos-delay="300">
                    <div class="text-4xl font-bold gradient-text" data-count="500">0</div>
                    <div class="text-gray-400 mt-1">Hours Saved Daily</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem/Solution Section -->
    <section class="py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    Stop Juggling <span class="gradient-text">7+ Different Tools</span>
                </h2>
                <p class="text-xl text-gray-300" data-aos="fade-up" data-aos-delay="100">
                    CreatorOS replaces your entire toolkit with one powerful desktop app
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                <!-- Before Card -->
                <div class="glass rounded-2xl p-8 border border-red-500/20" data-aos="fade-right">
                    <div class="flex items-center justify-between mb-6">
                        <span class="text-red-400 font-medium">Before CreatorOS</span>
                        <span class="text-3xl">😫</span>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">YouTube downloader</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">Video editor</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">Thumbnail creator</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">Upload scheduler</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">Analytics tracker</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">SEO optimizer</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-red-500">✗</span>
                            <span class="text-gray-300">Social media manager</span>
                        </li>
                    </ul>
                    <div class="border-t border-white/10 pt-6">
                        <div class="flex justify-between items-center">
                            <span class="text-red-400 text-2xl font-bold">$200+/month</span>
                            <span class="text-gray-400 text-sm">10+ hours/week managing</span>
                        </div>
                    </div>
                </div>
                
                <!-- After Card -->
                <div class="glass-strong rounded-2xl p-8 border border-green-500/20" data-aos="fade-left">
                    <div class="flex items-center justify-between mb-6">
                        <span class="text-green-400 font-medium">With CreatorOS</span>
                        <span class="text-3xl">🚀</span>
                    </div>
                    <ul class="space-y-3 mb-8">
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">All-in-one desktop app</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">AI-powered automation</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">One-click publishing</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">Smart scheduling</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">Real-time analytics</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">Auto-optimization</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span class="text-gray-300">Unified dashboard</span>
                        </li>
                    </ul>
                    <div class="border-t border-white/10 pt-6">
                        <div class="flex justify-between items-center">
                            <span class="text-green-400 text-2xl font-bold">$19.99/month</span>
                            <span class="text-gray-400 text-sm">Save 10+ hours/week</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-24" id="features">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    Everything You Need to <span class="gradient-text">Create Like a Pro</span>
                </h2>
                <p class="text-xl text-gray-300" data-aos="fade-up" data-aos-delay="100">
                    Powerful features designed for modern content creators
                </p>
            </div>
            
            <!-- Main Features Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <!-- AI Intelligence Feature -->
                <div class="glass rounded-2xl p-8 border border-purple-500/20 md:col-span-2 lg:col-span-1" data-aos="zoom-in">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
                        <span class="text-3xl">🧠</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">AI Content Intelligence</h3>
                    <p class="text-gray-300 mb-6">
                        Predict viral potential, optimize for each platform, and get AI-powered 
                        suggestions to make your content stand out.
                    </p>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-center space-x-2">
                            <span class="text-purple-400">•</span>
                            <span>Viral probability score</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span class="text-purple-400">•</span>
                            <span>Best time to post analysis</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span class="text-purple-400">•</span>
                            <span>Hashtag recommendations</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span class="text-purple-400">•</span>
                            <span>Thumbnail A/B testing</span>
                        </li>
                    </ul>
                </div>
                
                <!-- Multi-Platform Feature -->
                <div class="glass rounded-2xl p-8 border border-blue-500/20" data-aos="zoom-in" data-aos-delay="100">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-6">
                        <span class="text-3xl">🌐</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">All Platforms, One Click</h3>
                    <p class="text-gray-300 mb-6">
                        Upload to YouTube, TikTok, Instagram, and 50+ platforms simultaneously 
                        with platform-specific optimization.
                    </p>
                    <div class="grid grid-cols-4 gap-2">
                        <div class="bg-white/5 rounded-lg p-2 flex items-center justify-center">
                            <span class="text-red-500 text-xl">▶</span>
                        </div>
                        <div class="bg-white/5 rounded-lg p-2 flex items-center justify-center">
                            <span class="text-2xl">🎵</span>
                        </div>
                        <div class="bg-white/5 rounded-lg p-2 flex items-center justify-center">
                            <span class="text-2xl">📷</span>
                        </div>
                        <div class="bg-white/5 rounded-lg p-2 flex items-center justify-center">
                            <span class="text-blue-400 text-xl">𝕏</span>
                        </div>
                        <div class="col-span-4 bg-white/5 rounded-lg p-2 text-center text-gray-400 text-sm">
                            +50 more platforms
                        </div>
                    </div>
                </div>
                
                <!-- Speed Feature -->
                <div class="glass rounded-2xl p-8 border border-green-500/20" data-aos="zoom-in" data-aos-delay="200">
                    <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mb-6">
                        <span class="text-3xl">⚡</span>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Lightning Fast Downloads</h3>
                    <p class="text-gray-300 mb-6">
                        Download from any website at maximum speed with our Rust-powered engine. 
                        Resume interrupted downloads anytime.
                    </p>
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-400">Web Tools</span>
                                <span class="text-gray-400">1.2 MB/s</span>
                            </div>
                            <div class="h-2 bg-white/10 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-red-500 to-orange-500 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-gray-300 font-medium">CreatorOS</span>
                                <span class="text-green-400 font-medium">9.5 MB/s</span>
                            </div>
                            <div class="h-2 bg-white/10 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Privacy Feature -->
            <div class="glass rounded-2xl p-8 border border-amber-500/20 mb-16" data-aos="zoom-in" data-aos-delay="300">
                <div class="grid md:grid-cols-2 gap-8 items-center">
                    <div>
                        <div class="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-xl flex items-center justify-center mb-6">
                            <span class="text-3xl">🔒</span>
                        </div>
                        <h3 class="text-2xl font-bold mb-4">Your Data, Your Control</h3>
                        <p class="text-gray-300 mb-6">
                            Everything runs on your computer. No cloud uploads, no data mining. 
                            Work offline and keep your content secure.
                        </p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="glass rounded-lg p-4 text-center">
                            <span class="text-3xl mb-2 block">🔒</span>
                            <span class="text-sm text-gray-300">Local Processing</span>
                        </div>
                        <div class="glass rounded-lg p-4 text-center">
                            <span class="text-3xl mb-2 block">🛡️</span>
                            <span class="text-sm text-gray-300">No Cloud Storage</span>
                        </div>
                        <div class="glass rounded-lg p-4 text-center">
                            <span class="text-3xl mb-2 block">✓</span>
                            <span class="text-sm text-gray-300">GDPR Compliant</span>
                        </div>
                        <div class="glass rounded-lg p-4 text-center">
                            <span class="text-3xl mb-2 block">🔐</span>
                            <span class="text-sm text-gray-300">Encrypted</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- More Features -->
            <div data-aos="fade-up">
                <h3 class="text-2xl font-bold text-center mb-8">Plus Everything Else You Need</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="100">
                        <span class="text-2xl mb-2 block">✏️</span>
                        <span class="text-sm text-gray-300">Smart Editing</span>
                    </div>
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="150">
                        <span class="text-2xl mb-2 block">📅</span>
                        <span class="text-sm text-gray-300">Scheduling</span>
                    </div>
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="200">
                        <span class="text-2xl mb-2 block">📊</span>
                        <span class="text-sm text-gray-300">Analytics</span>
                    </div>
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="250">
                        <span class="text-2xl mb-2 block">👥</span>
                        <span class="text-sm text-gray-300">Team Collab</span>
                    </div>
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="300">
                        <span class="text-2xl mb-2 block">🔌</span>
                        <span class="text-sm text-gray-300">API Access</span>
                    </div>
                    <div class="glass rounded-lg p-4 text-center" data-aos="fade-up" data-aos-delay="350">
                        <span class="text-2xl mb-2 block">⌨️</span>
                        <span class="text-sm text-gray-300">Shortcuts</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platforms Section -->
    <section class="py-24 relative overflow-hidden" id="platforms">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    From Idea to Viral in <span class="gradient-text">5 Simple Steps</span>
                </h2>
                <p class="text-xl text-gray-300" data-aos="fade-up" data-aos-delay="100">
                    See how CreatorOS streamlines your entire content creation workflow
                </p>
            </div>
            
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 via-blue-500 to-green-500 hidden md:block"></div>
                
                <!-- Step 1 -->
                <div class="relative flex items-center mb-16" data-aos="fade-right">
                    <div class="flex-1 md:pr-16 md:text-right">
                        <div class="glass rounded-2xl p-6 inline-block">
                            <h3 class="text-xl font-bold mb-2">Find & Download</h3>
                            <p class="text-gray-300">Download any content from any website with one click. Support for 1000+ platforms.</p>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-16 h-16 -ml-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-2xl font-bold z-10">
                        1
                    </div>
                    <div class="flex-1 md:pl-16 hidden md:block"></div>
                </div>
                
                <!-- Step 2 -->
                <div class="relative flex items-center mb-16" data-aos="fade-left">
                    <div class="flex-1 md:pr-16 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-16 h-16 -ml-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-2xl font-bold z-10">
                        2
                    </div>
                    <div class="flex-1 md:pl-16">
                        <div class="glass rounded-2xl p-6 inline-block">
                            <h3 class="text-xl font-bold mb-2">AI Optimization</h3>
                            <p class="text-gray-300">Let AI analyze and optimize your content for maximum engagement on each platform.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3 -->
                <div class="relative flex items-center mb-16" data-aos="fade-right">
                    <div class="flex-1 md:pr-16 md:text-right">
                        <div class="glass rounded-2xl p-6 inline-block">
                            <h3 class="text-xl font-bold mb-2">Edit & Enhance</h3>
                            <p class="text-gray-300">Professional editing tools at your fingertips. Trim, crop, add effects, and more.</p>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-16 h-16 -ml-8 bg-gradient-to-r from-cyan-500 to-green-500 rounded-full flex items-center justify-center text-2xl font-bold z-10">
                        3
                    </div>
                    <div class="flex-1 md:pl-16 hidden md:block"></div>
                </div>
                
                <!-- Step 4 -->
                <div class="relative flex items-center mb-16" data-aos="fade-left">
                    <div class="flex-1 md:pr-16 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-16 h-16 -ml-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-2xl font-bold z-10">
                        4
                    </div>
                    <div class="flex-1 md:pl-16">
                        <div class="glass rounded-2xl p-6 inline-block">
                            <h3 class="text-xl font-bold mb-2">Publish Everywhere</h3>
                            <p class="text-gray-300">One-click publishing to all your platforms with optimal settings for each.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Step 5 -->
                <div class="relative flex items-center" data-aos="fade-right">
                    <div class="flex-1 md:pr-16 md:text-right">
                        <div class="glass rounded-2xl p-6 inline-block">
                            <h3 class="text-xl font-bold mb-2">Track & Grow</h3>
                            <p class="text-gray-300">Monitor performance across all platforms and get AI insights to improve.</p>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-16 h-16 -ml-8 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center text-2xl font-bold z-10">
                        5
                    </div>
                    <div class="flex-1 md:pl-16 hidden md:block"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-24" id="testimonials">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    Loved by <span class="gradient-text">10,000+ Creators</span>
                </h2>
                <p class="text-xl text-gray-300" data-aos="fade-up" data-aos-delay="100">
                    See what successful creators are saying about CreatorOS
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <!-- Video Testimonial -->
                <div class="lg:col-span-2" data-aos="zoom-in">
                    <div class="glass rounded-2xl overflow-hidden h-full flex flex-col">
                        <div class="relative" style="padding-bottom: 56.25%;">
                            <img src="assets/images/placeholder-hero.svg" alt="Video Testimonial" class="absolute inset-0 w-full h-full object-cover">
                            <button class="absolute inset-0 flex items-center justify-center group" data-testimonial-video="sarah-chen" aria-label="Play testimonial video">
                                <div class="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:scale-110 transition">
                                    <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                </div>
                            </button>
                        </div>
                        <div class="p-8 flex-1 flex flex-col justify-between">
                            <p class="text-lg mb-6 leading-relaxed text-gray-100">
                                "CreatorOS cut my workflow time by 80%. What used to take me 5 hours now takes just 1 hour. Game changer!"
                            </p>
                            <div class="flex items-center space-x-4">
                                <img src="assets/images/creator-avatar-1.svg" alt="Sarah Chen" class="w-12 h-12 rounded-full flex-shrink-0">
                                <div>
                                    <h4 class="font-bold text-white">Sarah Chen</h4>
                                    <p class="text-gray-400 text-sm">2.3M YouTube Subscribers</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Text Testimonial 1 -->
                <div class="glass rounded-2xl p-8 flex flex-col h-full" data-aos="zoom-in" data-aos-delay="100">
                    <div class="text-yellow-400 text-xl mb-4">★★★★★</div>
                    <p class="mb-6 flex-1 text-gray-100">
                        "The AI predictions are scary accurate. It told me my video would go viral, and it hit 5M views in 3 days!"
                    </p>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <img src="assets/images/creator-avatar-2.svg" alt="Mike Johnson" class="w-12 h-12 rounded-full flex-shrink-0">
                            <div>
                                <h4 class="font-bold text-white">Mike Johnson</h4>
                                <p class="text-gray-400 text-sm">850K TikTok Followers</p>
                            </div>
                        </div>
                        <div class="glass rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold gradient-text">10x</div>
                            <div class="text-gray-400 text-sm">Growth in 6 months</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Success Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="glass rounded-2xl p-8 text-center" data-aos="fade-up">
                    <div class="text-4xl mb-4">📈</div>
                    <div class="text-3xl font-bold gradient-text mb-2">327%</div>
                    <div class="text-gray-400">Average growth increase</div>
                </div>
                <div class="glass rounded-2xl p-8 text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-4xl mb-4">⏱️</div>
                    <div class="text-3xl font-bold gradient-text mb-2">10hrs</div>
                    <div class="text-gray-400">Saved per week</div>
                </div>
                <div class="glass rounded-2xl p-8 text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-4xl mb-4">💰</div>
                    <div class="text-3xl font-bold gradient-text mb-2">$2,400</div>
                    <div class="text-gray-400">Saved annually on tools</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-24" id="pricing">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    Choose Your <span class="gradient-text">Creator Plan</span>
                </h2>
                <p class="text-xl text-gray-300" data-aos="fade-up" data-aos-delay="100">
                    Start free, upgrade when you're ready. Cancel anytime.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <!-- Free Plan -->
                <div class="glass rounded-2xl p-8" data-aos="zoom-in">
                    <h3 class="text-2xl font-bold mb-2">Free</h3>
                    <div class="flex items-baseline mb-4">
                        <span class="text-4xl font-bold" data-price="starter">$0</span>
                        <span class="text-gray-400 ml-2">/forever</span>
                    </div>
                    <p class="text-gray-300 mb-8">Perfect for getting started</p>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>50 downloads/month</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>5 uploads/month</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Basic editing tools</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>480p exports</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Community support</span>
                        </li>
                        <li class="flex items-center space-x-3 opacity-50">
                            <span class="text-gray-500">✗</span>
                            <span>AI predictions</span>
                        </li>
                        <li class="flex items-center space-x-3 opacity-50">
                            <span class="text-gray-500">✗</span>
                            <span>Priority processing</span>
                        </li>
                    </ul>
                    
                    <button class="w-full glass hover:bg-white/20 text-white py-3 rounded-lg font-medium transition" data-cta="start-free">
                        Start Free
                    </button>
                </div>
                
                <!-- Creator Plan (Popular) -->
                <div class="glass-strong rounded-2xl p-8 relative transform md:scale-105" data-aos="zoom-in" data-aos-delay="100">
                    <div class="absolute -top-4 left-1/2 -translate-x-1/2 bg-gradient-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                        Most Popular
                    </div>
                    <h3 class="text-2xl font-bold mb-2">Creator</h3>
                    <div class="flex items-baseline mb-4">
                        <span class="text-4xl font-bold" data-price="creator">$19.99</span>
                        <span class="text-gray-400 ml-2">/month</span>
                    </div>
                    <p class="text-gray-300 mb-8">For serious content creators</p>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Unlimited downloads</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>100 uploads/month</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Pro editing suite</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>4K exports</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>AI predictions (100/mo)</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Priority support</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Auto-scheduling</span>
                        </li>
                    </ul>
                    
                    <button class="w-full bg-gradient-primary text-white py-3 rounded-lg font-medium hover:opacity-90 transition mb-2" data-cta="start-trial">
                        Start 7-Day Trial
                    </button>
                    <p class="text-center text-sm text-gray-400">No credit card required</p>
                </div>
                
                <!-- Pro Plan -->
                <div class="glass rounded-2xl p-8" data-aos="zoom-in" data-aos-delay="200">
                    <h3 class="text-2xl font-bold mb-2">Pro</h3>
                    <div class="flex items-baseline mb-4">
                        <span class="text-4xl font-bold" data-price="team">$49.99</span>
                        <span class="text-gray-400 ml-2">/month</span>
                    </div>
                    <p class="text-gray-300 mb-8">Scale your content empire</p>
                    
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Everything in Creator</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Unlimited uploads</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Unlimited AI predictions</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Advanced analytics</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>API access (10k calls)</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>Custom branding</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <span class="text-green-500">✓</span>
                            <span>1-on-1 onboarding</span>
                        </li>
                    </ul>
                    
                    <button class="w-full glass hover:bg-white/20 text-white py-3 rounded-lg font-medium transition" data-cta="contact-sales">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section class="py-24" id="download">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="glass-strong rounded-3xl p-12 md:p-16 text-center">
                <h2 class="text-4xl md:text-5xl font-display font-bold mb-4" data-aos="fade-up">
                    Ready to <span class="gradient-text">10x Your Content</span>?
                </h2>
                <p class="text-xl text-gray-300 mb-12" data-aos="fade-up" data-aos-delay="100">
                    Join 10,000+ creators who are already saving 10+ hours every week
                </p>
                
                <div class="grid md:grid-cols-3 gap-6 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                    <!-- Windows Download -->
                    <button class="glass hover:bg-white/20 rounded-xl p-6 transition group" data-download="windows">
                        <svg class="w-12 h-12 mx-auto mb-4 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M0 3.449L9.75 2.1v9.451H0m10.949-9.602L24 0v11.4H10.949M0 12.6h9.75v9.451L0 20.699M10.949 12.6H24V24l-12.9-1.801"/>
                        </svg>
                        <div class="font-bold mb-1">Download for Windows</div>
                        <div class="text-sm text-gray-400">Windows 10 or later • 45MB</div>
                    </button>
                    
                    <!-- macOS Download -->
                    <button class="glass hover:bg-white/20 rounded-xl p-6 transition group" data-download="macos">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                        </svg>
                        <div class="font-bold mb-1">Download for macOS</div>
                        <div class="text-sm text-gray-400">macOS 10.15+ • 52MB</div>
                    </button>
                    
                    <!-- Linux Download -->
                    <button class="glass hover:bg-white/20 rounded-xl p-6 transition group" data-download="linux">
                        <svg class="w-12 h-12 mx-auto mb-4 text-yellow-400" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <div class="font-bold mb-1">Download for Linux</div>
                        <div class="text-sm text-gray-400">Ubuntu, Debian, Fedora • 48MB</div>
                    </button>
                </div>
                
                <div class="mt-12 text-gray-400" data-aos="fade-up" data-aos-delay="300">
                    <p class="mb-2">System Requirements:</p>
                    <p class="text-sm">4GB RAM (8GB recommended) • 500MB free disk space • Internet connection for uploads</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="border-t border-white/10 py-12 mt-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <!-- Brand -->
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-xl">C</span>
                        </div>
                        <span class="font-display font-bold text-xl">CreatorOS</span>
                    </div>
                    <p class="text-gray-400 mb-4">
                        The AI-powered desktop app for content creators. Create content 10x faster.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23 3a10.9 10.9 0 01-3.14 1.53 4.48 4.48 0 00-7.86 3v1A10.66 10.66 0 013 4s-4 9 5 13a11.64 11.64 0 01-7 2c9 5 20 0 20-11.5a4.5 4.5 0 00-.08-.83A7.72 7.72 0 0023 3z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm4.441 16.892c-2.102.144-6.784.144-8.883 0C5.282 16.736 5.017 15.622 5 12c.017-3.629.285-4.736 2.558-4.892 2.099-.144 6.782-.144 8.883 0C18.718 7.264 18.982 8.378 19 12c-.018 3.629-.285 4.736-2.559 4.892zM10 9.658l4.917 2.338L10 14.342V9.658z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0C5.37 0 0 5.37 0 12c0 5.31 3.435 9.795 8.205 11.385.6.105.825-.255.825-.57 0-.285-.015-1.23-.015-2.235-3.015.555-3.795-.735-4.035-1.41-.135-.345-.72-1.41-1.23-1.695-.42-.225-1.02-.78-.015-.795.945-.015 1.62.87 1.845 1.23 1.08 1.815 2.805 1.305 3.495.99.105-.78.42-1.305.765-1.605-2.67-.3-5.46-1.335-5.46-5.925 0-1.305.465-2.385 1.23-3.225-.12-.3-.54-1.53.12-3.18 0 0 1.005-.315 3.3 1.23.96-.27 1.98-.405 3-.405s2.04.135 3 .405c2.295-1.56 3.3-1.23 3.3-1.23.66 1.65.24 2.88.12 3.18.765.84 1.23 1.905 1.23 3.225 0 4.605-2.805 5.625-5.475 5.925.435.375.81 1.095.81 2.22 0 1.605-.015 2.895-.015 3.3 0 .315.225.69.825.57A12.02 12.02 0 0024 12c0-6.63-5.37-12-12-12z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Product -->
                <div>
                    <h4 class="font-bold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#features" class="hover:text-white transition">Features</a></li>
                        <li><a href="#pricing" class="hover:text-white transition">Pricing</a></li>
                        <li><a href="#download" class="hover:text-white transition">Download</a></li>
                        <li><a href="#" class="hover:text-white transition">Changelog</a></li>
                    </ul>
                </div>
                
                <!-- Resources -->
                <div>
                    <h4 class="font-bold mb-4">Resources</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">Documentation</a></li>
                        <li><a href="#" class="hover:text-white transition">API Reference</a></li>
                        <li><a href="#" class="hover:text-white transition">Tutorials</a></li>
                        <li><a href="#" class="hover:text-white transition">Blog</a></li>
                    </ul>
                </div>
                
                <!-- Company -->
                <div>
                    <h4 class="font-bold mb-4">Company</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition">About</a></li>
                        <li><a href="#" class="hover:text-white transition">Careers</a></li>
                        <li><a href="#" class="hover:text-white transition">Contact</a></li>
                        <li><a href="#" class="hover:text-white transition">Press Kit</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-white/10 pt-8 flex flex-col md:flex-row justify-between items-center text-gray-400 text-sm">
                <p>&copy; 2024 CreatorOS Inc. All rights reserved.</p>
                <p>Made with ❤️ by creators, for creators</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            once: true
        });
        
        // Counter animation
        const counters = document.querySelectorAll('[data-count]');
        const speed = 200;
        
        counters.forEach(counter => {
            const animate = () => {
                const value = +counter.getAttribute('data-count');
                const data = +counter.innerText.replace(/,/g, '');
                const time = value / speed;
                
                if (data < value) {
                    counter.innerText = Math.ceil(data + time).toLocaleString();
                    setTimeout(animate, 1);
                } else {
                    counter.innerText = value.toLocaleString();
                }
            }
            
            // Start animation when element is in view
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animate();
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
        
        // Smooth scroll for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href'))?.scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
    
    <!-- Main JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/mobile-menu-fix.js"></script>
</body>
</html>