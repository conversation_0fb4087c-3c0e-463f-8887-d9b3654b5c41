<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="600" fill="#1A1A23"/>
  
  <!-- Window frame -->
  <rect x="50" y="50" width="700" height="500" rx="12" fill="url(#windowBg)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- Header -->
  <rect x="50" y="50" width="700" height="40" rx="12" fill="rgba(255,255,255,0.05)"/>
  <circle cx="70" cy="70" r="5" fill="#FF5F57"/>
  <circle cx="85" cy="70" r="5" fill="#FFBD2E"/>
  <circle cx="100" cy="70" r="5" fill="#28CA42"/>
  <text x="400" y="76" font-family="Inter, sans-serif" font-size="14" font-weight="500" fill="white" text-anchor="middle">Workflow Pipeline</text>
  
  <!-- Pipeline stages -->
  <!-- Download Stage -->
  <g transform="translate(100, 150)">
    <rect width="140" height="80" rx="8" fill="url(#stage1)" stroke="url(#stageBorder1)" stroke-width="2"/>
    <text x="70" y="35" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">Download</text>
    <circle cx="70" cy="55" r="15" fill="rgba(255,255,255,0.1)"/>
    <path d="M70 48L70 58M70 58L65 53M70 58L75 53" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Arrow 1 -->
  <path d="M240 190L280 190" stroke="url(#arrowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Process Stage -->
  <g transform="translate(280, 150)">
    <rect width="140" height="80" rx="8" fill="url(#stage2)" stroke="url(#stageBorder2)" stroke-width="2"/>
    <text x="70" y="35" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">AI Process</text>
    <circle cx="70" cy="55" r="15" fill="rgba(255,255,255,0.1)"/>
    <path d="M60 55L65 60L80 45" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Arrow 2 -->
  <path d="M420 190L460 190" stroke="url(#arrowGradient)" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Upload Stage -->
  <g transform="translate(460, 150)">
    <rect width="140" height="80" rx="8" fill="url(#stage3)" stroke="url(#stageBorder3)" stroke-width="2"/>
    <text x="70" y="35" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">Upload</text>
    <circle cx="70" cy="55" r="15" fill="rgba(255,255,255,0.1)"/>
    <path d="M70 62L70 52M70 52L65 57M70 52L75 57" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Platform icons below -->
  <g transform="translate(100, 300)">
    <text x="0" y="0" font-family="Inter, sans-serif" font-size="12" font-weight="500" fill="rgba(255,255,255,0.6)">Source Platforms:</text>
    <circle cx="20" cy="30" r="20" fill="#FF0000" opacity="0.8"/>
    <circle cx="70" cy="30" r="20" fill="#000000" opacity="0.8"/>
    <circle cx="120" cy="30" r="20" fill="url(#instaGrad)" opacity="0.8"/>
  </g>
  
  <g transform="translate(460, 300)">
    <text x="0" y="0" font-family="Inter, sans-serif" font-size="12" font-weight="500" fill="rgba(255,255,255,0.6)">Target Platforms:</text>
    <circle cx="20" cy="30" r="20" fill="#FF0000" opacity="0.8"/>
    <circle cx="70" cy="30" r="20" fill="#000000" opacity="0.8"/>
    <circle cx="120" cy="30" r="20" fill="url(#instaGrad)" opacity="0.8"/>
  </g>
  
  <!-- Progress indicator -->
  <rect x="100" y="450" width="600" height="6" rx="3" fill="rgba(255,255,255,0.1)"/>
  <rect x="100" y="450" width="400" height="6" rx="3" fill="url(#progressGrad)">
    <animate attributeName="width" values="0;400;0" dur="4s" repeatCount="indefinite"/>
  </rect>
  
  <!-- Status text -->
  <text x="400" y="490" font-family="Inter, sans-serif" font-size="14" font-weight="400" fill="rgba(255,255,255,0.8)" text-anchor="middle">Processing 3 videos • 2 completed • 1 in progress</text>
  
  <defs>
    <linearGradient id="windowBg" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(139,92,246,0.05);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(59,130,246,0.02);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="stage1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.2" />
    </linearGradient>
    
    <linearGradient id="stage2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.2" />
    </linearGradient>
    
    <linearGradient id="stage3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.2" />
    </linearGradient>
    
    <linearGradient id="stageBorder1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.5" />
    </linearGradient>
    
    <linearGradient id="stageBorder2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.5" />
    </linearGradient>
    
    <linearGradient id="stageBorder3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.5" />
    </linearGradient>
    
    <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.2)" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.6)" />
    </linearGradient>
    
    <linearGradient id="progressGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="instaGrad" x1="0%" y1="100%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FED373" />
      <stop offset="50%" style="stop-color:#D92E7F" />
      <stop offset="100%" style="stop-color:#515ECF" />
    </linearGradient>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
      <polygon points="0 0, 10 3, 0 6" fill="rgba(255,255,255,0.6)" />
    </marker>
  </defs>
</svg>