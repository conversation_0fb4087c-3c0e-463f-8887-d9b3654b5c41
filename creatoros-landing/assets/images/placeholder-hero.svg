<svg width="1200" height="800" viewBox="0 0 1200 800" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="800" fill="#1A1A23"/>
  
  <!-- Glass window frame -->
  <rect x="100" y="50" width="1000" height="700" rx="20" fill="url(#glass)" stroke="url(#borderGradient)" stroke-width="2"/>
  
  <!-- Header bar -->
  <rect x="100" y="50" width="1000" height="60" rx="20" fill="url(#headerGlass)"/>
  <rect x="100" y="100" width="1000" height="2" fill="url(#divider)"/>
  
  <!-- Traffic lights -->
  <circle cx="140" cy="80" r="8" fill="#FF5F57"/>
  <circle cx="165" cy="80" r="8" fill="#FFBD2E"/>
  <circle cx="190" cy="80" r="8" fill="#28CA42"/>
  
  <!-- App title -->
  <text x="550" y="88" font-family="Inter, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">CreatorOS</text>
  
  <!-- Sidebar -->
  <rect x="100" y="110" width="250" height="640" fill="url(#sidebarGlass)"/>
  <rect x="340" y="110" width="2" height="640" fill="url(#divider)"/>
  
  <!-- Main content area -->
  <rect x="370" y="140" width="700" height="580" rx="10" fill="url(#contentGlass)"/>
  
  <!-- AI Glow Effect -->
  <ellipse cx="720" cy="430" rx="300" ry="200" fill="url(#aiGlow)" opacity="0.3"/>
  
  <!-- Feature cards -->
  <rect x="400" y="180" width="300" height="150" rx="12" fill="url(#cardGlass)" stroke="url(#cardBorder)" stroke-width="1"/>
  <rect x="730" y="180" width="300" height="150" rx="12" fill="url(#cardGlass)" stroke="url(#cardBorder)" stroke-width="1"/>
  <rect x="400" y="360" width="300" height="150" rx="12" fill="url(#cardGlass)" stroke="url(#cardBorder)" stroke-width="1"/>
  <rect x="730" y="360" width="300" height="150" rx="12" fill="url(#cardGlass)" stroke="url(#cardBorder)" stroke-width="1"/>
  
  <!-- Progress indicator -->
  <rect x="400" y="550" width="630" height="8" rx="4" fill="rgba(255,255,255,0.1)"/>
  <rect x="400" y="550" width="378" height="8" rx="4" fill="url(#progressGradient)"/>
  
  <!-- Floating elements -->
  <circle cx="150" cy="200" r="30" fill="url(#floatGradient1)" opacity="0.6"/>
  <circle cx="1050" cy="650" r="40" fill="url(#floatGradient2)" opacity="0.5"/>
  
  <!-- Definitions -->
  <defs>
    <linearGradient id="glass" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.02);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="headerGlass" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.08);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.04);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="sidebarGlass" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.03);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.01);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="contentGlass" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(139,92,246,0.05);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(59,130,246,0.02);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="cardGlass" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.5" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.5" />
    </linearGradient>
    
    <linearGradient id="cardBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(139,92,246,0.3);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(59,130,246,0.3);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="divider" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.1);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="aiGlow">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="floatGradient1">
      <stop offset="0%" style="stop-color:#EC4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="floatGradient2">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0" />
    </radialGradient>
  </defs>
</svg>