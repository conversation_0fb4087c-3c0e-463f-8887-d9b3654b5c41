<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="48" fill="url(#avatarBg3)" stroke="url(#avatarBorder3)" stroke-width="2"/>
  
  <!-- Face -->
  <circle cx="50" cy="45" r="22" fill="#92400E"/>
  
  <!-- Hair (afro style) -->
  <circle cx="50" cy="35" r="25" fill="#1F2937"/>
  <circle cx="35" cy="40" r="15" fill="#1F2937"/>
  <circle cx="65" cy="40" r="15" fill="#1F2937"/>
  
  <!-- Eyes -->
  <circle cx="40" cy="45" r="2" fill="#F3F4F6"/>
  <circle cx="60" cy="45" r="2" fill="#F3F4F6"/>
  
  <!-- Cool smile -->
  <path d="M35 52C35 52 42 58 50 58C58 58 65 52 65 52" stroke="#F3F4F6" stroke-width="2" stroke-linecap="round" fill="none"/>
  
  <!-- Sunglasses (lifted up) -->
  <path d="M30 25C30 25 35 20 40 20C45 20 47.5 25 50 25C52.5 25 55 20 60 20C65 20 70 25 70 25" stroke="#1F2937" stroke-width="2" fill="none"/>
  <rect x="30" y="18" width="15" height="10" rx="5" fill="#1F2937" opacity="0.8"/>
  <rect x="55" y="18" width="15" height="10" rx="5" fill="#1F2937" opacity="0.8"/>
  <path d="M45 23L55 23" stroke="#1F2937" stroke-width="2"/>
  
  <!-- Microphone -->
  <rect x="45" y="65" width="10" height="20" rx="5" fill="#3B82F6"/>
  <circle cx="50" cy="70" r="8" fill="#60A5FA"/>
  <path d="M42 70C42 70 42 65 50 65C58 65 58 70 58 70" stroke="#1E40AF" stroke-width="1" stroke-linecap="round"/>
  <rect x="48" y="83" width="4" height="10" fill="#1F2937"/>
  <rect x="45" y="91" width="10" height="4" rx="2" fill="#1F2937"/>
  
  <!-- Sound waves -->
  <path d="M65 68C65 68 68 65 68 70C68 75 65 72 65 72" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" opacity="0.6"/>
  <path d="M70 68C70 68 73 65 73 70C73 75 70 72 70 72" stroke="#3B82F6" stroke-width="2" stroke-linecap="round" opacity="0.4"/>
  
  <defs>
    <radialGradient id="avatarBg3">
      <stop offset="0%" style="stop-color:#DBEAFE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BFDBFE;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="avatarBorder3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>