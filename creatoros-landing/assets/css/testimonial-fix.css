/* Specific fixes for testimonial section */

/* Reset the testimonial grid */
#testimonials .grid {
    display: grid;
    gap: 2rem;
}

/* Fix the video testimonial card */
#testimonials .lg\:col-span-2 {
    grid-column: span 1;
}

@media (min-width: 768px) {
    #testimonials .md\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    #testimonials .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    #testimonials .lg\:col-span-2 {
        grid-column: span 2;
    }
}

/* Fix the video container */
#testimonials .glass {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Fix aspect ratio container */
#testimonials .relative[style*="padding-bottom"] {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25% !important;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
}

/* Fix the image inside video container */
#testimonials .relative[style*="padding-bottom"] img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fix play button positioning */
#testimonials button[data-testimonial-video] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;
}

/* Play button circle */
#testimonials button[data-testimonial-video] > div {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease, background 0.3s ease;
}

#testimonials button[data-testimonial-video]:hover > div {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* Fix testimonial content padding */
#testimonials .p-8 {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Fix text spacing */
#testimonials .text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
    margin-bottom: 1.5rem;
}

/* Fix avatar and info alignment */
#testimonials .flex.items-center.space-x-4 {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Fix avatar size */
#testimonials .w-12.h-12 {
    width: 48px;
    height: 48px;
    flex-shrink: 0;
}

/* Fix text testimonial cards */
#testimonials > div > div:nth-child(2) > div:not(.lg\:col-span-2) {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Ensure consistent card heights */
#testimonials .glass.rounded-2xl {
    min-height: 400px;
}

/* Fix metrics grid at bottom */
#testimonials > div > div:last-child {
    margin-top: 3rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    #testimonials .glass.rounded-2xl {
        min-height: auto;
    }
    
    #testimonials .p-8 {
        padding: 1.5rem;
    }
    
    #testimonials button[data-testimonial-video] > div {
        width: 60px;
        height: 60px;
    }
    
    #testimonials .text-lg {
        font-size: 1rem;
    }
}

/* Fix overflow and spacing issues */
#testimonials {
    overflow: hidden;
}

#testimonials .max-w-7xl {
    overflow: visible;
}

/* Ensure proper stacking context */
#testimonials .relative {
    position: relative;
    z-index: 1;
}

/* Fix button focus states */
#testimonials button:focus {
    outline: none;
}

#testimonials button:focus-visible > div {
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5);
}