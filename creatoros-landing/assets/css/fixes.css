/* Layout fixes for CreatorOS Landing Page */

/* Fix testimonial section layout */
#testimonials .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ensure proper spacing for testimonial cards */
#testimonials .grid {
    gap: 2rem;
}

/* Fix video testimonial card */
#testimonials .aspect-video {
    position: relative;
    padding-bottom: 56.25%;
    height: 0;
    overflow: hidden;
}

#testimonials .aspect-video img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fix text overflow in testimonial cards */
#testimonials p {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Ensure testimonial cards have minimum height */
#testimonials .glass {
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

/* Fix metrics grid at bottom */
#testimonials .grid.grid-cols-1 {
    margin-top: 3rem;
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
    #testimonials .lg\\:col-span-2 {
        grid-column: span 1;
    }
    
    #testimonials .grid {
        gap: 1.5rem;
    }
    
    #testimonials .glass {
        min-height: auto;
    }
}

/* Fix navigation z-index */
nav {
    z-index: 1000;
}

/* Fix mobile menu positioning */
#mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(15, 15, 20, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    max-height: calc(100vh - 64px);
    overflow-y: auto;
}

/* Fix section padding for better spacing */
section {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

/* Fix hero section min-height */
.min-h-screen {
    min-height: calc(100vh - 64px);
}

/* Fix glass card hover effects */
.glass {
    transition: all 0.3s ease;
}

.glass:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Fix button states */
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Fix overflow issues */
body {
    overflow-x: hidden;
}

.max-w-7xl {
    overflow-x: hidden;
}

/* Fix grid layouts on different screen sizes */
@media (max-width: 640px) {
    .grid {
        grid-template-columns: 1fr !important;
    }
}

/* Fix text readability */
.text-gray-300 {
    color: rgba(255, 255, 255, 0.7);
}

.text-gray-400 {
    color: rgba(255, 255, 255, 0.5);
}

/* Fix feature cards grid */
#features .grid > div {
    height: 100%;
}

#features .glass {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Fix pricing cards alignment */
#pricing .grid > div {
    display: flex;
    flex-direction: column;
}

#pricing .glass {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Fix download section buttons */
#download .grid {
    gap: 1.5rem;
}

#download button {
    width: 100%;
    justify-content: center;
}

/* Animation performance optimization */
[data-aos] {
    will-change: transform, opacity;
}

/* Fix footer layout */
footer .grid {
    gap: 2rem;
}

@media (max-width: 768px) {
    footer .grid {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Fix z-index stacking */
.relative {
    position: relative;
}

.z-10 {
    z-index: 10;
}

.z-20 {
    z-index: 20;
}

.z-30 {
    z-index: 30;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

/* Ensure proper contrast for text */
.gradient-text {
    font-weight: 600;
}

/* Fix mobile hamburger menu */
#mobile-menu-button {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
}

#mobile-menu-button span {
    display: block;
    transition: all 0.3s ease;
    transform-origin: center;
}

#mobile-menu-button.active span:nth-child(1) {
    transform: rotate(45deg) translateY(7px);
}

#mobile-menu-button.active span:nth-child(2) {
    opacity: 0;
}

#mobile-menu-button.active span:nth-child(3) {
    transform: rotate(-45deg) translateY(-7px);
}

/* Ensure mobile menu is properly positioned */
@media (max-width: 767px) {
    #mobile-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(15, 15, 20, 0.98);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 999;
    }
    
    #mobile-menu.hidden {
        display: none !important;
    }
    
    nav {
        position: relative;
    }
}

/* Additional responsive fixes */
@media (max-width: 1024px) {
    .lg\:col-span-2 {
        grid-column: span 2 / span 2;
    }
    
    @media (max-width: 768px) {
        .lg\:col-span-2 {
            grid-column: span 1 / span 1;
        }
    }
}

/* Fix testimonial image container */
.aspect-video {
    aspect-ratio: 16/9;
    position: relative;
    overflow: hidden;
}

/* Fix container widths on mobile */
@media (max-width: 640px) {
    .max-w-7xl {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    section {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }
}

/* Fix pricing toggle visibility */
#pricing > div > div:first-child {
    display: flex;
    justify-content: center;
}

/* Ensure proper navigation height */
nav {
    min-height: 64px;
    position: sticky;
    top: 0;
}

/* Fix hero section spacing */
#hero, .hero-section {
    padding-top: 2rem;
}