<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Lightning bolt -->
  <path d="M38 12L26 28H36L24 52L40 32H30L38 12Z" fill="url(#lightningGradient)" stroke="url(#lightningStroke)" stroke-width="2" stroke-linejoin="round"/>
  
  <!-- Speed lines -->
  <path d="M12 20L20 20" stroke="url(#speedGradient)" stroke-width="2" stroke-linecap="round" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
  </path>
  <path d="M10 32L18 32" stroke="url(#speedGradient)" stroke-width="2" stroke-linecap="round" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="1.5s" begin="0.3s" repeatCount="indefinite"/>
  </path>
  <path d="M14 44L22 44" stroke="url(#speedGradient)" stroke-width="2" stroke-linecap="round" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="1.5s" begin="0.6s" repeatCount="indefinite"/>
  </path>
  
  <!-- Energy circle -->
  <circle cx="32" cy="32" r="28" stroke="url(#energyGradient)" stroke-width="1" fill="none" opacity="0.3">
    <animate attributeName="r" values="28;32;28" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0.6;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <defs>
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%%" y2="100%">
      <stop offset="0%" style="stop-color:#FDE047;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FACC15;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="lightningStroke" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="speedGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:0" />
      <stop offset="50%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0" />
    </linearGradient>
    
    <radialGradient id="energyGradient">
      <stop offset="0%" style="stop-color:#FDE047;stop-opacity:0" />
      <stop offset="50%" style="stop-color:#FACC15;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </radialGradient>
  </defs>
</svg>