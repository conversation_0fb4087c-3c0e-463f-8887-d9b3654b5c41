<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Shield background -->
  <path d="M32 8C32 8 48 12 48 12V28C48 40 40 48 32 56C24 48 16 40 16 28V12C16 12 32 8 32 8Z" 
        fill="url(#shieldGradient)" stroke="url(#shieldBorder)" stroke-width="2"/>
  
  <!-- Lock icon -->
  <rect x="26" y="28" width="12" height="10" rx="2" fill="white" opacity="0.9"/>
  <path d="M29 28V25C29 23.3 30.3 22 32 22C33.7 22 35 23.3 35 25V28" 
        stroke="white" stroke-width="2" stroke-linecap="round" fill="none"/>
  <circle cx="32" cy="33" r="1.5" fill="#1F2937"/>
  
  <!-- Protection waves -->
  <circle cx="32" cy="32" r="20" stroke="url(#waveGradient)" stroke-width="1" fill="none" opacity="0">
    <animate attributeName="r" values="20;35;20" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0;0.5;0" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="32" cy="32" r="20" stroke="url(#waveGradient)" stroke-width="1" fill="none" opacity="0">
    <animate attributeName="r" values="20;35;20" dur="3s" begin="1s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0;0.5;0" dur="3s" begin="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Security particles -->
  <circle cx="20" cy="20" r="1" fill="#10B981" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="44" cy="20" r="1" fill="#10B981" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="20" cy="44" r="1" fill="#10B981" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="44" cy="44" r="1" fill="#10B981" opacity="0">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <defs>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="shieldBorder" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="waveGradient">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0" />
      <stop offset="100%" style="stop-color:#10B981;stop-opacity:1" />
    </radialGradient>
  </defs>
</svg>