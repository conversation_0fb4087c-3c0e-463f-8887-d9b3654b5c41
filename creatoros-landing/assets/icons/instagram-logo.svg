<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect x="4" y="4" width="40" height="40" rx="10" fill="url(#instagramGradient)"/>
  <rect x="12" y="12" width="24" height="24" rx="8" stroke="white" stroke-width="2" fill="none"/>
  <circle cx="24" cy="24" r="6" stroke="white" stroke-width="2" fill="none"/>
  <circle cx="32" cy="16" r="2" fill="white"/>
  
  <defs>
    <linearGradient id="instagramGradient" x1="0%" y1="100%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#FED373;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#F15245;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#D92E7F;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#9B36B7;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#515ECF;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>