<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Network connections -->
  <line x1="32" y1="32" x2="16" y2="16" stroke="url(#networkGradient)" stroke-width="2" opacity="0.3"/>
  <line x1="32" y1="32" x2="48" y2="16" stroke="url(#networkGradient)" stroke-width="2" opacity="0.3"/>
  <line x1="32" y1="32" x2="16" y2="48" stroke="url(#networkGradient)" stroke-width="2" opacity="0.3"/>
  <line x1="32" y1="32" x2="48" y2="48" stroke="url(#networkGradient)" stroke-width="2" opacity="0.3"/>
  
  <!-- Center hub -->
  <circle cx="32" cy="32" r="12" fill="url(#hubGradient)"/>
  <circle cx="32" cy="32" r="8" fill="#FFFFFF" opacity="0.2"/>
  <text x="32" y="37" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">1</text>
  
  <!-- Platform nodes -->
  <!-- YouTube -->
  <circle cx="16" cy="16" r="10" fill="#FF0000"/>
  <path d="M13 13L19 16L13 19V13Z" fill="white"/>
  
  <!-- TikTok -->
  <circle cx="48" cy="16" r="10" fill="#000000"/>
  <text x="48" y="21" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">♪</text>
  
  <!-- Instagram -->
  <circle cx="16" cy="48" r="10" fill="url(#instaGradient)"/>
  <rect x="13" y="45" width="6" height="6" rx="1.5" stroke="white" stroke-width="1.5" fill="none"/>
  <circle cx="16" cy="48" r="2" stroke="white" stroke-width="1.5" fill="none"/>
  
  <!-- Twitter/X -->
  <circle cx="48" cy="48" r="10" fill="#1DA1F2"/>
  <text x="48" y="53" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white" text-anchor="middle">X</text>
  
  <!-- Animated pulse -->
  <circle cx="32" cy="32" r="12" fill="none" stroke="url(#pulseGradient)" stroke-width="1">
    <animate attributeName="r" values="12;20;12" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="1;0;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <defs>
    <linearGradient id="networkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="hubGradient">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="instaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#833AB4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FD1D1D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FCAF45;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="pulseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>