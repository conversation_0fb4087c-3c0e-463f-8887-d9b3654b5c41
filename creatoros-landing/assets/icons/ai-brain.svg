<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="32" cy="32" r="30" stroke="url(#brainGradient)" stroke-width="2" opacity="0.3"/>
  <circle cx="32" cy="32" r="24" stroke="url(#brainGradient)" stroke-width="1" opacity="0.2"/>
  
  <path d="M32 12C25.4 12 20 17.4 20 24C20 24.6 20.1 25.2 20.2 25.8C19.5 26.5 19 27.5 19 28.5C19 30.4 20.6 32 22.5 32C22.7 32 22.9 32 23.1 31.9C24.3 34.4 26.3 36.4 28.8 37.6C28.3 38.3 28 39.1 28 40C28 42.2 29.8 44 32 44C34.2 44 36 42.2 36 40C36 39.1 35.7 38.3 35.2 37.6C37.7 36.4 39.7 34.4 40.9 31.9C41.1 32 41.3 32 41.5 32C43.4 32 45 30.4 45 28.5C45 27.5 44.5 26.5 43.8 25.8C43.9 25.2 44 24.6 44 24C44 17.4 38.6 12 32 12Z" fill="url(#brainGradient)"/>
  
  <path d="M26 20C26 18.9 26.9 18 28 18C29.1 18 30 18.9 30 20" stroke="white" stroke-width="1.5" stroke-linecap="round" opacity="0.8"/>
  <path d="M34 20C34 18.9 34.9 18 36 18C37.1 18 38 18.9 38 20" stroke="white" stroke-width="1.5" stroke-linecap="round" opacity="0.8"/>
  <path d="M28 26H36" stroke="white" stroke-width="1.5" stroke-linecap="round" opacity="0.8"/>
  <path d="M30 30C30 31.1 30.9 32 32 32C33.1 32 34 31.1 34 30" stroke="white" stroke-width="1.5" stroke-linecap="round" opacity="0.8"/>
  
  <!-- AI Sparkles -->
  <circle cx="48" cy="16" r="2" fill="url(#sparkleGradient)">
    <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="16" cy="48" r="2" fill="url(#sparkleGradient)">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="0.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="50" cy="44" r="1.5" fill="url(#sparkleGradient)">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="14" cy="20" r="1.5" fill="url(#sparkleGradient)">
    <animate attributeName="opacity" values="0;1;0" dur="2s" begin="1.5s" repeatCount="indefinite"/>
  </circle>
  
  <defs>
    <linearGradient id="brainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    
    <radialGradient id="sparkleGradient">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0.5" />
    </radialGradient>
  </defs>
</svg>