// Main JavaScript for CreatorOS Landing Page

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    
    // 1. Mobile Menu Toggle - DISABLED (using mobile-menu-fix.js instead)
    /*
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const body = document.body;
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            const isOpen = !mobileMenu.classList.contains('hidden');
            
            if (isOpen) {
                mobileMenu.classList.add('hidden');
                body.style.overflow = '';
                // Reset hamburger icon
                const spans = this.querySelectorAll('span');
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            } else {
                mobileMenu.classList.remove('hidden');
                body.style.overflow = 'hidden';
                // Transform to X icon
                const spans = this.querySelectorAll('span');
                spans[0].style.transform = 'rotate(45deg) translateY(7px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translateY(-7px)';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
                mobileMenu.classList.add('hidden');
                body.style.overflow = '';
                // Reset hamburger icon
                const spans = mobileMenuButton.querySelectorAll('span');
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            }
        });
        
        // Close mobile menu when clicking on a link
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                body.style.overflow = '';
                // Reset hamburger icon
                const spans = mobileMenuButton.querySelectorAll('span');
                spans[0].style.transform = '';
                spans[1].style.opacity = '';
                spans[2].style.transform = '';
            });
        });
    }
    */
    
    // 2. Smooth Scroll for Navigation Links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // 3. OS Detection for Download Buttons
    function detectOS() {
        const platform = navigator.platform.toLowerCase();
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (platform.includes('mac') || userAgent.includes('mac')) {
            return 'macos';
        } else if (platform.includes('linux') || userAgent.includes('linux')) {
            return 'linux';
        } else {
            return 'windows';
        }
    }
    
    // Update primary download buttons based on OS
    const currentOS = detectOS();
    const primaryDownloadBtns = document.querySelectorAll('[data-download-primary]');
    
    primaryDownloadBtns.forEach(btn => {
        if (currentOS === 'macos') {
            btn.innerHTML = `
                <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                </svg>
                Download for macOS
                <span class="text-xs opacity-75 block">macOS 10.15+ • 52MB</span>
            `;
        } else if (currentOS === 'linux') {
            btn.innerHTML = `
                <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                </svg>
                Download for Linux
                <span class="text-xs opacity-75 block">Ubuntu, Debian, Fedora • 48MB</span>
            `;
        }
    });
    
    // 4. Newsletter Form Submission
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            
            // Show success message
            const button = this.querySelector('button');
            const originalText = button.innerHTML;
            button.innerHTML = '✓ Subscribed!';
            button.disabled = true;
            
            // Reset after 3 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                this.reset();
            }, 3000);
            
            // In production, you would send this to your backend
            console.log('Newsletter signup:', email);
        });
    }
    
    // 5. Video Modal
    const demoButton = document.querySelector('[data-demo-video]');
    if (demoButton) {
        demoButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Create modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm';
            modal.innerHTML = `
                <div class="relative w-full max-w-4xl">
                    <button class="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                    <div class="relative pt-[56.25%] rounded-xl overflow-hidden bg-black">
                        <iframe 
                            class="absolute inset-0 w-full h-full"
                            src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                        ></iframe>
                    </div>
                </div>
            `;
            
            // Add to body
            document.body.appendChild(modal);
            body.style.overflow = 'hidden';
            
            // Close on click
            modal.addEventListener('click', function(e) {
                if (e.target === modal || e.target.closest('button')) {
                    modal.remove();
                    body.style.overflow = '';
                }
            });
        });
    }
    
    // 6. Pricing Toggle (Monthly/Yearly)
    const pricingToggle = document.createElement('div');
    pricingToggle.className = 'flex items-center justify-center gap-4 mb-12';
    pricingToggle.innerHTML = `
        <span class="text-gray-400">Monthly</span>
        <button class="relative w-14 h-7 bg-gray-700 rounded-full transition-colors" data-pricing-toggle>
            <span class="absolute left-1 top-1 w-5 h-5 bg-white rounded-full transition-transform"></span>
        </button>
        <span class="text-gray-400">Yearly <span class="text-green-400 text-sm">(Save 20%)</span></span>
    `;
    
    const pricingSection = document.querySelector('#pricing .grid');
    if (pricingSection) {
        pricingSection.parentNode.insertBefore(pricingToggle, pricingSection);
        
        const toggle = pricingToggle.querySelector('[data-pricing-toggle]');
        let isYearly = false;
        
        toggle.addEventListener('click', function() {
            isYearly = !isYearly;
            
            // Update toggle appearance
            const indicator = this.querySelector('span');
            if (isYearly) {
                this.classList.add('bg-gradient-to-r', 'from-purple-600', 'to-blue-600');
                this.classList.remove('bg-gray-700');
                indicator.style.transform = 'translateX(1.75rem)';
            } else {
                this.classList.remove('bg-gradient-to-r', 'from-purple-600', 'to-blue-600');
                this.classList.add('bg-gray-700');
                indicator.style.transform = 'translateX(0)';
            }
            
            // Update prices
            const prices = {
                starter: { monthly: '$0', yearly: '$0' },
                creator: { monthly: '$19.99', yearly: '$191.88' },
                team: { monthly: '$49.99', yearly: '$479.88' }
            };
            
            document.querySelectorAll('[data-price]').forEach(el => {
                const plan = el.getAttribute('data-price');
                if (prices[plan]) {
                    el.textContent = isYearly ? prices[plan].yearly : prices[plan].monthly;
                    
                    // Update period text
                    const period = el.nextElementSibling;
                    if (period && plan !== 'starter') {
                        period.textContent = isYearly ? '/year' : '/month';
                    }
                }
            });
        });
    }
    
    // 7. Download buttons functionality
    document.querySelectorAll('[data-download]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const os = this.getAttribute('data-download');
            
            // Show download started message
            const originalContent = this.innerHTML;
            this.innerHTML = 'Starting download...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = originalContent;
                this.disabled = false;
                
                // In production, trigger actual download
                alert(`Download would start for ${os} version. In production, this would download the actual installer.`);
            }, 1500);
        });
    });
    
    // 8. CTA buttons functionality
    document.querySelectorAll('[data-cta]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.getAttribute('data-cta');
            
            switch(action) {
                case 'start-free':
                    window.location.href = '#download';
                    break;
                case 'start-trial':
                    alert('Trial signup would open here. In production, this would lead to account creation.');
                    break;
                case 'contact-sales':
                    alert('Sales contact form would open here. In production, this would open a contact modal or page.');
                    break;
            }
        });
    });
    
    // 9. Testimonial video thumbnails
    document.querySelectorAll('[data-testimonial-video]').forEach(thumb => {
        thumb.addEventListener('click', function() {
            const videoId = this.getAttribute('data-testimonial-video');
            alert(`Would play testimonial video ${videoId}. In production, this would open a video modal.`);
        });
    });
    
    // 10. Add intersection observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('aos-animate');
            }
        });
    }, observerOptions);
    
    // Observe all AOS elements
    document.querySelectorAll('[data-aos]').forEach(el => {
        observer.observe(el);
    });
    
    // 11. Header scroll effect
    const header = document.querySelector('header');
    let lastScroll = 0;
    
    window.addEventListener('scroll', () => {
        const currentScroll = window.pageYOffset;
        
        if (currentScroll > 100) {
            header.classList.add('backdrop-blur-xl', 'bg-dark-100/80', 'border-b', 'border-white/10');
        } else {
            header.classList.remove('backdrop-blur-xl', 'bg-dark-100/80', 'border-b', 'border-white/10');
        }
        
        lastScroll = currentScroll;
    });
    
    // 12. Platform filter in features section
    const platformFilters = document.querySelectorAll('[data-platform-filter]');
    if (platformFilters.length > 0) {
        platformFilters.forEach(filter => {
            filter.addEventListener('click', function() {
                const platform = this.getAttribute('data-platform-filter');
                
                // Update active state
                platformFilters.forEach(f => f.classList.remove('active'));
                this.classList.add('active');
                
                // Filter features (in production, this would filter actual content)
                console.log(`Filtering features for platform: ${platform}`);
            });
        });
    }
});

// Utility function for copying to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        console.log('Copied to clipboard:', text);
    }).catch(err => {
        console.error('Failed to copy:', err);
    });
}