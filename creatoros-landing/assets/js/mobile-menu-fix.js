// Simple mobile menu fix
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (!mobileMenuButton || !mobileMenu) {
        console.error('Mobile menu elements not found');
        return;
    }
    
    // Remove the mobile-menu.js functionality and use this instead
    let isMenuOpen = false;
    
    mobileMenuButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        isMenuOpen = !isMenuOpen;
        
        if (isMenuOpen) {
            // Show menu
            mobileMenu.classList.remove('hidden');
            mobileMenuButton.classList.add('active');
            
            // Animate hamburger to X
            const spans = mobileMenuButton.querySelectorAll('span');
            if (spans.length === 3) {
                spans[0].style.transform = 'rotate(45deg) translateY(7px)';
                spans[1].style.opacity = '0';
                spans[2].style.transform = 'rotate(-45deg) translateY(-7px)';
            }
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        } else {
            // Hide menu
            mobileMenu.classList.add('hidden');
            mobileMenuButton.classList.remove('active');
            
            // Reset hamburger
            const spans = mobileMenuButton.querySelectorAll('span');
            if (spans.length === 3) {
                spans[0].style.transform = '';
                spans[1].style.opacity = '1';
                spans[2].style.transform = '';
            }
            
            // Allow body scroll
            document.body.style.overflow = '';
        }
    });
    
    // Close menu when clicking menu links
    const menuLinks = mobileMenu.querySelectorAll('a');
    menuLinks.forEach(link => {
        link.addEventListener('click', function() {
            isMenuOpen = false;
            mobileMenu.classList.add('hidden');
            mobileMenuButton.classList.remove('active');
            
            // Reset hamburger
            const spans = mobileMenuButton.querySelectorAll('span');
            if (spans.length === 3) {
                spans[0].style.transform = '';
                spans[1].style.opacity = '1';
                spans[2].style.transform = '';
            }
            
            document.body.style.overflow = '';
        });
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (isMenuOpen && !mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
            isMenuOpen = false;
            mobileMenu.classList.add('hidden');
            mobileMenuButton.classList.remove('active');
            
            // Reset hamburger
            const spans = mobileMenuButton.querySelectorAll('span');
            if (spans.length === 3) {
                spans[0].style.transform = '';
                spans[1].style.opacity = '1';
                spans[2].style.transform = '';
            }
            
            document.body.style.overflow = '';
        }
    });
    
    // Close menu on window resize if open
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            if (window.innerWidth >= 768 && isMenuOpen) {
                isMenuOpen = false;
                mobileMenu.classList.add('hidden');
                mobileMenuButton.classList.remove('active');
                
                // Reset hamburger
                const spans = mobileMenuButton.querySelectorAll('span');
                if (spans.length === 3) {
                    spans[0].style.transform = '';
                    spans[1].style.opacity = '1';
                    spans[2].style.transform = '';
                }
                
                document.body.style.overflow = '';
            }
        }, 250);
    });
});