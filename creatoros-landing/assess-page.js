const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function assessPage() {
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  // Set viewport
  await page.setViewport({ width: 1920, height: 1080 });
  
  // Enable console logging
  page.on('console', msg => console.log('Browser console:', msg.text()));
  page.on('pageerror', error => console.log('Page error:', error.message));
  
  // Load the page
  const filePath = `file://${path.join(__dirname, 'index.html')}`;
  await page.goto(filePath, { waitUntil: 'networkidle0' });
  
  console.log('🔍 Assessing CreatorOS Landing Page...\n');
  
  // Test results object
  const assessment = {
    functionality: [],
    accessibility: [],
    performance: [],
    recommendations: []
  };
  
  // 1. Check for broken links and buttons
  const links = await page.evaluate(() => {
    const allLinks = Array.from(document.querySelectorAll('a'));
    return allLinks.map(link => ({
      text: link.textContent.trim(),
      href: link.href,
      hasClickHandler: link.onclick !== null
    }));
  });
  
  console.log('📎 Links found:', links.length);
  links.forEach(link => {
    if (link.href === '' || link.href.includes('file://')) {
      assessment.functionality.push(`Non-functional link: "${link.text}"`);
    }
  });
  
  // 2. Check buttons functionality
  const buttons = await page.evaluate(() => {
    const allButtons = Array.from(document.querySelectorAll('button'));
    return allButtons.map(btn => ({
      text: btn.textContent.trim(),
      hasClickHandler: btn.onclick !== null,
      type: btn.type
    }));
  });
  
  console.log('🔘 Buttons found:', buttons.length);
  buttons.forEach(btn => {
    if (!btn.hasClickHandler) {
      assessment.functionality.push(`Non-functional button: "${btn.text}"`);
    }
  });
  
  // 3. Check form elements
  const forms = await page.evaluate(() => {
    const allForms = Array.from(document.querySelectorAll('form'));
    return allForms.map(form => ({
      action: form.action,
      method: form.method,
      hasSubmitHandler: form.onsubmit !== null
    }));
  });
  
  if (forms.length > 0) {
    forms.forEach(form => {
      if (!form.hasSubmitHandler && (!form.action || form.action.includes('file://'))) {
        assessment.functionality.push('Form without proper action/handler');
      }
    });
  }
  
  // 4. Check images
  const images = await page.evaluate(() => {
    const allImages = Array.from(document.querySelectorAll('img'));
    return allImages.map(img => ({
      src: img.src,
      alt: img.alt,
      naturalWidth: img.naturalWidth
    }));
  });
  
  images.forEach(img => {
    if (!img.alt) {
      assessment.accessibility.push(`Image missing alt text: ${img.src}`);
    }
    if (img.naturalWidth === 0) {
      assessment.functionality.push(`Broken image: ${img.src}`);
    }
  });
  
  // 5. Check mobile menu
  const hasMobileMenu = await page.evaluate(() => {
    return document.querySelector('[data-mobile-menu]') !== null ||
           document.querySelector('.mobile-menu') !== null ||
           document.querySelector('#mobile-menu') !== null;
  });
  
  if (!hasMobileMenu) {
    assessment.functionality.push('No mobile menu implementation found');
  }
  
  // 6. Check scroll behavior
  const smoothScroll = await page.evaluate(() => {
    return document.documentElement.classList.contains('scroll-smooth');
  });
  
  console.log('📜 Smooth scroll:', smoothScroll ? '✅' : '❌');
  
  // 7. Test responsive design
  const viewports = [
    { width: 375, height: 667, device: 'Mobile' },
    { width: 768, height: 1024, device: 'Tablet' },
    { width: 1920, height: 1080, device: 'Desktop' }
  ];
  
  for (const viewport of viewports) {
    await page.setViewport(viewport);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const overflow = await page.evaluate(() => {
      return document.body.scrollWidth > window.innerWidth;
    });
    
    if (overflow) {
      assessment.performance.push(`Horizontal overflow on ${viewport.device}`);
    }
  }
  
  // 8. Check for interactive elements
  const interactiveElements = await page.evaluate(() => {
    const selectors = [
      'input[type="email"]',
      'select',
      'textarea',
      '[data-tab]',
      '[data-modal]',
      '.accordion',
      '.carousel'
    ];
    
    const found = {};
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        found[selector] = elements.length;
      }
    });
    
    return found;
  });
  
  console.log('\n🎮 Interactive elements:', interactiveElements);
  
  // Generate recommendations
  if (assessment.functionality.length > 0) {
    assessment.recommendations.push('Add JavaScript functionality to handle user interactions');
  }
  
  if (!hasMobileMenu) {
    assessment.recommendations.push('Implement mobile navigation menu');
  }
  
  if (Object.keys(interactiveElements).length === 0) {
    assessment.recommendations.push('Add more interactive elements for better engagement');
  }
  
  // Close browser
  await browser.close();
  
  // Output assessment
  console.log('\n📊 ASSESSMENT RESULTS:');
  console.log('====================\n');
  
  console.log('❌ Functionality Issues:');
  assessment.functionality.forEach(issue => console.log(`  - ${issue}`));
  
  console.log('\n♿ Accessibility Issues:');
  assessment.accessibility.forEach(issue => console.log(`  - ${issue}`));
  
  console.log('\n⚡ Performance Issues:');
  assessment.performance.forEach(issue => console.log(`  - ${issue}`));
  
  console.log('\n💡 Recommendations:');
  assessment.recommendations.forEach(rec => console.log(`  - ${rec}`));
  
  return assessment;
}

// Run assessment
assessPage().catch(console.error);