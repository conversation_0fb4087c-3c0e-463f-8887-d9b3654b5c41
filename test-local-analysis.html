<!DOCTYPE html>
<html>
<head>
    <title>Test Local Analysis</title>
    <script type="module">
        // Test if we can call local analysis directly
        async function testLocalAnalysis() {
            const testPath = "/Users/<USER>/Downloads/test-video.mp4"; // Update this path
            console.log('Testing local analysis for:', testPath);
            
            try {
                // Test 1: Get metadata
                console.log('1. Testing get_media_metadata...');
                const metadata = await window.__TAURI__.invoke('get_media_metadata', {
                    filePath: testPath
                });
                console.log('✅ Metadata:', metadata);
                
                // Test 2: Analyze content locally
                console.log('\n2. Testing analyze_content_local...');
                const analysis = await window.__TAURI__.invoke('analyze_content_local', {
                    filePath: testPath
                });
                console.log('✅ Local analysis:', analysis);
                
                // Test 3: Detect scenes
                console.log('\n3. Testing detect_scenes_local...');
                const scenes = await window.__TAURI__.invoke('detect_scenes_local', {
                    filePath: testPath
                });
                console.log('✅ Scenes:', scenes);
                
            } catch (error) {
                console.error('❌ Error:', error);
            }
        }
        
        // Wait for Tauri
        let checkCount = 0;
        const checkTauri = setInterval(() => {
            checkCount++;
            if (window.__TAURI__) {
                clearInterval(checkTauri);
                console.log('Tauri loaded after', checkCount, 'checks');
                testLocalAnalysis();
            } else if (checkCount > 100) {
                clearInterval(checkTauri);
                console.error('Tauri not available');
            }
        }, 100);
    </script>
</head>
<body>
    <h1>Testing Local AI Analysis</h1>
    <p>Check the console for results</p>
    <p>Make sure to update the test video path in the script!</p>
</body>
</html>