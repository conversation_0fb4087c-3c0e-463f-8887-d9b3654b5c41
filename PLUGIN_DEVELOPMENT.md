# FlowDownload Plugin Development Guide

## 🧩 Plugin System Overview

FlowDownload's plugin system allows developers to extend the application's functionality by creating custom plugins for new platforms, enhanced features, and specialized workflows.

## 🚀 Quick Start

### 1. Plugin Structure

A FlowDownload plugin is a JavaScript module that exports an object implementing the `PluginInstance` interface:

```javascript
const plugin = {
  // Required methods
  supports: async (url) => { /* return boolean */ },
  extract: async (url) => { /* return MediaInfo */ },
  
  // Optional methods
  download: async (info) => { /* return DownloadStream */ },
  upload: async (media, platform) => { /* return UploadResult */ },
  
  // Lifecycle hooks
  onInstall: async () => { /* setup */ },
  onUninstall: async () => { /* cleanup */ },
  onEnable: async () => { /* activate */ },
  onDisable: async () => { /* deactivate */ },
  
  // Configuration
  getConfig: () => { /* return config object */ },
  setConfig: async (config) => { /* save config */ }
};

module.exports = plugin;
```

### 2. Plugin Manifest

Each plugin requires a manifest describing its metadata:

```json
{
  "name": "my-awesome-plugin",
  "version": "1.0.0",
  "author": "Your Name",
  "description": "An awesome plugin for FlowDownload",
  "homepage": "https://github.com/yourname/awesome-plugin",
  "license": "MIT",
  "keywords": ["platform", "feature"],
  "main": "index.js",
  "permissions": [
    {
      "type": "network",
      "description": "Access external APIs",
      "required": true
    }
  ],
  "platforms": ["custom"]
}
```

### 3. Available APIs

Plugins have access to a comprehensive API through the `api` global:

```javascript
// Download Management
await api.download.create(url, options);
await api.download.pause(id);
await api.download.resume(id);
await api.download.cancel(id);

// Upload Management (coming soon)
await api.upload.create(file, platform, options);

// Persistent Storage
await api.storage.set('key', value);
const value = await api.storage.get('key');

// User Interface
api.ui.showNotification('Hello!', 'success');
api.ui.addMenuItem({ id: 'test', label: 'Test', action: () => {} });

// System Integration
api.system.openExternal('https://example.com');
api.system.showInFolder('/path/to/file');

// Network Access
const response = await api.network.fetch('https://api.example.com');
```

## 📝 Example Plugins

### Simple URL Pattern Plugin

```javascript
const simplePlugin = {
  supports: async (url) => {
    return url.includes('example.com');
  },
  
  extract: async (url) => {
    return {
      id: Date.now().toString(),
      title: 'Example Download',
      description: 'Downloaded from example.com',
      formats: [{
        id: 'original',
        quality: 'original',
        format: 'mp4',
        url: url
      }],
      metadata: {
        platform: 'example',
        originalUrl: url
      }
    };
  }
};
```

### Advanced Platform Plugin

See `/examples/sample-plugin.js` for a comprehensive example with:
- Multi-platform support
- Custom UI elements
- Configuration management
- Error handling
- Lifecycle management

## 🔧 Development Workflow

### 1. Local Development

Create your plugin files:
```bash
mkdir my-plugin
cd my-plugin
touch index.js manifest.json
```

### 2. Testing

Load your plugin for testing:
```javascript
// In FlowDownload console
const { pluginRegistry } = require('./src/plugins');
const pluginPackage = {
  manifest: /* your manifest */,
  code: /* your plugin code as string */
};
await pluginRegistry.install(pluginPackage);
await pluginRegistry.enable('my-plugin');
```

### 3. Packaging

Create a plugin package:
```json
{
  "manifest": { /* manifest.json content */ },
  "code": "/* index.js content as string */",
  "assets": { /* optional binary assets */ }
}
```

## 🛡️ Security & Best Practices

### Security Considerations

1. **Sandboxed Execution**: Plugins run in a restricted environment
2. **Permission System**: Declare required permissions in manifest
3. **API Restrictions**: Limited access to system resources
4. **Input Validation**: Always validate URLs and user input

### Best Practices

1. **Error Handling**: Use try-catch blocks for all async operations
2. **Resource Cleanup**: Implement proper cleanup in lifecycle hooks
3. **User Feedback**: Provide clear notifications and progress updates
4. **Configuration**: Make plugins configurable for different use cases
5. **Documentation**: Include clear usage instructions

### Example Error Handling

```javascript
const plugin = {
  extract: async (url) => {
    try {
      const response = await api.network.fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return processData(data);
      
    } catch (error) {
      api.ui.showNotification(`Failed to extract: ${error.message}`, 'error');
      throw error;
    }
  }
};
```

## 📚 API Reference

### MediaInfo Object

```typescript
interface MediaInfo {
  id: string;                    // Unique identifier
  title: string;                 // Display title
  description?: string;          // Optional description
  thumbnail?: string;            // Preview image URL
  duration?: number;             // Duration in seconds
  formats: MediaFormat[];        // Available download formats
  subtitles?: SubtitleTrack[];   // Available subtitles
  metadata: Record<string, any>; // Additional metadata
}
```

### MediaFormat Object

```typescript
interface MediaFormat {
  id: string;          // Format identifier
  quality: string;     // Quality label (e.g., "1080p", "high")
  format: string;      // File format (e.g., "mp4", "webm")
  url: string;         // Download URL
  filesize?: number;   // File size in bytes
  bitrate?: number;    // Bitrate in kbps
  resolution?: string; // Video resolution (e.g., "1920x1080")
  fps?: number;        // Frames per second
}
```

### Plugin Permissions

Available permission types:
- `network`: Internet access for API calls
- `filesystem`: Local file system access
- `shell`: Execute shell commands
- `notifications`: Show system notifications

## 🎯 Plugin Ideas

### Platform Support
- **Streaming**: Netflix, Hulu, Disney+ (where legally permitted)
- **Educational**: Coursera, Udemy, Khan Academy
- **Professional**: LinkedIn Learning, Pluralsight
- **Gaming**: Twitch clips, YouTube Gaming

### Enhancement Features
- **AI Processing**: Auto-transcription, content analysis
- **Quality Enhancement**: Upscaling, noise reduction
- **Format Conversion**: Advanced codec support
- **Metadata Enhancement**: Auto-tagging, categorization

### Workflow Automation
- **Batch Processing**: Playlist downloaders
- **Cloud Integration**: Auto-upload to cloud storage
- **Social Sharing**: Direct posting to social media
- **Content Management**: Library organization

## 🚀 Publishing

### Plugin Marketplace (Coming Soon)

1. **Submit Plugin**: Upload to marketplace
2. **Review Process**: Security and quality review
3. **Distribution**: Available to all users
4. **Updates**: Automatic update distribution

### Manual Distribution

1. **GitHub Releases**: Host plugin packages
2. **Direct Installation**: Users install via URL
3. **Community Sharing**: Share in forums/Discord

## 🔍 Debugging

### Console Logging

```javascript
console.log('Plugin loaded:', manifest.name);
console.error('Error occurred:', error);
```

### Storage Inspection

```javascript
// Check plugin storage
const config = await api.storage.get('plugin_config');
console.log('Current config:', config);
```

### Network Debugging

```javascript
// Log network requests
const response = await api.network.fetch(url);
console.log('Response status:', response.status);
console.log('Response headers:', response.headers);
```

## 📞 Support

- **Documentation**: Check this guide and API docs
- **Examples**: Study the sample plugins in `/examples/`
- **Community**: Join the FlowDownload Discord server
- **Issues**: Report bugs on GitHub

---

Happy plugin development! 🎉