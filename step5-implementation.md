# Step 5: Application Services Implementation

## File 1: src/application/services/DownloadApplicationService.ts

```typescript
import { DownloadRepository } from '../../domain/repositories';
import { DownloadDomainService } from '../../domain/services/DownloadDomainService';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export interface DownloadProgressEvent {
  downloadId: string;
  bytesDownloaded: number;
  totalBytes: number;
  speedBytesPerSecond: number;
}

export interface DownloadCompletedEvent {
  downloadId: string;
  filePath: string;
}

export interface DownloadFailedEvent {
  downloadId: string;
  error: string;
}

export type DownloadEvent = 
  | { type: 'progress'; data: DownloadProgressEvent }
  | { type: 'completed'; data: DownloadCompletedEvent }
  | { type: 'failed'; data: DownloadFailedEvent };

export interface EventEmitter {
  emit(event: DownloadEvent): void;
}

export interface DownloadEngine {
  startDownload(id: string, url: string, filePath: string): Promise<void>;
  pauseDownload(id: string): Promise<void>;
  resumeDownload(id: string): Promise<void>;
  cancelDownload(id: string): Promise<void>;
}

export class DownloadApplicationService {
  constructor(
    private readonly repository: DownloadRepository,
    private readonly eventEmitter: EventEmitter,
    private readonly downloadEngine: DownloadEngine
  ) {}

  async createDownload(
    url: string, 
    filename: string, 
    quality: string, 
    downloadPath: string
  ): Promise<string> {
    // Validate request using domain service
    const fullPath = `${downloadPath}/${filename}`;
    DownloadDomainService.validateDownloadRequest(url, fullPath, quality);

    // Check if we can start another download
    const allDownloads = await this.repository.findAll();
    if (!DownloadDomainService.canDownloadsConcurrently(allDownloads)) {
      throw new Error('Maximum number of concurrent downloads reached');
    }

    // Create domain entity
    const download = Download.create(url, fullPath, quality);
    
    // Save to repository
    await this.repository.save(download);

    // Start download process
    this.startDownloadProcess(download);

    return download.getId().toString();
  }

  async pauseDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    download.pause();
    await this.repository.save(download);
    await this.downloadEngine.pauseDownload(downloadId);
  }

  async resumeDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    // Check concurrent download limit
    const allDownloads = await this.repository.findAll();
    if (!DownloadDomainService.canDownloadsConcurrently(allDownloads)) {
      throw new Error('Maximum number of concurrent downloads reached');
    }

    download.start();
    await this.repository.save(download);
    this.startDownloadProcess(download);
  }

  async cancelDownload(downloadId: string): Promise<void> {
    const id = DownloadId.fromString(downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) {
      throw new Error('Download not found');
    }

    await this.downloadEngine.cancelDownload(downloadId);
    await this.repository.delete(id);
  }

  async getAllDownloads(): Promise<Download[]> {
    return this.repository.findAll();
  }

  async getDownloadsByStatus(status: DownloadStatus): Promise<Download[]> {
    return this.repository.findByStatus(status);
  }

  async clearCompletedDownloads(): Promise<void> {
    await this.repository.deleteByStatus(DownloadStatus.COMPLETED);
  }

  // Event handlers for download engine events
  async handleDownloadProgress(event: DownloadProgressEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.updateProgress(
      event.bytesDownloaded, 
      event.totalBytes, 
      event.speedBytesPerSecond
    );
    
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'progress', data: event });
  }

  async handleDownloadCompleted(event: DownloadCompletedEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.complete();
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'completed', data: event });
  }

  async handleDownloadFailed(event: DownloadFailedEvent): Promise<void> {
    const id = DownloadId.fromString(event.downloadId);
    const download = await this.repository.findById(id);
    
    if (!download) return;

    download.fail(event.error);
    await this.repository.save(download);
    this.eventEmitter.emit({ type: 'failed', data: event });
  }

  private async startDownloadProcess(download: Download): Promise<void> {
    try {
      download.start();
      await this.repository.save(download);
      
      await this.downloadEngine.startDownload(
        download.getId().toString(),
        download.getUrl().getValue(),
        download.getFilePath().getValue()
      );
    } catch (error) {
      download.fail(error instanceof Error ? error.message : 'Unknown error');
      await this.repository.save(download);
    }
  }
}
```

## File 2: src/infrastructure/DownloadEngine.ts

```typescript
import { DownloadEngine } from '../application/services/DownloadApplicationService';
import { isInTauriEnvironment } from '../utils/tauriUtils';

export class TauriDownloadEngine implements DownloadEngine {
  async startDownload(id: string, url: string, filePath: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('download_file', { id, url, file_path: filePath });
  }

  async pauseDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('pause_download', { id });
  }

  async resumeDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('resume_download', { id });
  }

  async cancelDownload(id: string): Promise<void> {
    const { invoke } = await import('@tauri-apps/api/core');
    await invoke('cancel_download', { id });
  }
}

export class MockDownloadEngine implements DownloadEngine {
  private activeDownloads = new Set<string>();

  async startDownload(id: string, url: string, filePath: string): Promise<void> {
    this.activeDownloads.add(id);
    console.log(`Mock download started: ${id}, ${url} -> ${filePath}`);
    
    // Simulate download completion after 2 seconds for testing
    setTimeout(() => {
      this.activeDownloads.delete(id);
      console.log(`Mock download completed: ${id}`);
    }, 2000);
  }

  async pauseDownload(id: string): Promise<void> {
    console.log(`Mock download paused: ${id}`);
  }

  async resumeDownload(id: string): Promise<void> {
    console.log(`Mock download resumed: ${id}`);
  }

  async cancelDownload(id: string): Promise<void> {
    this.activeDownloads.delete(id);
    console.log(`Mock download cancelled: ${id}`);
  }
}

// Factory function
export function createDownloadEngine(): DownloadEngine {
  if (isInTauriEnvironment()) {
    return new TauriDownloadEngine();
  }
  return new MockDownloadEngine();
}
```

## File 3: src/application/services/ApplicationServiceFactory.ts

```typescript
import { DownloadApplicationService, EventEmitter } from './DownloadApplicationService';
import { createDownloadRepository } from '../../infrastructure/repositories';
import { createDownloadEngine } from '../../infrastructure/DownloadEngine';

// Simple event emitter implementation
class SimpleEventEmitter implements EventEmitter {
  private listeners: Array<(event: any) => void> = [];

  emit(event: any): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in event listener:', error);
      }
    });
  }

  addListener(listener: (event: any) => void): void {
    this.listeners.push(listener);
  }

  removeListener(listener: (event: any) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }
}

// Factory to create application service with all dependencies
export function createDownloadApplicationService(): DownloadApplicationService {
  const repository = createDownloadRepository();
  const downloadEngine = createDownloadEngine();
  const eventEmitter = new SimpleEventEmitter();

  return new DownloadApplicationService(
    repository,
    eventEmitter,
    downloadEngine
  );
}

// Export the event emitter for use in other parts of the app
export const globalEventEmitter = new SimpleEventEmitter();

// Create singleton application service
export const downloadApplicationService = createDownloadApplicationService();
```

## File 4: src/hooks/useDomainDownloadStore.ts

```typescript
import { useState, useEffect, useCallback } from 'react';
import { Download, DownloadStatus } from '../domain/models';
import { downloadApplicationService, globalEventEmitter } from '../application/services/ApplicationServiceFactory';

interface DomainDownloadState {
  downloads: Download[];
  loading: boolean;
  error: string | null;
}

export function useDomainDownloadStore() {
  const [state, setState] = useState<DomainDownloadState>({
    downloads: [],
    loading: false,
    error: null
  });

  const refreshDownloads = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      const downloads = await downloadApplicationService.getAllDownloads();
      setState(prev => ({ ...prev, downloads, loading: false }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load downloads',
        loading: false
      }));
    }
  }, []);

  const createDownload = useCallback(async (
    url: string,
    filename: string,
    quality: string,
    downloadPath: string
  ) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      await downloadApplicationService.createDownload(url, filename, quality, downloadPath);
      await refreshDownloads();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create download',
        loading: false
      }));
      throw error;
    }
  }, [refreshDownloads]);

  const pauseDownload = useCallback(async (id: string) => {
    try {
      await downloadApplicationService.pauseDownload(id);
      await refreshDownloads();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to pause download'
      }));
    }
  }, [refreshDownloads]);

  const resumeDownload = useCallback(async (id: string) => {
    try {
      await downloadApplicationService.resumeDownload(id);
      await refreshDownloads();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to resume download'
      }));
    }
  }, [refreshDownloads]);

  const cancelDownload = useCallback(async (id: string) => {
    try {
      await downloadApplicationService.cancelDownload(id);
      await refreshDownloads();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to cancel download'
      }));
    }
  }, [refreshDownloads]);

  const clearCompletedDownloads = useCallback(async () => {
    try {
      await downloadApplicationService.clearCompletedDownloads();
      await refreshDownloads();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to clear completed downloads'
      }));
    }
  }, [refreshDownloads]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Set up event listeners
  useEffect(() => {
    const handleEvent = (event: any) => {
      // Refresh downloads when events occur
      refreshDownloads();
    };

    globalEventEmitter.addListener(handleEvent);
    
    // Initial load
    refreshDownloads();

    return () => {
      globalEventEmitter.removeListener(handleEvent);
    };
  }, [refreshDownloads]);

  return {
    // State
    downloads: state.downloads,
    loading: state.loading,
    error: state.error,

    // Actions
    createDownload,
    pauseDownload,
    resumeDownload,
    cancelDownload,
    clearCompletedDownloads,
    refreshDownloads,
    clearError,

    // Computed values
    activeDownloads: state.downloads.filter(d => d.getStatus() === DownloadStatus.DOWNLOADING),
    completedDownloads: state.downloads.filter(d => d.getStatus() === DownloadStatus.COMPLETED),
    failedDownloads: state.downloads.filter(d => d.getStatus() === DownloadStatus.ERROR),
  };
}
```

## File 5: src/__tests__/application/DownloadApplicationService.test.ts

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DownloadApplicationService, EventEmitter } from '../../application/services/DownloadApplicationService';
import { InMemoryDownloadRepository } from '../../infrastructure/repositories/InMemoryDownloadRepository';
import { MockDownloadEngine } from '../../infrastructure/DownloadEngine';
import { DownloadStatus } from '../../domain/models';

describe('DownloadApplicationService', () => {
  let service: DownloadApplicationService;
  let repository: InMemoryDownloadRepository;
  let downloadEngine: MockDownloadEngine;
  let eventEmitter: EventEmitter;

  beforeEach(() => {
    repository = new InMemoryDownloadRepository();
    downloadEngine = new MockDownloadEngine();
    eventEmitter = { emit: vi.fn() };
    
    service = new DownloadApplicationService(repository, eventEmitter, downloadEngine);
  });

  it('should create download successfully', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    expect(downloadId).toBeDefined();
    
    const downloads = await repository.findAll();
    expect(downloads).toHaveLength(1);
    expect(downloads[0].getStatus()).toBe(DownloadStatus.DOWNLOADING);
  });

  it('should reject invalid download requests', async () => {
    await expect(service.createDownload('', 'video.mp4', '1080p', '/downloads'))
      .rejects.toThrow('URL cannot be empty');
    
    await expect(service.createDownload('https://example.com/video.mp4', '', '1080p', '/downloads'))
      .rejects.toThrow('File path cannot be empty');
  });

  it('should enforce concurrent download limit', async () => {
    // Create 3 downloads (max limit)
    await service.createDownload('https://example.com/video1.mp4', 'video1.mp4', '1080p', '/downloads');
    await service.createDownload('https://example.com/video2.mp4', 'video2.mp4', '1080p', '/downloads');
    await service.createDownload('https://example.com/video3.mp4', 'video3.mp4', '1080p', '/downloads');

    // 4th download should fail
    await expect(service.createDownload('https://example.com/video4.mp4', 'video4.mp4', '1080p', '/downloads'))
      .rejects.toThrow('Maximum number of concurrent downloads reached');
  });

  it('should pause and resume downloads', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    await service.pauseDownload(downloadId);
    
    const downloads = await repository.findAll();
    expect(downloads[0].getStatus()).toBe(DownloadStatus.PAUSED);

    await service.resumeDownload(downloadId);
    expect(downloads[0].getStatus()).toBe(DownloadStatus.DOWNLOADING);
  });

  it('should handle download progress updates', async () => {
    const downloadId = await service.createDownload(
      'https://example.com/video.mp4',
      'video.mp4',
      '1080p',
      '/downloads'
    );

    await service.handleDownloadProgress({
      downloadId,
      bytesDownloaded: 50,
      totalBytes: 100,
      speedBytesPerSecond: 1000
    });

    const downloads = await repository.findAll();
    expect(downloads[0].getProgress().getPercentage()).toBe(50);
    expect(eventEmitter.emit).toHaveBeenCalledWith({
      type: 'progress',
      data: expect.objectContaining({ downloadId })
    });
  });

  it('should clear completed downloads', async () => {
    const downloadId1 = await service.createDownload(
      'https://example.com/video1.mp4',
      'video1.mp4',
      '1080p',
      '/downloads'
    );
    const downloadId2 = await service.createDownload(
      'https://example.com/video2.mp4',
      'video2.mp4',
      '1080p',
      '/downloads'
    );

    // Complete first download
    await service.handleDownloadCompleted({
      downloadId: downloadId1,
      filePath: '/downloads/video1.mp4'
    });

    await service.clearCompletedDownloads();

    const remaining = await repository.findAll();
    expect(remaining).toHaveLength(1);
    expect(remaining[0].getId().toString()).toBe(downloadId2);
  });
});
```

## 🧪 VALIDATION STEP

Run these commands to validate Step 5:

```bash
# Test the application services
npm test src/__tests__/application

# Test everything so far
npm run test:domain
npm test src/__tests__/infrastructure
npm test src/__tests__/migration
npm test src/__tests__/application

# Check TypeScript compilation
npx tsc --noEmit

# Ensure existing app still works
npm run dev
```

## 🔧 Testing the New Domain Hook

To test the new domain-driven hook, you can create a simple test component:

Create `src/components/DomainTestComponent.tsx`:

```typescript
import React from 'react';
import { useDomainDownloadStore } from '../hooks/useDomainDownloadStore';
import { DownloadAdapter } from '../adapters/DownloadAdapter';

export const DomainTestComponent: React.FC = () => {
  const {
    downloads,
    loading,
    error,
    createDownload,
    pauseDownload,
    resumeDownload,
    cancelDownload,
    clearError
  } = useDomainDownloadStore();

  const handleTestDownload = async () => {
    try {
      await createDownload(
        'https://example.com/test.mp4',
        'test.mp4',
        '1080p',
        '/downloads'
      );
    } catch (error) {
      console.error('Failed to create test download:', error);
    }
  };

  return (
    <div className="p-4 border border-blue-200 rounded-lg bg-blue-50">
      <h3 className="text-lg font-semibold text-blue-800">Domain Layer Test</h3>
      
      {error && (
        <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded">
          <p className="text-red-700">{error}</p>
          <button onClick={clearError} className="text-red-600 underline">
            Clear Error
          </button>
        </div>
      )}
      
      <div className="mt-3">
        <button 
          onClick={handleTestDownload}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Creating...' : 'Create Test Download'}
        </button>
      </div>
      
      <div className="mt-4">
        <h4 className="font-medium">Domain Downloads ({downloads.length}):</h4>
        {downloads.map(download => {
          const legacy = DownloadAdapter.toLegacy(download);
          return (
            <div key={legacy.id} className="mt-2 p-2 bg-white border rounded">
              <p><strong>URL:</strong> {legacy.url}</p>
              <p><strong>Status:</strong> {legacy.status}</p>
              <p><strong>Progress:</strong> {legacy.progress}%</p>
              <div className="mt-2 space-x-2">
                <button 
                  onClick={() => pauseDownload(legacy.id)}
                  className="px-2 py-1 bg-yellow-500 text-white rounded text-sm"
                >
                  Pause
                </button>
                <button 
                  onClick={() => resumeDownload(legacy.id)}
                  className="px-2 py-1 bg-green-500 text-white rounded text-sm"
                >
                  Resume
                </button>
                <button 
                  onClick={() => cancelDownload(legacy.id)}
                  className="px-2 py-1 bg-red-500 text-white rounded text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

Add this component to your main app to test the domain layer:

```typescript
// In src/App.tsx, add this import and component
import { DomainTestComponent } from './components/DomainTestComponent';

// Add this somewhere in your JSX (maybe in a development section)
{process.env.NODE_ENV === 'development' && <DomainTestComponent />}
```

✅ **Success Criteria**: All application service tests pass, domain hook works, existing app still functions.

**Once this step is complete, proceed to Step 6 (Store Migration).**
