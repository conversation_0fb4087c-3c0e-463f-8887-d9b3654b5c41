# Step 3: Infrastructure Layer Implementation

## File 1: src/infrastructure/repositories/InMemoryDownloadRepository.ts

```typescript
import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class InMemoryDownloadRepository implements DownloadRepository {
  private downloads = new Map<string, Download>();

  async save(download: Download): Promise<void> {
    this.downloads.set(download.getId().toString(), download);
  }

  async findById(id: DownloadId): Promise<Download | null> {
    return this.downloads.get(id.toString()) || null;
  }

  async findAll(): Promise<Download[]> {
    return Array.from(this.downloads.values());
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    return Array.from(this.downloads.values())
      .filter(download => download.getStatus() === status);
  }

  async delete(id: DownloadId): Promise<void> {
    this.downloads.delete(id.toString());
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    const toDelete = await this.findByStatus(status);
    toDelete.forEach(download => {
      this.downloads.delete(download.getId().toString());
    });
  }
}
```

## File 2: src/infrastructure/repositories/LocalStorageDownloadRepository.ts

```typescript
import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class LocalStorageDownloadRepository implements DownloadRepository {
  private readonly storageKey = 'flowdownload_downloads_v2'; // v2 to avoid conflicts

  private async getStoredDownloads(): Promise<Download[]> {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) return [];
      
      const dtos = JSON.parse(stored);
      return dtos.map((dto: any) => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error reading downloads from localStorage:', error);
      return [];
    }
  }

  private async saveDownloads(downloads: Download[]): Promise<void> {
    try {
      const dtos = downloads.map(download => download.toDTO());
      localStorage.setItem(this.storageKey, JSON.stringify(dtos));
    } catch (error) {
      console.error('Error saving downloads to localStorage:', error);
      throw new Error('Failed to save downloads');
    }
  }

  async save(download: Download): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const existingIndex = downloads.findIndex(d => 
      d.getId().equals(download.getId())
    );

    if (existingIndex >= 0) {
      downloads[existingIndex] = download;
    } else {
      downloads.push(download);
    }

    await this.saveDownloads(downloads);
  }

  async findById(id: DownloadId): Promise<Download | null> {
    const downloads = await this.getStoredDownloads();
    return downloads.find(download => download.getId().equals(id)) || null;
  }

  async findAll(): Promise<Download[]> {
    return this.getStoredDownloads();
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    const downloads = await this.getStoredDownloads();
    return downloads.filter(download => download.getStatus() === status);
  }

  async delete(id: DownloadId): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const filtered = downloads.filter(download => !download.getId().equals(id));
    await this.saveDownloads(filtered);
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    const downloads = await this.getStoredDownloads();
    const filtered = downloads.filter(download => download.getStatus() !== status);
    await this.saveDownloads(filtered);
  }
}
```

## File 3: src/infrastructure/repositories/TauriDownloadRepository.ts

```typescript
import { DownloadRepository } from '../../domain/repositories';
import { Download, DownloadId, DownloadStatus } from '../../domain/models';

export class TauriDownloadRepository implements DownloadRepository {
  async save(download: Download): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dto = download.toDTO();
      await invoke('save_download', { download: dto });
    } catch (error) {
      throw new Error(`Failed to save download: ${error}`);
    }
  }

  async findById(id: DownloadId): Promise<Download | null> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dto = await invoke<any>('find_download_by_id', { id: id.toString() });
      return dto ? Download.fromDTO(dto) : null;
    } catch (error) {
      console.error('Error finding download by id:', error);
      return null;
    }
  }

  async findAll(): Promise<Download[]> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dtos = await invoke<any[]>('find_all_downloads');
      return dtos.map(dto => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error finding all downloads:', error);
      return [];
    }
  }

  async findByStatus(status: DownloadStatus): Promise<Download[]> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      const dtos = await invoke<any[]>('find_downloads_by_status', { status });
      return dtos.map(dto => Download.fromDTO(dto));
    } catch (error) {
      console.error('Error finding downloads by status:', error);
      return [];
    }
  }

  async delete(id: DownloadId): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('delete_download', { id: id.toString() });
    } catch (error) {
      throw new Error(`Failed to delete download: ${error}`);
    }
  }

  async deleteByStatus(status: DownloadStatus): Promise<void> {
    try {
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('delete_downloads_by_status', { status });
    } catch (error) {
      throw new Error(`Failed to delete downloads by status: ${error}`);
    }
  }
}
```

## File 4: src/infrastructure/repositories/index.ts

```typescript
// Factory function to create appropriate repository based on environment
import { DownloadRepository } from '../../domain/repositories';
import { InMemoryDownloadRepository } from './InMemoryDownloadRepository';
import { LocalStorageDownloadRepository } from './LocalStorageDownloadRepository';
import { TauriDownloadRepository } from './TauriDownloadRepository';
import { isInTauriEnvironment } from '../../utils/tauriUtils';

export function createDownloadRepository(): DownloadRepository {
  if (typeof window === 'undefined') {
    // Server-side rendering or testing
    return new InMemoryDownloadRepository();
  }
  
  if (isInTauriEnvironment()) {
    // Desktop app with Tauri backend
    return new TauriDownloadRepository();
  }
  
  // Web browser
  return new LocalStorageDownloadRepository();
}

export { InMemoryDownloadRepository, LocalStorageDownloadRepository, TauriDownloadRepository };
```

## File 5: src/__tests__/infrastructure/repositories.test.ts

```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { InMemoryDownloadRepository } from '../../infrastructure/repositories/InMemoryDownloadRepository';
import { Download, DownloadStatus } from '../../domain/models';

describe('InMemoryDownloadRepository', () => {
  let repository: InMemoryDownloadRepository;
  let download1: Download;
  let download2: Download;

  beforeEach(() => {
    repository = new InMemoryDownloadRepository();
    download1 = Download.create('https://example.com/file1.mp4', '/downloads/file1.mp4', '1080p');
    download2 = Download.create('https://example.com/file2.mp4', '/downloads/file2.mp4', '720p');
  });

  it('should save and find download by ID', async () => {
    await repository.save(download1);
    
    const found = await repository.findById(download1.getId());
    expect(found).toBe(download1);
  });

  it('should return null for non-existent download', async () => {
    const found = await repository.findById(download1.getId());
    expect(found).toBeNull();
  });

  it('should find all downloads', async () => {
    await repository.save(download1);
    await repository.save(download2);
    
    const all = await repository.findAll();
    expect(all).toHaveLength(2);
    expect(all).toContain(download1);
    expect(all).toContain(download2);
  });

  it('should find downloads by status', async () => {
    download1.start();
    download2.start();
    download1.complete();
    
    await repository.save(download1);
    await repository.save(download2);
    
    const completed = await repository.findByStatus(DownloadStatus.COMPLETED);
    const downloading = await repository.findByStatus(DownloadStatus.DOWNLOADING);
    
    expect(completed).toHaveLength(1);
    expect(completed[0]).toBe(download1);
    expect(downloading).toHaveLength(1);
    expect(downloading[0]).toBe(download2);
  });

  it('should delete download by ID', async () => {
    await repository.save(download1);
    await repository.save(download2);
    
    await repository.delete(download1.getId());
    
    const remaining = await repository.findAll();
    expect(remaining).toHaveLength(1);
    expect(remaining[0]).toBe(download2);
  });
});
```

## 🧪 VALIDATION STEP

Run these commands to validate Step 3:

```bash
# Test the infrastructure layer
npm test src/__tests__/infrastructure

# Test everything so far
npm run test:domain
npm test src/__tests__/infrastructure

# Check TypeScript compilation
npx tsc --noEmit

# Ensure existing app still works
npm run dev
```

✅ **Success Criteria**: All infrastructure tests pass, existing app still works unchanged.

**Once this step is complete, proceed to Step 4 (Adapter Layer).**
