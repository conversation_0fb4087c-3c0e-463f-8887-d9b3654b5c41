// Sample FlowDownload Plugin - Enhanced Social Media Downloader
// This is an example plugin that demonstrates the plugin system capabilities

const plugin = {
  // Plugin lifecycle hooks
  onInstall: async () => {
    console.log('Enhanced Social Media Plugin installed');
    // Initialize plugin-specific storage
    await api.storage.set('installation_date', new Date().toISOString());
    api.ui.showNotification('Enhanced Social Media Plugin installed successfully!', 'success');
  },
  
  onEnable: async () => {
    console.log('Enhanced Social Media Plugin enabled');
    
    // Add custom menu items
    api.ui.addMenuItem({
      id: 'bulk_social_download',
      label: 'Bulk Social Media Download',
      icon: '📱',
      action: () => {
        api.ui.showNotification('Opening bulk download dialog...', 'info');
        // In a real plugin, this would open a custom modal
      }
    });
    
    api.ui.addMenuItem({
      id: 'separator_1',
      label: '',
      separator: true
    });
    
    api.ui.addMenuItem({
      id: 'plugin_settings',
      label: 'Plugin Settings',
      icon: '⚙️',
      action: () => {
        api.ui.showNotification('Opening plugin settings...', 'info');
      }
    });
  },
  
  onDisable: async () => {
    console.log('Enhanced Social Media Plugin disabled');
    // Clean up menu items
    api.ui.removeMenuItem('bulk_social_download');
    api.ui.removeMenuItem('separator_1');
    api.ui.removeMenuItem('plugin_settings');
  },
  
  // Core plugin functionality
  supports: async (url) => {
    const socialPlatforms = [
      'youtube.com', 'youtu.be',
      'instagram.com', 'instagr.am',
      'tiktok.com',
      'twitter.com', 'x.com',
      'facebook.com', 'fb.com',
      'reddit.com',
      'twitch.tv'
    ];
    
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      return socialPlatforms.some(platform => 
        hostname.includes(platform) || hostname.includes(platform.replace('.com', ''))
      );
    } catch {
      return false;
    }
  },
  
  extract: async (url) => {
    console.log('Extracting media info from:', url);
    
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // Determine platform
      let platform = 'unknown';
      let extractor = 'generic';
      
      if (hostname.includes('youtube')) {
        platform = 'youtube';
        extractor = 'youtube-dl';
        return await extractYouTubeInfo(url);
      } else if (hostname.includes('instagram')) {
        platform = 'instagram';
        extractor = 'gallery-dl';
        return await extractInstagramInfo(url);
      } else if (hostname.includes('tiktok')) {
        platform = 'tiktok';
        extractor = 'yt-dlp';
        return await extractTikTokInfo(url);
      } else if (hostname.includes('twitter') || hostname.includes('x.com')) {
        platform = 'twitter';
        extractor = 'yt-dlp';
        return await extractTwitterInfo(url);
      } else if (hostname.includes('reddit')) {
        platform = 'reddit';
        extractor = 'gallery-dl';
        return await extractRedditInfo(url);
      }
      
      // Generic extraction for other platforms
      return await extractGenericInfo(url);
      
    } catch (error) {
      throw new Error(`Failed to extract media info: ${error.message}`);
    }
  },
  
  // Plugin configuration
  getConfig: () => {
    return {
      quality_preference: 'high',
      include_subtitles: true,
      thumbnail_download: true,
      metadata_extraction: true,
      batch_size: 5,
      retry_attempts: 3
    };
  },
  
  setConfig: async (newConfig) => {
    const currentConfig = plugin.getConfig();
    const updatedConfig = { ...currentConfig, ...newConfig };
    await api.storage.set('plugin_config', updatedConfig);
    api.ui.showNotification('Configuration updated successfully', 'success');
  }
};

// Platform-specific extraction functions
async function extractYouTubeInfo(url) {
  const videoId = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/)?.[1];
  
  if (!videoId) {
    throw new Error('Invalid YouTube URL');
  }
  
  // In a real plugin, this would use the YouTube API or youtube-dl
  return {
    id: videoId,
    title: `YouTube Video ${videoId}`,
    description: 'Downloaded with Enhanced Social Media Plugin',
    thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
    duration: 300, // 5 minutes
    formats: [
      {
        id: '360p',
        quality: '360p',
        format: 'mp4',
        url: url,
        resolution: '640x360',
        filesize: 50 * 1024 * 1024 // 50MB
      },
      {
        id: '720p',
        quality: '720p',
        format: 'mp4',
        url: url,
        resolution: '1280x720',
        filesize: 120 * 1024 * 1024 // 120MB
      },
      {
        id: '1080p',
        quality: '1080p',
        format: 'mp4',
        url: url,
        resolution: '1920x1080',
        filesize: 200 * 1024 * 1024 // 200MB
      }
    ],
    subtitles: [
      {
        language: 'en',
        label: 'English',
        url: `https://example.com/subtitles/${videoId}_en.srt`,
        format: 'srt'
      }
    ],
    metadata: {
      platform: 'youtube',
      uploader: 'Sample Channel',
      upload_date: '2024-01-15',
      view_count: 1000000,
      like_count: 50000,
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

async function extractInstagramInfo(url) {
  const postId = url.match(/\/p\/([a-zA-Z0-9_-]+)/)?.[1] || 
                 url.match(/\/reel\/([a-zA-Z0-9_-]+)/)?.[1];
  
  if (!postId) {
    throw new Error('Invalid Instagram URL');
  }
  
  return {
    id: postId,
    title: `Instagram Post ${postId}`,
    description: 'Instagram content downloaded with Enhanced Plugin',
    thumbnail: `https://example.com/ig_thumb_${postId}.jpg`,
    formats: [
      {
        id: 'original',
        quality: 'original',
        format: 'mp4',
        url: url,
        resolution: '1080x1080'
      }
    ],
    metadata: {
      platform: 'instagram',
      post_type: url.includes('/reel/') ? 'reel' : 'post',
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

async function extractTikTokInfo(url) {
  const videoId = url.match(/\/video\/(\d+)/)?.[1] ||
                  url.match(/@[\w.]+\/video\/(\d+)/)?.[1];
  
  if (!videoId) {
    throw new Error('Invalid TikTok URL');
  }
  
  return {
    id: videoId,
    title: `TikTok Video ${videoId}`,
    description: 'TikTok content downloaded with Enhanced Plugin',
    thumbnail: `https://example.com/tiktok_thumb_${videoId}.jpg`,
    duration: 60, // 1 minute typical TikTok length
    formats: [
      {
        id: 'mobile',
        quality: 'mobile',
        format: 'mp4',
        url: url,
        resolution: '540x960'
      },
      {
        id: 'hd',
        quality: 'hd',
        format: 'mp4',
        url: url,
        resolution: '720x1280'
      }
    ],
    metadata: {
      platform: 'tiktok',
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

async function extractTwitterInfo(url) {
  const tweetId = url.match(/status\/(\d+)/)?.[1];
  
  if (!tweetId) {
    throw new Error('Invalid Twitter URL');
  }
  
  return {
    id: tweetId,
    title: `Twitter Video ${tweetId}`,
    description: 'Twitter content downloaded with Enhanced Plugin',
    formats: [
      {
        id: 'twitter_video',
        quality: 'original',
        format: 'mp4',
        url: url
      }
    ],
    metadata: {
      platform: 'twitter',
      tweet_id: tweetId,
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

async function extractRedditInfo(url) {
  const postId = url.match(/comments\/([a-zA-Z0-9]+)/)?.[1];
  
  if (!postId) {
    throw new Error('Invalid Reddit URL');
  }
  
  return {
    id: postId,
    title: `Reddit Post ${postId}`,
    description: 'Reddit content downloaded with Enhanced Plugin',
    formats: [
      {
        id: 'reddit_video',
        quality: 'original',
        format: 'mp4',
        url: url
      }
    ],
    metadata: {
      platform: 'reddit',
      post_id: postId,
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

async function extractGenericInfo(url) {
  const filename = url.split('/').pop() || 'social_media_content';
  
  return {
    id: `generic_${Date.now()}`,
    title: filename,
    description: 'Generic social media content',
    formats: [
      {
        id: 'original',
        quality: 'original',
        format: 'mp4',
        url: url
      }
    ],
    metadata: {
      platform: 'generic',
      original_url: url,
      extractor: 'enhanced-social-media-plugin'
    }
  };
}

// Export the plugin
if (typeof module !== 'undefined' && module.exports) {
  module.exports = plugin;
} else {
  window.enhancedSocialMediaPlugin = plugin;
}

// Plugin manifest (would typically be in a separate file)
const manifest = {
  name: 'enhanced-social-media',
  version: '1.2.0',
  author: 'FlowDownload Community',
  description: 'Enhanced social media downloader with advanced features and multi-platform support',
  homepage: 'https://github.com/flowdownload/plugins/enhanced-social-media',
  license: 'MIT',
  keywords: ['social-media', 'youtube', 'instagram', 'tiktok', 'twitter', 'reddit', 'enhanced'],
  main: 'index.js',
  permissions: [
    { type: 'network', description: 'Access social media platforms for content extraction', required: true },
    { type: 'filesystem', description: 'Save downloaded content and metadata', required: true },
    { type: 'notifications', description: 'Show download progress and completion notifications', required: false }
  ],
  platforms: ['youtube', 'instagram', 'tiktok', 'twitter', 'reddit', 'custom']
};

console.log('Enhanced Social Media Plugin loaded with manifest:', manifest);