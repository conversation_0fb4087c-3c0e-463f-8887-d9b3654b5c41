# .cursorrules - Cursor Project Configuration for FlowDownload

project:
  name: FlowDownload
  description: "Modern, clean, sophisticated desktop downloader app"
  version: 1.0.0
  language: rust
  framework: tauri-react-ts-tailwind
  license: MIT

ai:
  # AI coding assistant settings
  enable_ai: true
  ai_model: gpt-4
  auto_suggest: true
  suggest_on_save: false
  max_tokens: 512
  temperature: 0.7
  prompt_prefix: |
    You are a full-stack software architect working on FlowDownload,
    a modern desktop downloader app built with Tauri, Rust, React, and TailwindCSS.
    Your task is to help implement features while following best practices.

  rules:
    - Prefer functional components over class-based ones in React
    - Always use Zustand or Redux for global state management
    - Format code using Prettier and ESLint
    - Follow Rust idioms and safety patterns
    - Use descriptive variable names
    - Add doc comments to all public functions
    - Avoid unsafe Rust unless performance-critical
    - Keep UI modular and reusable
    - Ensure cross-platform compatibility
    - Prioritize security and user privacy

  suggestions:
    - Suggest Tailwind utility classes for styling
    - Recommend Rust crates for common tasks (e.g., reqwest, dirs-next)
    - Provide Tauri command examples
    - Suggest Zustand store structures
    - Warn about platform-specific binaries (e.g., yt-dlp.exe) 