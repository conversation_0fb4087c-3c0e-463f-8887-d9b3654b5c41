# Quality & Format Selection Test Plan

## Test Cases

### 1. Video Quality Tests
- [ ] Test downloading a YouTube video in 1080p MP4
- [ ] Test downloading in 720p MP4
- [ ] Test downloading in 480p MP4
- [ ] Test downloading in "Best Quality Available"

### 2. Audio Format Tests
- [ ] Test downloading audio only in MP3 (High Quality)
- [ ] Test downloading audio only in M4A (Medium Quality)
- [ ] Test downloading audio only in FLAC

### 3. Video Format Tests
- [ ] Test downloading video in MKV format
- [ ] Test downloading video in WebM format
- [ ] Test downloading video in MOV format

### 4. Edge Cases
- [ ] Test with non-YouTube URL (e.g., Twitter/X video)
- [ ] Test with search term instead of URL
- [ ] Test quality selection that's not available (should fallback)

## Expected Results

1. **Quality Selection**: 
   - The downloaded file should match the selected quality (verify with file properties)
   - yt-dlp logs should show the correct format string being used

2. **Format Selection**:
   - Files should have the correct extension
   - Audio-only selections should extract audio properly
   - Video formats should maintain quality settings

3. **Backend Logs**:
   - Should see "📐 Built format string: ..." in console
   - Should see quality and format parameters passed to download_file

## Test URLs
- YouTube: https://www.youtube.com/watch?v=dQw4w9WgXcQ (short video for quick tests)
- Twitter/X: Any recent video tweet
- Search term: "test video 4k"