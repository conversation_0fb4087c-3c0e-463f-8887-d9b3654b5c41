# TikTok API Integration Guide

## Overview

This guide covers integrating TikTok's API for video uploads in FlowDownload. TikTok provides APIs for content creation, but the process is more restricted than YouTube's API.

## API Access Requirements

### 1. Developer Account
- Register at [TikTok for Developers](https://developers.tiktok.com/)
- Verify your email and phone number
- Complete developer profile

### 2. App Registration
- Create a new app in the developer console
- Select "Web" as platform type
- Add required scopes:
  - `video.upload` - Upload videos
  - `video.publish` - Publish videos
  - `user.info.basic` - Get user information

### 3. Business Verification (Required for Production)
- Submit business documentation
- Provide app description and use case
- Wait for approval (can take 1-2 weeks)

## API Limitations

### Content Restrictions
- Videos must be between 3 seconds and 10 minutes
- Maximum file size: 4GB
- Supported formats: MP4, MOV, MPEG, 3GP, AVI
- Aspect ratios: 9:16 (recommended), 1:1, 16:9

### Rate Limits
- 100 requests per minute
- 1000 videos per day
- OAuth tokens expire after 24 hours

## Authentication Flow

### OAuth 2.0 Implementation

```rust
// TikTok OAuth configuration
pub struct TikTokConfig {
    pub client_key: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_url: String,
    pub token_url: String,
}

impl TikTokConfig {
    pub fn new() -> Self {
        Self {
            client_key: env::var("TIKTOK_CLIENT_KEY").unwrap(),
            client_secret: env::var("TIKTOK_CLIENT_SECRET").unwrap(),
            redirect_uri: "http://localhost:1420/auth/tiktok/callback",
            auth_url: "https://www.tiktok.com/auth/authorize/",
            token_url: "https://open-api.tiktok.com/oauth/access_token/",
        }
    }
}
```

### Authorization URL
```
https://www.tiktok.com/auth/authorize/
  ?client_key={CLIENT_KEY}
  &scope=user.info.basic,video.upload,video.publish
  &response_type=code
  &redirect_uri={REDIRECT_URI}
  &state={STATE}
```

### Token Exchange
```rust
pub async fn exchange_code_for_token(
    code: String,
    client_key: String,
    client_secret: String,
) -> Result<TikTokToken, Error> {
    let params = [
        ("client_key", client_key),
        ("client_secret", client_secret),
        ("code", code),
        ("grant_type", "authorization_code"),
    ];
    
    let response = reqwest::Client::new()
        .post("https://open-api.tiktok.com/oauth/access_token/")
        .form(&params)
        .send()
        .await?;
    
    response.json::<TikTokToken>().await
}
```

## Video Upload Process

### 1. Initialize Upload
```rust
pub async fn initialize_upload(
    access_token: &str,
    video_size: u64,
) -> Result<InitUploadResponse, Error> {
    let body = json!({
        "upload_mode": "UPLOAD_MODE_CHUNK_INIT",
        "chunk_size": 10485760, // 10MB chunks
        "total_byte_count": video_size,
    });
    
    let response = client
        .post("https://open-api.tiktok.com/share/video/upload/")
        .header("Authorization", format!("Bearer {}", access_token))
        .json(&body)
        .send()
        .await?;
    
    response.json().await
}
```

### 2. Upload Video Chunks
```rust
pub async fn upload_chunk(
    access_token: &str,
    upload_id: &str,
    chunk_data: Vec<u8>,
    chunk_number: u32,
) -> Result<(), Error> {
    let response = client
        .post("https://open-api.tiktok.com/share/video/upload/")
        .header("Authorization", format!("Bearer {}", access_token))
        .multipart(
            reqwest::multipart::Form::new()
                .text("upload_mode", "UPLOAD_MODE_CHUNK_UPLOAD")
                .text("upload_id", upload_id)
                .text("chunk_number", chunk_number.to_string())
                .part("video", reqwest::multipart::Part::bytes(chunk_data))
        )
        .send()
        .await?;
    
    if !response.status().is_success() {
        return Err(Error::UploadFailed);
    }
    
    Ok(())
}
```

### 3. Finalize and Publish
```rust
pub async fn finalize_upload(
    access_token: &str,
    upload_id: &str,
    metadata: VideoMetadata,
) -> Result<String, Error> {
    let body = json!({
        "upload_mode": "UPLOAD_MODE_CHUNK_FINISH",
        "upload_id": upload_id,
        "post_info": {
            "title": metadata.title,
            "privacy_level": metadata.privacy_level,
            "disable_duet": false,
            "disable_comment": false,
            "disable_stitch": false,
            "video_cover_timestamp_ms": 1000,
        }
    });
    
    let response = client
        .post("https://open-api.tiktok.com/share/video/upload/")
        .header("Authorization", format!("Bearer {}", access_token))
        .json(&body)
        .send()
        .await?;
    
    let result: PublishResponse = response.json().await?;
    Ok(result.share_id)
}
```

## Video Optimization

### Recommended Settings
```rust
pub struct TikTokVideoPreset {
    pub resolution: (u32, u32),  // 1080x1920 (9:16)
    pub fps: u32,                // 30
    pub bitrate: u32,            // 8000 kbps
    pub codec: &'static str,     // "h264"
    pub audio_bitrate: u32,      // 128 kbps
    pub audio_codec: &'static str, // "aac"
}

impl Default for TikTokVideoPreset {
    fn default() -> Self {
        Self {
            resolution: (1080, 1920),
            fps: 30,
            bitrate: 8000,
            codec: "h264",
            audio_bitrate: 128,
            audio_codec: "aac",
        }
    }
}
```

### FFmpeg Optimization Command
```bash
ffmpeg -i input.mp4 \
  -vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2" \
  -c:v libx264 -preset slow -crf 23 \
  -c:a aac -b:a 128k \
  -movflags +faststart \
  output.mp4
```

## Error Handling

### Common Error Codes
- `10000` - Server error
- `10002` - Invalid parameters
- `10003` - Video not found
- `10004` - Access token invalid
- `10005` - Insufficient permissions
- `10007` - Rate limit exceeded
- `10013` - Video too short/long
- `10020` - Video processing failed

### Retry Strategy
```rust
pub async fn upload_with_retry(
    video_path: &str,
    metadata: VideoMetadata,
    max_retries: u32,
) -> Result<String, Error> {
    let mut retries = 0;
    let mut backoff = Duration::from_secs(1);
    
    loop {
        match upload_video(video_path, metadata.clone()).await {
            Ok(video_id) => return Ok(video_id),
            Err(e) if retries < max_retries => {
                if is_retryable_error(&e) {
                    tokio::time::sleep(backoff).await;
                    backoff *= 2;
                    retries += 1;
                } else {
                    return Err(e);
                }
            }
            Err(e) => return Err(e),
        }
    }
}
```

## Testing in Development

### Test Mode
TikTok provides a sandbox environment for testing:
- Use test credentials
- Videos are not publicly visible
- No rate limits
- Faster processing

### Mock Server
For local development without API access:
```rust
#[cfg(test)]
pub struct MockTikTokAPI {
    responses: HashMap<String, serde_json::Value>,
}

impl MockTikTokAPI {
    pub fn new() -> Self {
        let mut responses = HashMap::new();
        responses.insert(
            "upload_init".to_string(),
            json!({
                "upload_id": "mock_upload_123",
                "upload_url": "http://localhost:8080/mock/upload",
            })
        );
        Self { responses }
    }
}
```

## Implementation Checklist

- [ ] Register TikTok developer account
- [ ] Create app and obtain API credentials
- [ ] Implement OAuth 2.0 flow
- [ ] Build chunked upload system
- [ ] Add video validation and optimization
- [ ] Handle API errors and rate limits
- [ ] Test with sandbox environment
- [ ] Submit for production approval

## Important Notes

1. **Business Account Required**: Production access requires business verification
2. **Content Moderation**: Videos go through automatic content review
3. **Geographic Restrictions**: API availability varies by region
4. **Privacy Compliance**: Must handle user data according to TikTok's privacy policy
5. **Brand Guidelines**: Follow TikTok's branding requirements in UI

## Resources

- [TikTok for Developers](https://developers.tiktok.com/)
- [API Documentation](https://developers.tiktok.com/doc/web-video-kit-with-web)
- [OAuth Guide](https://developers.tiktok.com/doc/login-kit-web)
- [Video Upload API](https://developers.tiktok.com/doc/web-video-kit-video-upload)
- [Error Codes Reference](https://developers.tiktok.com/doc/common-error-codes)