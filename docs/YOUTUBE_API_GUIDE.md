# YouTube Data API v3 Integration Guide

## Overview
This guide covers the complete implementation of YouTube upload functionality using the YouTube Data API v3.

## Prerequisites

### 1. Google Cloud Project Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create OAuth 2.0 credentials

### 2. API Quotas
- **Default quota**: 10,000 units per day
- **Upload cost**: 1600 units per video
- **Max uploads per day**: ~6 videos (with default quota)

## Authentication Flow

### OAuth 2.0 Scopes Required
```
https://www.googleapis.com/auth/youtube.upload
https://www.googleapis.com/auth/youtube
https://www.googleapis.com/auth/youtubepartner
```

### Implementation Steps

#### 1. Environment Configuration
```rust
// src-tauri/src/config/youtube_config.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct YouTubeConfig {
    pub client_id: String,
    pub client_secret: String,
    pub redirect_uri: String,
    pub auth_uri: String,
    pub token_uri: String,
}

impl YouTubeConfig {
    pub fn from_env() -> Result<Self, String> {
        Ok(Self {
            client_id: std::env::var("YOUTUBE_CLIENT_ID")
                .map_err(|_| "YOUTUBE_CLIENT_ID not set")?,
            client_secret: std::env::var("YOUTUBE_CLIENT_SECRET")
                .map_err(|_| "YOUTUBE_CLIENT_SECRET not set")?,
            redirect_uri: "http://localhost:8080/auth/youtube".to_string(),
            auth_uri: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_uri: "https://oauth2.googleapis.com/token".to_string(),
        })
    }
}
```

#### 2. OAuth Flow Implementation
```rust
// src-tauri/src/auth/youtube_auth.rs
use oauth2::{
    AuthorizationCode, AuthUrl, ClientId, ClientSecret, CsrfToken,
    PkceCodeChallenge, RedirectUrl, Scope, TokenUrl,
};
use oauth2::basic::BasicClient;
use oauth2::reqwest::async_http_client;
use oauth2::{TokenResponse, RefreshToken};

pub struct YouTubeAuth {
    client: BasicClient,
    config: YouTubeConfig,
}

impl YouTubeAuth {
    pub fn new(config: YouTubeConfig) -> Result<Self, String> {
        let client = BasicClient::new(
            ClientId::new(config.client_id.clone()),
            Some(ClientSecret::new(config.client_secret.clone())),
            AuthUrl::new(config.auth_uri.clone())
                .map_err(|e| format!("Invalid auth URL: {}", e))?,
            Some(TokenUrl::new(config.token_uri.clone())
                .map_err(|e| format!("Invalid token URL: {}", e))?),
        )
        .set_redirect_uri(
            RedirectUrl::new(config.redirect_uri.clone())
                .map_err(|e| format!("Invalid redirect URL: {}", e))?,
        );

        Ok(Self { client, config })
    }

    pub fn get_auth_url(&self) -> (String, CsrfToken, PkceCodeVerifier) {
        let (pkce_code_challenge, pkce_code_verifier) = PkceCodeChallenge::new_random_sha256();
        
        let (authorize_url, csrf_state) = self
            .client
            .authorize_url(CsrfToken::new_random)
            .add_scope(Scope::new("https://www.googleapis.com/auth/youtube.upload".to_string()))
            .add_scope(Scope::new("https://www.googleapis.com/auth/youtube".to_string()))
            .set_pkce_challenge(pkce_code_challenge)
            .url();

        (authorize_url.to_string(), csrf_state, pkce_code_verifier)
    }

    pub async fn exchange_code(
        &self,
        code: String,
        verifier: PkceCodeVerifier,
    ) -> Result<YouTubeToken, String> {
        let token_response = self
            .client
            .exchange_code(AuthorizationCode::new(code))
            .set_pkce_verifier(verifier)
            .request_async(async_http_client)
            .await
            .map_err(|e| format!("Failed to exchange code: {}", e))?;

        Ok(YouTubeToken {
            access_token: token_response.access_token().secret().to_string(),
            refresh_token: token_response.refresh_token()
                .map(|t| t.secret().to_string()),
            expires_at: chrono::Utc::now() + chrono::Duration::seconds(
                token_response.expires_in()
                    .map(|d| d.as_secs() as i64)
                    .unwrap_or(3600)
            ),
        })
    }
}
```

## Upload Implementation

### 1. Video Upload Process
YouTube uses a resumable upload protocol for large files:

```rust
// src-tauri/src/upload/youtube_upload.rs
use reqwest::{Client, Body};
use tokio::fs::File;
use tokio_util::codec::{BytesCodec, FramedRead};

pub struct YouTubeUploader {
    client: Client,
    token: String,
}

impl YouTubeUploader {
    pub fn new(token: String) -> Self {
        Self {
            client: Client::new(),
            token,
        }
    }

    pub async fn upload_video(
        &self,
        file_path: &str,
        metadata: VideoMetadata,
        progress_callback: impl Fn(f64),
    ) -> Result<String, String> {
        // Step 1: Initialize resumable upload
        let upload_url = self.init_resumable_upload(&metadata).await?;
        
        // Step 2: Upload video data
        let video_id = self.upload_file(&upload_url, file_path, progress_callback).await?;
        
        // Step 3: Set thumbnail (optional)
        if let Some(thumbnail_path) = &metadata.thumbnail_path {
            self.upload_thumbnail(&video_id, thumbnail_path).await?;
        }
        
        Ok(video_id)
    }

    async fn init_resumable_upload(&self, metadata: &VideoMetadata) -> Result<String, String> {
        let url = "https://www.googleapis.com/upload/youtube/v3/videos?uploadType=resumable&part=snippet,status";
        
        let body = serde_json::json!({
            "snippet": {
                "title": metadata.title,
                "description": metadata.description,
                "tags": metadata.tags,
                "categoryId": metadata.category_id,
                "defaultLanguage": "en",
                "defaultAudioLanguage": "en"
            },
            "status": {
                "privacyStatus": metadata.privacy_status,
                "selfDeclaredMadeForKids": false,
                "notifySubscribers": metadata.notify_subscribers
            }
        });

        let response = self.client
            .post(url)
            .header("Authorization", format!("Bearer {}", self.token))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| format!("Failed to init upload: {}", e))?;

        if !response.status().is_success() {
            let error = response.text().await.unwrap_or_default();
            return Err(format!("Upload init failed: {}", error));
        }

        response.headers()
            .get("Location")
            .and_then(|h| h.to_str().ok())
            .map(|s| s.to_string())
            .ok_or_else(|| "No upload URL in response".to_string())
    }

    async fn upload_file(
        &self,
        upload_url: &str,
        file_path: &str,
        progress_callback: impl Fn(f64),
    ) -> Result<String, String> {
        let file = File::open(file_path).await
            .map_err(|e| format!("Failed to open file: {}", e))?;
        
        let metadata = file.metadata().await
            .map_err(|e| format!("Failed to get file metadata: {}", e))?;
        
        let file_size = metadata.len();
        
        // Upload in chunks for progress tracking
        const CHUNK_SIZE: u64 = 5 * 1024 * 1024; // 5MB chunks
        let mut uploaded = 0u64;
        
        while uploaded < file_size {
            let chunk_end = (uploaded + CHUNK_SIZE).min(file_size);
            
            // Read chunk
            let mut chunk = vec![0u8; (chunk_end - uploaded) as usize];
            file.read_exact(&mut chunk).await
                .map_err(|e| format!("Failed to read chunk: {}", e))?;
            
            // Upload chunk
            let response = self.client
                .put(upload_url)
                .header("Content-Range", format!("bytes {}-{}/{}", uploaded, chunk_end - 1, file_size))
                .body(chunk)
                .send()
                .await
                .map_err(|e| format!("Failed to upload chunk: {}", e))?;
            
            if response.status().is_success() || response.status().as_u16() == 308 {
                uploaded = chunk_end;
                progress_callback(uploaded as f64 / file_size as f64);
            } else {
                return Err(format!("Upload failed: {}", response.status()));
            }
        }
        
        // Parse video ID from response
        // Implementation details...
        Ok("VIDEO_ID".to_string())
    }
}
```

### 2. Video Metadata Structure
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoMetadata {
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub category_id: String,
    pub privacy_status: PrivacyStatus,
    pub thumbnail_path: Option<String>,
    pub notify_subscribers: bool,
    pub scheduled_time: Option<chrono::DateTime<chrono::Utc>>,
    pub playlist_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PrivacyStatus {
    Private,
    Unlisted,
    Public,
}
```

### 3. Category IDs
```rust
pub const YOUTUBE_CATEGORIES: &[(&str, &str)] = &[
    ("1", "Film & Animation"),
    ("2", "Autos & Vehicles"),
    ("10", "Music"),
    ("15", "Pets & Animals"),
    ("17", "Sports"),
    ("19", "Travel & Events"),
    ("20", "Gaming"),
    ("22", "People & Blogs"),
    ("23", "Comedy"),
    ("24", "Entertainment"),
    ("25", "News & Politics"),
    ("26", "Howto & Style"),
    ("27", "Education"),
    ("28", "Science & Technology"),
];
```

## Error Handling

### Common Errors and Solutions
1. **Quota Exceeded (403)**
   - Wait 24 hours or request quota increase
   - Implement exponential backoff

2. **Invalid Credentials (401)**
   - Refresh access token
   - Re-authenticate if refresh fails

3. **Network Errors**
   - Implement retry with exponential backoff
   - Use resumable upload for recovery

### Rate Limiting
```rust
use tokio::time::{sleep, Duration};

pub struct RateLimiter {
    last_request: tokio::sync::Mutex<chrono::DateTime<chrono::Utc>>,
    min_interval: Duration,
}

impl RateLimiter {
    pub fn new(requests_per_second: f64) -> Self {
        Self {
            last_request: tokio::sync::Mutex::new(chrono::Utc::now()),
            min_interval: Duration::from_secs_f64(1.0 / requests_per_second),
        }
    }

    pub async fn wait(&self) {
        let mut last = self.last_request.lock().await;
        let now = chrono::Utc::now();
        let elapsed = (now - *last).to_std().unwrap_or_default();
        
        if elapsed < self.min_interval {
            sleep(self.min_interval - elapsed).await;
        }
        
        *last = chrono::Utc::now();
    }
}
```

## Security Considerations

1. **Token Storage**
   - Use OS keychain for storing refresh tokens
   - Encrypt tokens at rest
   - Never log or expose tokens

2. **CORS and Redirect URIs**
   - Use exact redirect URI matching
   - Implement state parameter validation
   - Use PKCE for additional security

3. **Content Validation**
   - Verify file formats before upload
   - Scan for malicious content
   - Validate metadata lengths

## Testing

### Unit Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_auth_url_generation() {
        let config = YouTubeConfig {
            client_id: "test_id".to_string(),
            client_secret: "test_secret".to_string(),
            redirect_uri: "http://localhost:8080/auth/youtube".to_string(),
            auth_uri: "https://accounts.google.com/o/oauth2/v2/auth".to_string(),
            token_uri: "https://oauth2.googleapis.com/token".to_string(),
        };

        let auth = YouTubeAuth::new(config).unwrap();
        let (url, _, _) = auth.get_auth_url();
        
        assert!(url.contains("scope=https"));
        assert!(url.contains("response_type=code"));
    }
}
```

### Integration Tests
- Test with YouTube API test environment
- Verify upload progress tracking
- Test error recovery scenarios

## Production Checklist

- [ ] Store credentials securely (environment variables or secure vault)
- [ ] Implement proper error handling and retry logic
- [ ] Add comprehensive logging (without exposing sensitive data)
- [ ] Set up monitoring for API quota usage
- [ ] Implement user feedback for long uploads
- [ ] Add upload queue persistence
- [ ] Test with various video formats and sizes
- [ ] Implement proper cleanup for failed uploads
- [ ] Add analytics tracking for upload success rates

## Dependencies

Add to Cargo.toml:
```toml
[dependencies]
oauth2 = "4.4"
reqwest = { version = "0.11", features = ["json", "stream"] }
tokio = { version = "1", features = ["full"] }
tokio-util = { version = "0.7", features = ["codec"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
```

## Next Steps

1. Implement token refresh mechanism
2. Add playlist management
3. Implement video analytics retrieval
4. Add live streaming support
5. Implement comment moderation API