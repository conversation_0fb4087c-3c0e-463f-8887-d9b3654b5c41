# Step 4: Adapter Layer Implementation

## File 1: src/adapters/DownloadAdapter.ts

```typescript
import { Download as DomainDownload } from '../domain/models';
import { Download as LegacyDownload } from '../store/downloadStore';

export class DownloadAdapter {
  static toDomain(legacy: LegacyDownload): DomainDownload {
    // Convert legacy download to domain entity
    const fullPath = `${legacy.downloadPath}/${legacy.filename}`;
    const domain = DomainDownload.create(
      legacy.url,
      fullPath,
      legacy.quality
    );

    // Apply current state based on legacy status
    try {
      switch (legacy.status) {
        case 'downloading':
          domain.start();
          if (legacy.bytesDownloaded && legacy.bytesTotal) {
            domain.updateProgress(
              legacy.bytesDownloaded,
              legacy.bytesTotal,
              this.parseSpeed(legacy.downloadSpeed || '0 B/s')
            );
          }
          break;
        case 'completed':
          domain.start();
          domain.complete();
          break;
        case 'error':
          domain.fail(legacy.error || 'Unknown error');
          break;
        case 'paused':
          domain.start();
          domain.pause();
          break;
        // 'pending' is the default state, no action needed
      }
    } catch (error) {
      console.warn('Error applying legacy state to domain entity:', error);
      // If state transition fails, keep in pending state
    }

    return domain;
  }

  static toLegacy(domain: DomainDownload): LegacyDownload {
    const dto = domain.toDTO();
    return {
      id: dto.id,
      url: dto.url,
      filename: dto.filename,
      quality: dto.quality,
      downloadPath: dto.downloadPath,
      progress: dto.progress,
      status: dto.status as any, // Cast to legacy status type
      error: dto.error,
      createdAt: dto.createdAt,
      completedAt: dto.completedAt,
      downloadSpeed: dto.downloadSpeed,
      fileSize: this.formatFileSize(dto.bytesTotal),
      estimatedTimeRemaining: this.formatTimeRemaining(dto.estimatedTimeRemaining),
      bytesDownloaded: dto.bytesDownloaded,
      bytesTotal: dto.bytesTotal
    };
  }

  private static parseSpeed(speedString: string): number {
    // Parse speed string like "1.5 MB/s" back to bytes per second
    const match = speedString.match(/([0-9.]+)\s*(B|KB|MB|GB)\/s/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    switch (unit) {
      case 'GB': return value * 1024 * 1024 * 1024;
      case 'MB': return value * 1024 * 1024;
      case 'KB': return value * 1024;
      case 'B': return value;
      default: return 0;
    }
  }

  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }

  private static formatTimeRemaining(seconds: number | null): string {
    if (!seconds || seconds <= 0) return '';
    
    if (seconds < 60) {
      return `${Math.ceil(seconds)}s`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.ceil(seconds % 60);
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  }
}
```

## File 2: src/adapters/FeatureFlags.ts

```typescript
export interface FeatureFlags {
  useDomainLayer: boolean;
  useEnhancedRepository: boolean;
  enableBatchOperations: boolean;
  enableAnalytics: boolean;
  enableMigrationMode: boolean;
}

export const getFeatureFlags = (): FeatureFlags => {
  // Start with all flags off for safety
  const defaultFlags: FeatureFlags = {
    useDomainLayer: false,
    useEnhancedRepository: false,
    enableBatchOperations: false,
    enableAnalytics: false,
    enableMigrationMode: false
  };

  // Allow override via localStorage for development
  if (typeof window !== 'undefined') {
    return {
      useDomainLayer: localStorage.getItem('ff_domain_layer') === 'true',
      useEnhancedRepository: localStorage.getItem('ff_enhanced_repository') === 'true',
      enableBatchOperations: localStorage.getItem('ff_batch_operations') === 'true',
      enableAnalytics: localStorage.getItem('ff_analytics') === 'true',
      enableMigrationMode: localStorage.getItem('ff_migration_mode') === 'true'
    };
  }

  return defaultFlags;
};

export const setFeatureFlag = (flag: keyof FeatureFlags, enabled: boolean): void => {
  if (typeof window !== 'undefined') {
    const storageKey = `ff_${flag.replace(/([A-Z])/g, '_$1').toLowerCase()}`;
    localStorage.setItem(storageKey, enabled.toString());
  }
};
```

## File 3: src/adapters/MigrationSafeStore.ts

```typescript
import { create } from 'zustand';
import { DownloadAdapter } from './DownloadAdapter';
import { getFeatureFlags } from './FeatureFlags';
import { useDownloadStore as useLegacyStore } from '../store/downloadStore';
import { Download as DomainDownload } from '../domain/models';
import { createDownloadRepository } from '../infrastructure/repositories';
import { DownloadRepository } from '../domain/repositories';

interface MigrationState {
  migrationComplete: boolean;
  migrationErrors: string[];
  lastMigrationAttempt: Date | null;
}

interface MigrationStore extends MigrationState {
  // Actions
  startMigration: () => Promise<void>;
  rollbackMigration: () => void;
  clearMigrationErrors: () => void;
}

export const useMigrationStore = create<MigrationStore>((set, get) => ({
  migrationComplete: false,
  migrationErrors: [],
  lastMigrationAttempt: null,

  startMigration: async () => {
    set({ lastMigrationAttempt: new Date(), migrationErrors: [] });
    
    try {
      // Get legacy downloads
      const legacyStore = useLegacyStore.getState();
      const legacyDownloads = legacyStore.downloads;
      
      // Convert to domain entities
      const domainDownloads = legacyDownloads.map(DownloadAdapter.toDomain);
      
      // Save to new repository
      const repository = createDownloadRepository();
      await Promise.all(domainDownloads.map(download => repository.save(download)));
      
      set({ migrationComplete: true });
      console.log('Migration completed successfully');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown migration error';
      set(state => ({
        migrationErrors: [...state.migrationErrors, errorMessage]
      }));
      console.error('Migration failed:', error);
    }
  },

  rollbackMigration: () => {
    set({
      migrationComplete: false,
      migrationErrors: [],
      lastMigrationAttempt: null
    });
    
    // Clear domain repository storage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('flowdownload_downloads_v2');
    }
    
    console.log('Migration rolled back');
  },

  clearMigrationErrors: () => {
    set({ migrationErrors: [] });
  }
}));

// Unified store that switches between legacy and domain based on feature flags
export const useUnifiedDownloadStore = () => {
  const featureFlags = getFeatureFlags();
  const migrationState = useMigrationStore();
  const legacyStore = useLegacyStore();

  if (featureFlags.useDomainLayer && migrationState.migrationComplete) {
    // Use domain-driven implementation
    return {
      ...legacyStore,
      downloads: legacyStore.downloads.map(DownloadAdapter.toDomain),
      // Add migration-specific methods
      rollbackMigration: migrationState.rollbackMigration,
      migrationErrors: migrationState.migrationErrors
    };
  } else {
    // Use legacy implementation
    return {
      ...legacyStore,
      // Add migration helpers
      startMigration: migrationState.startMigration,
      migrationErrors: migrationState.migrationErrors,
      canMigrate: legacyStore.downloads.length > 0
    };
  }
};
```

## File 4: src/components/migration/MigrationErrorBoundary.tsx

```typescript
import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class MigrationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Migration Error Boundary caught an error:', error, errorInfo);
    
    // Call the optional error handler
    this.props.onError?.(error, errorInfo);
    
    // Could send to error reporting service
    this.reportError(error, errorInfo);
  }

  private reportError(error: Error, errorInfo: React.ErrorInfo) {
    // For now, just log to console
    // In production, this would send to error reporting service
    console.log('Migration Error Report:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    });
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-lg font-semibold text-red-800">
            Migration Error
          </h3>
          <p className="text-red-600 mt-2">
            There was an issue with the new domain layer. 
            The application has fallen back to the legacy implementation.
          </p>
          <details className="mt-3">
            <summary className="cursor-pointer text-red-700 font-medium">
              Error Details
            </summary>
            <pre className="mt-2 text-sm bg-red-100 p-2 rounded overflow-auto">
              {this.state.error?.message}
            </pre>
          </details>
          <button 
            onClick={() => window.location.reload()}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Reload Application
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## File 5: src/__tests__/migration/adapter.test.ts

```typescript
import { describe, it, expect } from 'vitest';
import { DownloadAdapter } from '../../adapters/DownloadAdapter';
import { Download as DomainDownload } from '../../domain/models';

describe('DownloadAdapter', () => {
  it('should convert legacy downloads to domain entities', () => {
    const legacyDownload = {
      id: 'test_123',
      url: 'https://example.com/video.mp4',
      filename: 'video.mp4',
      quality: '1080p',
      downloadPath: '/downloads',
      progress: 50,
      status: 'downloading' as const,
      createdAt: new Date(),
      bytesDownloaded: 50,
      bytesTotal: 100,
      downloadSpeed: '1.5 MB/s'
    };

    const domain = DownloadAdapter.toDomain(legacyDownload);
    
    expect(domain.getUrl().getValue()).toBe(legacyDownload.url);
    expect(domain.getProgress().getPercentage()).toBe(50);
    expect(domain.getStatus()).toBe('downloading');
  });

  it('should convert domain entities back to legacy format', () => {
    const domain = DomainDownload.create(
      'https://example.com/video.mp4',
      '/downloads/video.mp4',
      '1080p'
    );
    
    domain.start();
    domain.updateProgress(50, 100, 1572864); // 1.5 MB/s

    const legacy = DownloadAdapter.toLegacy(domain);
    
    expect(legacy.url).toBe('https://example.com/video.mp4');
    expect(legacy.progress).toBe(50);
    expect(legacy.status).toBe('downloading');
    expect(legacy.downloadSpeed).toBe('1.5 MB/s');
  });

  it('should maintain data integrity during round-trip conversion', () => {
    const originalLegacy = {
      id: 'test_123',
      url: 'https://example.com/video.mp4',
      filename: 'video.mp4',
      quality: '1080p',
      downloadPath: '/downloads',
      progress: 75,
      status: 'completed' as const,
      createdAt: new Date(),
      completedAt: new Date(),
      bytesDownloaded: 75,
      bytesTotal: 100
    };

    const domain = DownloadAdapter.toDomain(originalLegacy);
    const convertedLegacy = DownloadAdapter.toLegacy(domain);

    expect(convertedLegacy.url).toBe(originalLegacy.url);
    expect(convertedLegacy.status).toBe(originalLegacy.status);
    expect(convertedLegacy.progress).toBe(originalLegacy.progress);
  });
});
```

## 🧪 VALIDATION STEP

Run these commands to validate Step 4:

```bash
# Test the adapter layer
npm test src/__tests__/migration

# Test everything so far
npm run test:domain
npm test src/__tests__/infrastructure
npm test src/__tests__/migration

# Check TypeScript compilation
npx tsc --noEmit

# Ensure existing app still works
npm run dev
```

## 🔧 Enable Feature Flags (Optional)

To test the new domain layer, open your browser console and run:

```javascript
// Enable domain layer feature flag
localStorage.setItem('ff_domain_layer', 'true');
localStorage.setItem('ff_migration_mode', 'true');

// Reload to see the changes
window.location.reload();
```

To disable and rollback:

```javascript
// Disable feature flags
localStorage.setItem('ff_domain_layer', 'false');
localStorage.setItem('ff_migration_mode', 'false');

// Reload to rollback
window.location.reload();
```

✅ **Success Criteria**: All migration tests pass, feature flags work, existing app still works unchanged.

**Once this step is complete, proceed to Step 5 (Application Services).**
