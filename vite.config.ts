import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import { DEFAULT_CONFIG } from './src/config/constants';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  clearScreen: false,
  server: {
    port: DEFAULT_CONFIG.server.devPort,
    strictPort: DEFAULT_CONFIG.server.strictPort,
    watch: {
      ignored: ["**/src-tauri/**"],
    },
  },
  build: {
    outDir: 'dist',
  },
});