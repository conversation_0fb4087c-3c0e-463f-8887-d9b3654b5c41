# AI Setup Guide

## Overview
CreatorOS supports multiple AI tiers to match your needs and budget:

- **Free Tier**: Local processing using FFmpeg (no API keys needed!)
- **Basic Tier**: Affordable AI providers (Groq, DeepSeek, Qwen)
- **Premium Tier**: Best-in-class AI (OpenAI, Anthropic, Google)

## Quick Start (Free Tier)

1. **Install FFmpeg** (required for local processing):
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt install ffmpeg
   
   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

2. **Configure Environment**:
   ```bash
   # Copy the example file
   cp .env.example .env
   
   # Edit .env and set:
   VITE_DEFAULT_AI_TIER=free
   VITE_AI_PRIVACY_MODE=true
   ```

3. **Run the App**:
   ```bash
   npm run tauri dev
   ```

## Free Tier Features
With FFmpeg local processing, you get:
- ✅ Scene detection
- ✅ Audio level analysis
- ✅ Video metadata extraction
- ✅ Thumbnail generation
- ✅ Silence detection
- ❌ Transcription (upgrade to Basic)
- ❌ AI content analysis (upgrade to Basic)

## Upgrading to Basic/Premium

### Basic Tier ($5-10/month estimated)
Add any of these API keys to `.env`:

```env
# Groq (Free tier available!)
VITE_GROQ_API_KEY=your_key_here

# DeepSeek (Very affordable - $0.14/M tokens)
VITE_DEEPSEEK_API_KEY=your_key_here

# Qwen (Alibaba Cloud - Free tier: 1M tokens/month)
VITE_QWEN_API_KEY=your_key_here

# Set tier
VITE_DEFAULT_AI_TIER=basic
```

### Premium Tier (Usage-based)
```env
# OpenAI
VITE_OPENAI_API_KEY=your_key_here

# Anthropic Claude
VITE_ANTHROPIC_API_KEY=your_key_here

# Google Cloud
VITE_GOOGLE_CLOUD_API_KEY=your_key_here

# Set tier
VITE_DEFAULT_AI_TIER=premium
```

## Getting API Keys

### Free/Affordable Options:
1. **Groq**: https://console.groq.com/keys
   - 30 requests/minute free
   - Very fast inference

2. **DeepSeek**: https://platform.deepseek.com/
   - $0.14/million input tokens
   - Great for code and analysis

3. **Qwen**: https://dashscope.console.aliyun.com/
   - 1M tokens/month free
   - Excellent multilingual support

### Premium Options:
1. **OpenAI**: https://platform.openai.com/api-keys
2. **Anthropic**: https://console.anthropic.com/
3. **Google Cloud**: https://console.cloud.google.com/

## Privacy Mode
Set `VITE_AI_PRIVACY_MODE=true` to ensure all processing stays local, regardless of API keys.

## Troubleshooting

### "Analysis failed" error
1. Check FFmpeg is installed: `ffmpeg -version`
2. Verify file exists and is accessible
3. Check console for specific errors
4. Try with a smaller file first

### API errors
1. Verify API key is correct
2. Check API quota/credits
3. Try switching to a different provider
4. Fall back to free tier if needed

## Cost Estimates

| Feature | Free | Basic | Premium |
|---------|------|-------|---------|
| Scene Detection | ✅ FFmpeg | ✅ FFmpeg | ✅ Google Vision |
| Transcription | ❌ | ~$0.002/min | $0.006/min |
| Content Analysis | Basic | ~$0.001/video | $0.03/video |
| Virality Prediction | ❌ | ~$0.001/video | $0.02/video |

## Support
- Report issues: https://github.com/creatoros/desktop/issues
- Documentation: https://docs.creatoros.ai